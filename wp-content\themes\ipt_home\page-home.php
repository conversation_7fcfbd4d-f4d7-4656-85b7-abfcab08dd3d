<?php
/**
 * Template Name: Home page template
 *
 * @package ipt_home
 */

get_header();
?>
<main>
<!-- Hero banner -->
<?php if( have_rows('banner_section') ): ?> 
    <?php while( have_rows('banner_section') ): the_row(); 
        $link = get_sub_field('button');
        $image = get_sub_field('image');
        
    ?> 
<section
    class="flex gap-20 items-center px-[16px] py-[48px] md:p-[80px] md:pt-[160px] bg-slate-100 max-md:flex-col">
    <div class="flex flex-col flex-1 gap-[48px] nd:gap-[64px] items-start py-0 md:py-[32px]">
        <div class="flex flex-col gap-[24px] md:gap-[20px] items-start sm:items-start items-center text-center sm:text-left w-full">
            <h2
                class="text-56 max-sm:text-32 font-semibold">
                <?php echo get_sub_field('title');?>
            </h2>
            <p class="text-18">
                <?php echo get_sub_field('description');?>
            </p>
        </div>
        <button onclick="window.location.href='<?php echo get_started_button_url(); ?>'" class="align-middle mx-auto sm:mx-0 text-16 font-semibold bg-brand-main hover:bg-brand-main active:bg-brand-main rounded-[8px] w-[305px] h-[48px] md:h-[56px] text-primary-main z-10">
            <?php echo $link['title'];?>
        </button>
    </div>
    <div class="flex flex-col flex-1">
        <div class="w-full">
            <img 
                src="<?php echo !empty($image['url'])?$image['url']:'http://localhost/weavehome/wp-content/themes/ipt_home/assets/img/banner-image.png'?>" 
                alt="<?php echo $image['alt']?>" 
                class="w-full h-auto object-cover"
            />
        </div>   
    </div>
</section>
    <?php endwhile; ?>      
<?php endif; ?>
<!-- Why Weaveform? -->
<?php if( have_rows('why_weaveform') ): ?> 
    <?php while( have_rows('why_weaveform') ): the_row(); 
        $title = get_sub_field('title');
    ?> 
<section class="flex flex-col gap-[32px] md:gap-[64px] items-center px-[16px] py-[48px] md:p-[80px] bg-white">
    <h2 class="text-24 md:text-42 font-bold text-center">
        <?php echo $title;?>
    </h2>
    <div class="flex gap-[16px] w-full max-md:flex-col">
        <?php if( have_rows('content_details') ): ?> 
            <?php while( have_rows('content_details') ): the_row(); 
            $title = get_sub_field('title');
            $description = get_sub_field('description');  
            ?> 
        <article class="flex flex-col flex-1 gap-[16px] items-center px-0 md:px-[16px]">
            <?php if( have_rows('image_section') ): ?> 
                <?php while( have_rows('image_section') ): the_row(); 
                    $image = get_sub_field('image_why');
                ?>
                    <?php if( get_sub_field('options_why') == 'logo' ) :?>
                        <?php echo get_sub_field('logo_why');?>
                    <?php elseif( get_sub_field('options_why') == 'image' ):?>
                        <img 
                            src="<?php echo !empty($image['url'])?$image['url']:''?>" 
                            alt="<?php echo $image['alt']?>" 
                            class="w-full h-auto object-cover"
                        />
                    <?php endif;?>
                <?php endwhile;?>
            <?php endif;?>
            <h3 class="text-18 font-bold text-center text-secondary-main tracking-normal">
                <?php echo $title;?>
            </h3>
            <p class="text-16 md:text-18 text-center text-neutral-light">
                <?php echo $description;?>
            </p>
        </article>
            <?php endwhile; ?>      
        <?php endif; ?>
    </div>
</section>
    <?php endwhile; ?>      
<?php endif; ?>
<!-- Templates -->
<?php if( have_rows('create_web_section') ): ?> 
    <?php while( have_rows('create_web_section') ): the_row(); 
    $title = get_sub_field('title');
    $link = get_sub_field('button');
    ?> 
<section
    class="flex flex-col items-center gap-[64px] bg-third-main px-[16px] py-[48px] md:p-[80px] ">
    <h1 class="text-secondary-main text-center text-42 max-sm:text-24 font-bold">
        <?php echo $title;?>
    </h1>

    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 w-full gap-[20px]" id="home-template-list">
       
    </div>
    <script>
        jQuery(document).ready(function($) {
            // GraphQL query
            const filters = ["status_id:=(2)","is_kit:=(0)"];
            const query = `
                query Template_list($filters: [String!]) {
                    template_list(body: { filters: $filters, limit: 12 }) {
                    totalCount
                        totalPages
                        currentPage
                        data {
                            id
                            name
                            desc
                            image_id
                            image {
                                id
                                file_name
                                file_url
                            }
                            industries {
                                name
                            }
                            info
                        } 
                    }
                }
            `;

            $.ajax({
                url: iptHomeAjax.ajax_url,
                type: 'POST',
                dataType: 'json',
                data: {
                    action: 'ipt_home_graphql',
                    query: query,
                    variables: JSON.stringify({ filters: filters})
                },
                success: function(response) {
                    if(response.data && response.data.template_list.data) {
                        renderTemplates(response.data.template_list.data);
                    } else {
                        // No data
                    }
                },
                error: function(xhr, status, error) {
                }
            });

            // Hàm render ra HTML
            function renderTemplates(list) {
                let html = '';
                list.forEach(function(item) {
                    html += `
                        <article class="flex flex-col border bg-white rounded-[8px] overflow-hidden border-solid border-fifth-main">
                            <div class="relative w-full h-[200px]">
                                <img src="${item.image ? item.image.file_url : 'https://cdn.builder.io/api/v1/image/assets/TEMP/11ce7b98d60f888e40ee582f5d48055fe0e8fdca'}"
                                    alt="${item.name}" class="w-full h-full object-cover" />
                            </div>
                            <div class="flex flex-col items-start p-[16px] gap-[4px] self-stretch">
                                <div class="text-secondary-main text-16 font-normal">
                                    ${item.industries[0] ? item.industries[0].name : ''}
                                </div>
                                <h3 class="text-secondary-main text-18 font-bold">
                                    ${item.name}
                                </h3>
                            </div>
                            <div class="flex items-start p-[16px] self-stretch p-4">
                                <button  onclick="window.open('${item.info.domain ? (item.info.domain.startsWith('http') ? item.info.domain : 'https://' + item.info.domain) : ''}', '_blank')"
                                    class="border-[1px] border-solid !border-fourth-main w-full text-neutral-strong font-semibold cursor-pointer bg-transparent hover:!bg-transparent py-[12px] rounded-[8px] text-16">
                                    Preview
                                </button>
                            </div>
                        </article>
                    `;
                });
                $('#home-template-list').html(html);
            }
        });
    </script>
    <!-- <button
        class="w-[305px] h-14 gap-2 text-[rgba(0,0,0,0.91)] text-base font-bold leading-6 tracking-[-0.16px] bg-[#48C9B0] px-6 py-4 rounded-lg max-sm:w-full hover:bg-[#3ab19a] transition-colors">
        Get start
    </button> -->
    <button onclick="window.location.href='<?php echo get_started_button_url(); ?>'"
            class="align-middle mx-auto sm:mx-0 text-16 font-semibold bg-brand-main hover:bg-brand-main rounded-[8px] w-[305px] h-[48px] md:h-[56px] text-primary-main">
            <?php echo $link['title'];?>
    </button>
</section>
    <?php endwhile;?>
<?php endif;?>

<!-- Functions -->
<?php if( have_rows('functionality_and_performance_section') ): ?> 
    <?php while( have_rows('functionality_and_performance_section') ): the_row(); 
        $title = get_sub_field('title');
    ?>
<section class="items-stretch self-stretch bg-white flex flex-col px-[16px] py-[48px] gap-[32px] md:p-[80px] md:gap-[64px]">
    <h1 class="w-full text-42 max-sm:text-24 text-secondary-main font-bold text-center leading-[1.1] max-md:max-w-full">
        <?php echo $title;?>
    </h1>

    <!-- First row of features -->
    <?php if( have_rows('content_details') ): ?> 
    <div
        class="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 w-full gap-[16px] md:gap-[28px] text-xl text-secondary-main font-normal leading-7 max-md:max-w-full">
        <!-- Feature 1 -->
        <?php while( have_rows('content_details') ): the_row(); 
            $title = get_sub_field('title');
        ?>
        <article class="flex items-center gap-[16px] justify-center">
            <?php if( have_rows('image_section') ): ?> 
                <?php while( have_rows('image_section') ): the_row(); 
                    $image = get_sub_field('image');
                    $logo = get_sub_field('logo');
                ?>
                    <?php if( get_sub_field('options') == 'image' ) :?>
                        <img src="<?php echo !empty($image['url'])?$image['url']:''?>"
                            alt="<?php echo $image['alt']?>" class="aspect-[1] object-contain w-[32px] md:w-64px self-stretch shrink-0 my-auto" aria-hidden="true" />
                    <?php elseif( get_sub_field('options') == 'logo' ) :?>
                        <?php echo $logo;?>
                    <?php endif;?>
                <?php endwhile;?>
            <?php endif;?>
            <p class="my-auto text-16 md:text-20 text-secondary-main font-normal">
                <?php echo $title;?>
            </p>
        </article>
        <?php endwhile;?>
    </div>
    <?php endif;?>
    <!-- CTA Button -->
    <div class="flex justify-center">
        <!-- <button
            class="min-h-14 w-[305px] max-w-full text-base font-semibold tracking-[-0.16px] bg-[rgba(72,201,176,1)] hover:bg-[rgba(62,181,156,1)] text-black px-6 py-4 rounded-md">
            Get start
        </button> -->
        <button onclick="window.location.href='<?php echo get_started_button_url(); ?>'"
            class="align-middle mx-auto sm:mx-0 text-16 font-semibold bg-brand-main hover:bg-brand-main rounded-[8px] w-[305px] h-[48px] md:h-[56px] text-primary-main">
            Get started
        </button>
    </div>
</section>
    <?php endwhile;?>
<?php endif;?>
</main><!-- #page -->
<?php
get_footer(); 