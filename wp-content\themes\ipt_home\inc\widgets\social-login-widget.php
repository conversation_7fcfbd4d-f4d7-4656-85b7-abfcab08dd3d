<?php
/**
 * Social Login Widget for Firebase Authentication
 */

class Social_Login_Widget extends WP_Widget {

    public function __construct() {
        parent::__construct(
            'social_login_widget',
            __('Social Login (Firebase)', 'ipt-home'),
            array(
                'description' => __('Social login buttons for Google and Facebook via Firebase', 'ipt-home'),
            )
        );
    }

    public function widget($args, $instance) {
        $title = !empty($instance['title']) ? $instance['title'] : '';
        $show_google = !empty($instance['show_google']) ? $instance['show_google'] : true;
        $show_facebook = !empty($instance['show_facebook']) ? $instance['show_facebook'] : true;

        echo $args['before_widget'];

        if (!empty($title)) {
            echo $args['before_title'] . apply_filters('widget_title', $title) . $args['after_title'];
        }

        // Check if user is already logged in
        if (is_user_logged_in()) {
            $current_user = wp_get_current_user();
            ?>
            <div class="social-login-widget logged-in">
                <p><?php printf(__('Welcome, %s!', 'ipt-home'), $current_user->display_name); ?></p>
                <button onclick="firebaseAuth.signOut()" class="btn btn-logout">
                    <?php _e('Sign Out', 'ipt-home'); ?>
                </button>
            </div>
            <?php
        } else {
            ?>
            <div class="social-login-widget">
                <?php if ($show_google): ?>
                    <button onclick="firebaseAuth.signInWithGoogle()" class="btn btn-google">
                        <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24">
                            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                        </svg>
                        <?php _e('Continue with Google', 'ipt-home'); ?>
                    </button>
                <?php endif; ?>

                <?php if ($show_facebook): ?>
                    <button onclick="firebaseAuth.signInWithFacebook()" class="btn btn-facebook">
                        <svg class="w-5 h-5 mr-2" fill="#1877F2" viewBox="0 0 24 24">
                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                        </svg>
                        <?php _e('Continue with Facebook', 'ipt-home'); ?>
                    </button>
                <?php endif; ?>
            </div>
            <?php
        }

        echo $args['after_widget'];
    }

    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : '';
        $show_google = !empty($instance['show_google']) ? $instance['show_google'] : true;
        $show_facebook = !empty($instance['show_facebook']) ? $instance['show_facebook'] : true;
        ?>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('title')); ?>"><?php _e('Title:', 'ipt-home'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('title')); ?>" name="<?php echo esc_attr($this->get_field_name('title')); ?>" type="text" value="<?php echo esc_attr($title); ?>">
        </p>
        <p>
            <input class="checkbox" type="checkbox" <?php checked($show_google); ?> id="<?php echo esc_attr($this->get_field_id('show_google')); ?>" name="<?php echo esc_attr($this->get_field_name('show_google')); ?>" />
            <label for="<?php echo esc_attr($this->get_field_id('show_google')); ?>"><?php _e('Show Google Login', 'ipt-home'); ?></label>
        </p>
        <p>
            <input class="checkbox" type="checkbox" <?php checked($show_facebook); ?> id="<?php echo esc_attr($this->get_field_id('show_facebook')); ?>" name="<?php echo esc_attr($this->get_field_name('show_facebook')); ?>" />
            <label for="<?php echo esc_attr($this->get_field_id('show_facebook')); ?>"><?php _e('Show Facebook Login', 'ipt-home'); ?></label>
        </p>
        <?php
    }

    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['title'] = (!empty($new_instance['title'])) ? sanitize_text_field($new_instance['title']) : '';
        $instance['show_google'] = (!empty($new_instance['show_google'])) ? 1 : 0;
        $instance['show_facebook'] = (!empty($new_instance['show_facebook'])) ? 1 : 0;
        return $instance;
    }
}

// Register the widget
function register_social_login_widget() {
    register_widget('Social_Login_Widget');
}
add_action('widgets_init', 'register_social_login_widget');
