<?php

require_once get_stylesheet_directory() . '/inc/constants.php';

// Load Astra hooks
require_once get_stylesheet_directory() . '/inc/hooks/astra-hooks.php';

// Load GraphQL functions
require_once get_stylesheet_directory() . '/inc/graphql/graphql-functions.php';

// Load Firebase authentication
require_once get_stylesheet_directory() . '/inc/auth/firebase-auth.php';

// Load Social Login Widget
require_once get_stylesheet_directory() . '/inc/widgets/social-login-widget.php';

// Load customer dashboard
require_once get_stylesheet_directory() . '/inc/customer.php';

// Load database functions
require_once get_stylesheet_directory() . '/inc/database.php';

// Load domain pricing system
require_once get_stylesheet_directory() . '/includes/domain-pricing-system.php';

// Domain pricing admin menu is now handled in the pricing system file

// Domain Management AJAX Handlers
add_action('wp_ajax_save_domain_selection', 'handle_save_domain_selection');
add_action('wp_ajax_get_user_domains', 'handle_get_user_domains');
add_action('wp_ajax_update_domain_status', 'handle_update_domain_status');
add_action('wp_ajax_remove_domain', 'handle_remove_domain');

function handle_save_domain_selection() {
    error_log('💾 BACKEND STEP 6: Domain Selection Save Handler Started');

    // Ensure domain tables exist
    ipt_ensure_domain_tables_exist();

    // Verify nonce
    if (!wp_verify_nonce($_POST['security'], 'save_domain_selection_nonce')) {
        error_log('❌ Security check failed for domain selection');
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    // Check if user is logged in
    if (!is_user_logged_in()) {
        error_log('❌ User not logged in for domain selection');
        wp_send_json_error(['message' => 'User not logged in']);
        return;
    }

    $user_id = get_current_user_id();
    $domain_name = sanitize_text_field($_POST['domain']);
    $price = sanitize_text_field($_POST['price']);
    $is_premium = isset($_POST['is_premium']) && $_POST['is_premium'] === 'true';

    error_log('📝 Domain Selection Data: ' . json_encode([
        'user_id' => $user_id,
        'domain' => $domain_name,
        'price' => $price,
        'is_premium' => $is_premium
    ]));

    // Validate input
    if (empty($domain_name) || empty($price)) {
        error_log('❌ Validation failed: missing domain or price');
        wp_send_json_error(['message' => 'Domain name and price are required']);
        return;
    }

    // Save to database
    error_log('💾 Saving domain selection to database...');
    $domain_id = ipt_save_domain_selection($user_id, $domain_name, $price, $is_premium);

    if ($domain_id) {
        error_log('✅ Domain selection saved successfully with ID: ' . $domain_id);
        wp_send_json_success([
            'message' => 'Domain selection saved successfully',
            'domain_id' => $domain_id
        ]);
    } else {
        error_log('❌ Failed to save domain selection to database');
        wp_send_json_error(['message' => 'Failed to save domain selection']);
    }
}

function handle_get_user_domains() {
    // Ensure domain tables exist
    ipt_ensure_domain_tables_exist();

    // Verify nonce
    if (!wp_verify_nonce($_POST['security'], 'get_user_domains_nonce')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    // Check if user is logged in
    if (!is_user_logged_in()) {
        wp_send_json_error(['message' => 'User not logged in']);
        return;
    }

    $user_id = get_current_user_id();
    $domains = ipt_get_user_domains($user_id);

    wp_send_json_success($domains);
}

function handle_update_domain_status() {
    // Ensure domain tables exist
    ipt_ensure_domain_tables_exist();

    // Verify nonce
    if (!wp_verify_nonce($_POST['security'], 'update_domain_status_nonce')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    // Check if user is logged in
    if (!is_user_logged_in()) {
        wp_send_json_error(['message' => 'User not logged in']);
        return;
    }

    $domain_id = intval($_POST['domain_id']);
    $status = sanitize_text_field($_POST['status']);
    $payment_intent_id = isset($_POST['payment_intent_id']) ? sanitize_text_field($_POST['payment_intent_id']) : null;

    // Validate input
    if (empty($domain_id) || empty($status)) {
        wp_send_json_error(['message' => 'Domain ID and status are required']);
        return;
    }

    // Validate status
    $valid_statuses = ['selected', 'pending', 'failed', 'completed'];
    if (!in_array($status, $valid_statuses)) {
        wp_send_json_error(['message' => 'Invalid status']);
        return;
    }

    // Update status
    $result = ipt_update_domain_status($domain_id, $status, $payment_intent_id);

    if ($result) {
        wp_send_json_success(['message' => 'Domain status updated successfully']);
    } else {
        wp_send_json_error(['message' => 'Failed to update domain status']);
    }
}

function handle_remove_domain() {
    // Ensure domain tables exist
    ipt_ensure_domain_tables_exist();

    // Verify nonce
    if (!wp_verify_nonce($_POST['security'], 'remove_domain_nonce')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    // Check if user is logged in
    if (!is_user_logged_in()) {
        wp_send_json_error(['message' => 'User not logged in']);
        return;
    }

    $user_id = get_current_user_id();
    $domain_id = intval($_POST['domain_id']);

    // Validate input
    if (empty($domain_id)) {
        wp_send_json_error(['message' => 'Domain ID is required']);
        return;
    }

    // Remove domain
    $result = ipt_remove_domain($domain_id, $user_id);

    if ($result) {
        wp_send_json_success(['message' => 'Domain removed successfully']);
    } else {
        wp_send_json_error(['message' => 'Failed to remove domain']);
    }
}

// PayNow Payment AJAX Handlers
add_action('wp_ajax_create_paynow_payment', 'handle_create_paynow_payment');
add_action('wp_ajax_nopriv_create_paynow_payment', 'handle_create_paynow_payment');
add_action('wp_ajax_check_payment_status', 'handle_check_payment_status');
add_action('wp_ajax_nopriv_check_payment_status', 'handle_check_payment_status');

// Domain Payment AJAX Handlers
add_action('wp_ajax_create_domain_payment_intent', 'handle_create_domain_payment_intent');
add_action('wp_ajax_nopriv_create_domain_payment_intent', 'handle_create_domain_payment_intent');
add_action('wp_ajax_create_domain_paynow_payment', 'handle_create_domain_paynow_payment');
add_action('wp_ajax_nopriv_create_domain_paynow_payment', 'handle_create_domain_paynow_payment');
add_action('wp_ajax_check_domain_payment_status', 'handle_check_domain_payment_status');
add_action('wp_ajax_nopriv_check_domain_payment_status', 'handle_check_domain_payment_status');
add_action('wp_ajax_store_domain_registration_data', 'handle_store_domain_registration_data');
add_action('wp_ajax_nopriv_store_domain_registration_data', 'handle_store_domain_registration_data');
add_action('wp_ajax_create_domain_product', 'handle_create_domain_product');

// Custom Domain AJAX Handler
add_action('wp_ajax_save_custom_domain', 'handle_save_custom_domain');

function handle_create_domain_payment_intent() {
    error_log('💳 BACKEND STEP 8: Payment Intent Creation Started');

    // Verify nonce
    if (!wp_verify_nonce($_POST['security'], 'domain-payment-nonce')) {
        error_log('❌ Security check failed for payment intent');
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    // Get domain and amount from POST data
    $domain = sanitize_text_field($_POST['domain']);
    $amount = intval($_POST['amount']);

    error_log('📝 Payment Intent Request: ' . json_encode([
        'domain' => $domain,
        'amount' => $amount,
        'user_id' => get_current_user_id()
    ]));

    if (empty($domain) || $amount <= 0) {
        error_log('❌ Invalid domain or amount: domain=' . $domain . ', amount=' . $amount);
        wp_send_json_error(['message' => 'Invalid domain or amount']);
        return;
    }

    try {
        error_log('🔧 Setting up Stripe API...');
        // Load WC_Stripe_API if not already loaded
        if (!class_exists('WC_Stripe_API')) {
            $stripe_api_path = WP_PLUGIN_DIR . '/woocommerce-gateway-stripe/includes/class-wc-stripe-api.php';
            if (file_exists($stripe_api_path)) {
                require_once($stripe_api_path);
            } else {
                throw new Exception('WooCommerce Stripe Gateway API not found.');
            }
        }

        // Get Stripe settings
        $stripe_settings = get_option('woocommerce_stripe_settings');
        $test_mode = isset($stripe_settings['testmode']) && 'yes' === $stripe_settings['testmode'];
        $secret_key = $test_mode ?
            (isset($stripe_settings['test_secret_key']) ? $stripe_settings['test_secret_key'] : '') :
            (isset($stripe_settings['secret_key']) ? $stripe_settings['secret_key'] : '');

        if (empty($secret_key)) {
            throw new Exception('Stripe secret key not configured');
        }

        $current_user = wp_get_current_user();
        $user_email = $current_user->user_email;

        // Create payment intent for domain purchase using direct Stripe API (not WooCommerce)
        $payment_intent_data = [
            'amount' => $amount,
            'currency' => 'usd', // Domain prices are typically in USD
            'payment_method_types' => ['card'],
            'metadata' => [
                'user_id' => get_current_user_id(),
                'user_email' => $user_email,
                'payment_type' => 'domain_purchase',
                'domain' => $domain
            ],
            'description' => "Domain purchase: {$domain}"
        ];

        // Use direct Stripe API call instead of WooCommerce
        $response = wp_remote_post('https://api.stripe.com/v1/payment_intents', [
            'headers' => [
                'Authorization' => 'Bearer ' . $secret_key,
                'Content-Type' => 'application/x-www-form-urlencoded',
            ],
            'body' => http_build_query($payment_intent_data),
            'timeout' => 30
        ]);

        if (is_wp_error($response)) {
            throw new Exception('Failed to create payment intent: ' . $response->get_error_message());
        }

        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code !== 200) {
            $error_body = wp_remote_retrieve_body($response);
            throw new Exception('Stripe API error: ' . $error_body);
        }

        $payment_intent = json_decode(wp_remote_retrieve_body($response));

        if (is_wp_error($payment_intent)) {
            throw new Exception('Failed to create payment intent: ' . $payment_intent->get_error_message());
        }

        // Update the domain record with the payment intent ID and set status to pending
        global $wpdb;
        $table_name = ipt_get_domain_purchases_table();
        $user_id = get_current_user_id();

        // Find the domain record for this user and domain
        $domain_record = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE user_id = %d AND domain_name = %s AND status = 'selected' ORDER BY created_at DESC LIMIT 1",
            $user_id,
            $domain
        ), ARRAY_A);

        if ($domain_record) {
            // Update with payment intent ID and set status to pending
            $update_result = ipt_update_domain_status($domain_record['id'], 'pending', $payment_intent->id);
            if ($update_result) {
                error_log('Domain status updated to pending with payment intent ID: ' . $payment_intent->id);
            } else {
                error_log('Failed to update domain status to pending for domain: ' . $domain);
            }
        } else {
            error_log('Domain record not found for user: ' . $user_id . ', domain: ' . $domain);
        }

        wp_send_json_success([
            'client_secret' => $payment_intent->client_secret,
            'payment_intent_id' => $payment_intent->id
        ]);

    } catch (Exception $e) {
        error_log('Domain Payment Intent Error: ' . $e->getMessage());
        wp_send_json_error(['message' => $e->getMessage()]);
    }

    wp_die();
}

function handle_store_domain_registration_data() {
    error_log('💾 BACKEND STEP 9D: Store Registration Data Handler Started');

    // Verify nonce
    if (!wp_verify_nonce($_POST['security'], 'store_domain_registration_data_nonce')) {
        error_log('❌ Security check failed for store registration data');
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    // Check if user is logged in
    if (!is_user_logged_in()) {
        error_log('❌ User not logged in for store registration data');
        wp_send_json_error(['message' => 'User not logged in']);
        return;
    }

    // Don't use sanitize_text_field() on JSON data as it corrupts it
    $registration_data = wp_unslash($_POST['registration_data']);
    error_log('📝 Raw Registration Data Received: ' . $registration_data);

    if (empty($registration_data)) {
        error_log('❌ Registration data is empty');
        wp_send_json_error(['message' => 'Registration data is required']);
        return;
    }

    // Validate JSON before storing
    $decoded_data = json_decode($registration_data, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log('❌ JSON decode error: ' . json_last_error_msg());
        error_log('🔍 Invalid JSON data: ' . $registration_data);
        wp_send_json_error(['message' => 'Invalid JSON data: ' . json_last_error_msg()]);
        return;
    }

    error_log('✅ JSON validation successful');
    error_log('📝 Decoded Registration Data: ' . json_encode($decoded_data));

    // Start session if not already started
    if (!session_id()) {
        session_start();
        error_log('🔄 Session started with ID: ' . session_id());
    } else {
        error_log('✅ Session already active with ID: ' . session_id());
    }

    // Store registration data in session
    $_SESSION['domain_registration_data'] = $decoded_data;
    error_log('✅ Registration data stored in session');
    error_log('🔍 Session contents after storage: ' . json_encode($_SESSION));

    wp_send_json_success(['message' => 'Registration data stored successfully']);
    wp_die();
}

/**
 * Create or update dynamic domain product
 * This creates a single "Purchase Domain" product that changes price based on selected domain
 */
function create_or_update_domain_product($domain_name, $price) {
    // Check if domain product already exists
    $existing_products = get_posts([
        'post_type' => 'product',
        'meta_query' => [
            [
                'key' => '_domain_product',
                'value' => 'yes',
                'compare' => '='
            ]
        ],
        'posts_per_page' => 1
    ]);

    if (!empty($existing_products)) {
        // Update existing product
        $product_id = $existing_products[0]->ID;
        $product = wc_get_product($product_id);
    } else {
        // Create new product
        $product = new WC_Product_Simple();
        $product->set_name('Purchase Domain');
        $product->set_description('Domain registration service');
        $product->set_short_description('Register your domain name');
        $product->set_status('publish');
        $product->set_catalog_visibility('hidden'); // Hide from catalog
        $product->set_virtual(true); // Virtual product

        // Mark as domain product
        $product->update_meta_data('_domain_product', 'yes');

        $product_id = $product->save();
    }

    // Update product details for current domain
    $product->set_name("Purchase Domain: {$domain_name}");
    $product->set_regular_price($price);
    $product->set_price($price);

    // Store current domain info
    $product->update_meta_data('_current_domain', $domain_name);
    $product->update_meta_data('_current_price', $price);
    $product->update_meta_data('_last_updated', current_time('mysql'));

    $product->save();

    return $product_id;
}

/**
 * AJAX handler to create domain product when domain is selected
 */
function handle_create_domain_product() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['security'], 'domain_product_nonce')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    // Check if user is logged in
    if (!is_user_logged_in()) {
        wp_send_json_error(['message' => 'User not logged in']);
        return;
    }

    $domain = sanitize_text_field($_POST['domain']);
    $price = floatval($_POST['price']);

    if (empty($domain) || $price <= 0) {
        wp_send_json_error(['message' => 'Invalid domain or price']);
        return;
    }

    try {
        $product_id = create_or_update_domain_product($domain, $price);

        wp_send_json_success([
            'message' => 'Domain product created/updated successfully',
            'product_id' => $product_id,
            'domain' => $domain,
            'price' => $price
        ]);
    } catch (Exception $e) {
        wp_send_json_error(['message' => 'Failed to create domain product: ' . $e->getMessage()]);
    }

    wp_die();
}

/**
 * Call Namecheap domain registration API
 * This function would integrate with your existing GraphQL API
 */
function call_namecheap_domain_registration_api($registration_data) {
    error_log('🌐 BACKEND STEP 11: Namecheap Domain Registration API Started');
    error_log('📝 Registration Data Received: ' . json_encode($registration_data));

    // This is where you would make the actual API call to register the domain
    // Based on your buy_new_domain_namecheap.md file, it should be a GraphQL mutation

    $graphql_endpoint = 'https://api-weaveform.ip-tribe.com/graphql';
    error_log('📡 GraphQL Endpoint: ' . $graphql_endpoint);

    // Extract domain name from registration data or get from URL parameter
    $domain_name = isset($registration_data['domainName']) ? $registration_data['domainName'] :
                   (isset($_GET['domain']) ? sanitize_text_field($_GET['domain']) : '');

    // Get websiteId - you might want to make this dynamic based on user or context
    $website_id = isset($registration_data['websiteId']) ? $registration_data['websiteId'] : "24";

    error_log('📝 Domain Name: ' . $domain_name);
    error_log('📝 Website ID: ' . $website_id);

    // Validate required fields
    error_log('✅ Validating required fields...');
    $required_fields = ['FirstName', 'LastName', 'Address1', 'City', 'Province', 'PostalCode', 'Country', 'Phone', 'EmailAddress'];
    $missing_fields = [];

    foreach ($required_fields as $field) {
        if (empty($registration_data[$field])) {
            $missing_fields[] = $field;
            error_log("❌ Missing required field: $field");
        } else {
            error_log("✅ Field $field: " . $registration_data[$field]);
        }
    }

    if (!empty($missing_fields)) {
        error_log("❌ Missing required fields: " . implode(', ', $missing_fields));
        return new WP_Error('missing_field', "Missing required fields: " . implode(', ', $missing_fields));
    }

    if (empty($domain_name)) {
        error_log("❌ Missing domain name for registration");
        return new WP_Error('missing_domain', "Domain name is required");
    }

    error_log('✅ All required fields validated successfully');

    error_log('🔧 BACKEND STEP 11B: Building GraphQL Mutation');

    // Build the GraphQL mutation exactly as shown in buy_new_domain_namecheap.md
    $mutation = sprintf('
        mutation Webhooks_custom_domain_process {
            webhooks_custom_domain_process(
                body: {
                    domainInfo: {
                        command: "namecheap.domains.create"
                        domainName: "%s"
                        years: 1
                        addFreeWhoisguard: true
                        wgEnabled: true
                        FirstName: "%s"
                        LastName: "%s"
                        Address1: "%s"
                        City: "%s"
                        Province: "%s"
                        PostalCode: "%s"
                        Country: "%s"
                        Phone: "%s"
                        EmailAddress: "%s"
                    }
                    websiteId: "%s"
                }
            ) {
                ip
                isSuccess
            }
        }
    ',
        addslashes($domain_name),
        addslashes($registration_data['FirstName']),
        addslashes($registration_data['LastName']),
        addslashes($registration_data['Address1']),
        addslashes($registration_data['City']),
        addslashes($registration_data['Province']),
        addslashes($registration_data['PostalCode']),
        addslashes($registration_data['Country']),
        addslashes($registration_data['Phone']),
        addslashes($registration_data['EmailAddress']),
        addslashes($website_id)
    );

    $payload = [
        'query' => $mutation
    ];

    error_log('✅ GraphQL Mutation Built Successfully');
    error_log('📤 BACKEND STEP 11C: Sending GraphQL Request to Namecheap');
    error_log('🔍 GraphQL Query: ' . $mutation);
    error_log('📦 Payload: ' . json_encode($payload));

    error_log('🔐 BACKEND STEP 11C1: Adding Authorization Header');

    // Get the API key for authentication
    $api_key = defined('IPT_API_KEY') ? IPT_API_KEY : '';
    if (empty($api_key)) {
        error_log('❌ IPT_API_KEY is not defined or empty');
        return new WP_Error('missing_api_key', 'API key is required for domain registration');
    }

    error_log('✅ API key found, length: ' . strlen($api_key));
    error_log('⏳ Making HTTP POST request to GraphQL endpoint...');

    $response = wp_remote_post($graphql_endpoint, [
        'headers' => [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $api_key,
        ],
        'body' => json_encode($payload),
        'timeout' => 30
    ]);

    error_log('📥 BACKEND STEP 11D: Processing GraphQL Response');

    if (is_wp_error($response)) {
        error_log('❌ Namecheap API HTTP Error: ' . $response->get_error_message());
        return $response;
    }

    $response_code = wp_remote_retrieve_response_code($response);
    $body = wp_remote_retrieve_body($response);

    error_log('📊 HTTP Response Code: ' . $response_code);
    error_log('📄 Raw Response Body: ' . $body);

    if ($response_code !== 200) {
        error_log('❌ Non-200 HTTP response code: ' . $response_code);
        return new WP_Error('http_error', 'HTTP error: ' . $response_code);
    }

    $data = json_decode($body, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log('❌ JSON decode error: ' . json_last_error_msg());
        error_log('🔍 Raw body that failed to decode: ' . $body);
        return new WP_Error('json_error', 'Failed to decode API response');
    }

    error_log('✅ JSON decoded successfully');
    error_log('🔍 Parsed Response Data: ' . json_encode($data));

    if (isset($data['errors'])) {
        error_log('❌ GraphQL Errors Found: ' . json_encode($data['errors']));
        return new WP_Error('api_error', 'GraphQL API returned errors: ' . json_encode($data['errors']));
    }

    error_log('🔍 Checking for success response...');

    if (isset($data['data']['webhooks_custom_domain_process']['isSuccess']) &&
        $data['data']['webhooks_custom_domain_process']['isSuccess']) {

        $success_data = $data['data']['webhooks_custom_domain_process'];
        error_log('✅ BACKEND STEP 11E: Domain Registration SUCCESSFUL!');
        error_log('🎉 Domain: ' . $domain_name);
        error_log('🌐 IP Address: ' . (isset($success_data['ip']) ? $success_data['ip'] : 'Not provided'));
        error_log('📊 Success Status: ' . ($success_data['isSuccess'] ? 'true' : 'false'));
        error_log('📦 Full Success Response: ' . json_encode($success_data));

        return $success_data;
    } else {
        error_log('❌ BACKEND STEP 11E: Domain Registration FAILED');
        error_log('🔍 Expected path: data.webhooks_custom_domain_process.isSuccess');
        error_log('🔍 Actual response structure: ' . json_encode($data));

        $error_message = 'Domain registration failed';
        if (isset($data['data'])) {
            $error_message .= ': ' . json_encode($data['data']);
        } else {
            $error_message .= ': Unknown error';
        }

        return new WP_Error('registration_failed', $error_message);
    }
}

function handle_create_domain_paynow_payment() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'domain_paynow_payment_nonce')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    // Get domain and amount from POST data
    $domain = sanitize_text_field($_POST['domain']);
    $amount = intval($_POST['amount']);
    $currency = sanitize_text_field($_POST['currency']);

    if (empty($domain) || $amount <= 0) {
        wp_send_json_error(['message' => 'Invalid domain or amount']);
        return;
    }

    try {
        // Load WC_Stripe_API if not already loaded
        if (!class_exists('WC_Stripe_API')) {
            $stripe_api_path = WP_PLUGIN_DIR . '/woocommerce-gateway-stripe/includes/class-wc-stripe-api.php';
            if (file_exists($stripe_api_path)) {
                require_once($stripe_api_path);
            } else {
                throw new Exception('WooCommerce Stripe Gateway API not found.');
            }
        }

        // Get Stripe settings
        $stripe_settings = get_option('woocommerce_stripe_settings');
        $test_mode = isset($stripe_settings['testmode']) && 'yes' === $stripe_settings['testmode'];
        $secret_key = $test_mode ?
            (isset($stripe_settings['test_secret_key']) ? $stripe_settings['test_secret_key'] : '') :
            (isset($stripe_settings['secret_key']) ? $stripe_settings['secret_key'] : '');

        if (empty($secret_key)) {
            throw new Exception('Stripe secret key not configured');
        }

        $current_user = wp_get_current_user();
        $user_email = $current_user->user_email;

        // Create PayNow payment intent for domain purchase using direct Stripe API
        $payment_intent_data = [
            'amount' => $amount,
            'currency' => strtolower($currency),
            'payment_method_types' => ['paynow'],
            'metadata' => [
                'user_id' => get_current_user_id(),
                'user_email' => $user_email,
                'payment_type' => 'domain_purchase',
                'domain' => $domain
            ],
            'description' => "Domain purchase: {$domain}"
        ];

        // Use direct Stripe API call instead of WooCommerce
        $response = wp_remote_post('https://api.stripe.com/v1/payment_intents', [
            'headers' => [
                'Authorization' => 'Bearer ' . $secret_key,
                'Content-Type' => 'application/x-www-form-urlencoded',
            ],
            'body' => http_build_query($payment_intent_data),
            'timeout' => 30
        ]);

        if (is_wp_error($response)) {
            throw new Exception('Failed to create PayNow payment intent: ' . $response->get_error_message());
        }

        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code !== 200) {
            $error_body = wp_remote_retrieve_body($response);
            throw new Exception('Stripe API error: ' . $error_body);
        }

        $payment_intent = json_decode(wp_remote_retrieve_body($response));

        // Generate QR code for PayNow
        $qr_code_url = generate_paynow_qr($payment_intent->client_secret);

        wp_send_json_success([
            'payment_intent_id' => $payment_intent->id,
            'client_secret' => $payment_intent->client_secret,
            'qr_code' => $qr_code_url
        ]);

    } catch (Exception $e) {
        error_log('Domain PayNow Payment Error: ' . $e->getMessage());
        wp_send_json_error(['message' => $e->getMessage()]);
    }

    wp_die();
}

function handle_check_domain_payment_status() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'check_domain_payment_nonce')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    $payment_intent_id = sanitize_text_field($_POST['payment_intent_id']);
    $domain = sanitize_text_field($_POST['domain']);

    if (empty($payment_intent_id)) {
        wp_send_json_error(['message' => 'Invalid payment intent ID']);
        return;
    }

    try {
        // Load WC_Stripe_API if not already loaded
        if (!class_exists('WC_Stripe_API')) {
            $stripe_api_path = WP_PLUGIN_DIR . '/woocommerce-gateway-stripe/includes/class-wc-stripe-api.php';
            if (file_exists($stripe_api_path)) {
                require_once($stripe_api_path);
            } else {
                throw new Exception('WooCommerce Stripe Gateway API not found.');
            }
        }

        // Get Stripe settings
        $stripe_settings = get_option('woocommerce_stripe_settings');
        $test_mode = isset($stripe_settings['testmode']) && 'yes' === $stripe_settings['testmode'];
        $secret_key = $test_mode ?
            (isset($stripe_settings['test_secret_key']) ? $stripe_settings['test_secret_key'] : '') :
            (isset($stripe_settings['secret_key']) ? $stripe_settings['secret_key'] : '');

        if (empty($secret_key)) {
            throw new Exception('Stripe secret key not configured');
        }

        // Set the API key
        WC_Stripe_API::set_secret_key($secret_key);

        // Retrieve payment intent status using WC_Stripe_API
        $payment_intent = WC_Stripe_API::retrieve('payment_intents/' . $payment_intent_id);

        if (is_wp_error($payment_intent)) {
            throw new Exception('Failed to retrieve payment intent: ' . $payment_intent->get_error_message());
        }

        wp_send_json_success([
            'status' => $payment_intent->status,
            'payment_intent_id' => $payment_intent_id,
            'domain' => $domain
        ]);

    } catch (Exception $e) {
        error_log('Domain Payment Status Check Error: ' . $e->getMessage());
        wp_send_json_error(['message' => $e->getMessage()]);
    }

    wp_die();
}

function handle_save_custom_domain() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'save-custom-domain-nonce')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    // Ensure domain tables exist
    ipt_ensure_domain_tables_exist();

    $domain = sanitize_text_field($_POST['domain']);
    $website_id = intval($_POST['website_id']);
    $server_ip = sanitize_text_field($_POST['server_ip']);
    $user_id = get_current_user_id();

    if (empty($domain) || empty($website_id)) {
        wp_send_json_error(['message' => 'Domain and website ID are required']);
        return;
    }

    global $wpdb;
    $table_name = ipt_get_domain_purchases_table();

    // Check if custom domain already exists for this user
    $existing_domain = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $table_name WHERE user_id = %d AND domain_name = %s AND domain_type = 'custom'",
        $user_id,
        $domain
    ));

    if ($existing_domain) {
        wp_send_json_error(['message' => 'Custom domain already exists']);
        return;
    }

    // Insert custom domain record
    $result = $wpdb->insert(
        $table_name,
        [
            'user_id' => $user_id,
            'domain_name' => $domain,
            'domain_type' => 'custom',
            'website_id' => $website_id,
            'server_ip' => $server_ip,
            'status' => 'completed',
            'price' => '0.00',
            'is_premium' => 0,
            'created_at' => current_time('mysql'),
            'paid_at' => current_time('mysql'),
            'expires_at' => date('Y-m-d H:i:s', strtotime('+10 years')) // Custom domains don't expire
        ],
        [
            '%d', '%s', '%s', '%d', '%s', '%s', '%s', '%d', '%s', '%s', '%s'
        ]
    );

    if ($result === false) {
        wp_send_json_error(['message' => 'Failed to save custom domain to database']);
        return;
    }

    wp_send_json_success(['message' => 'Custom domain saved successfully']);
}

function handle_create_paynow_payment() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'paynow_payment_nonce')) {
        wp_die('Security check failed');
    }

    try {
        // Get Stripe settings
        $stripe_settings = get_option('woocommerce_stripe_settings');
        $test_mode = isset($stripe_settings['testmode']) && 'yes' === $stripe_settings['testmode'];
        $secret_key = $test_mode ?
            (isset($stripe_settings['test_secret_key']) ? $stripe_settings['test_secret_key'] : '') :
            (isset($stripe_settings['secret_key']) ? $stripe_settings['secret_key'] : '');

        if (empty($secret_key)) {
            throw new Exception('Stripe secret key not configured');
        }

        \Stripe\Stripe::setApiKey($secret_key);

        $amount = intval($_POST['amount']);
        $currency = sanitize_text_field($_POST['currency']);

        // Create PayNow payment intent
        $payment_intent = \Stripe\PaymentIntent::create([
            'amount' => $amount,
            'currency' => strtolower($currency),
            'payment_method_types' => ['paynow'],
            'metadata' => [
                'user_id' => get_current_user_id(),
                'payment_type' => 'subscription'
            ]
        ]);

        // Generate QR code for PayNow
        $qr_code_url = generate_paynow_qr($payment_intent->client_secret);

        wp_send_json_success([
            'payment_intent_id' => $payment_intent->id,
            'client_secret' => $payment_intent->client_secret,
            'qr_code' => $qr_code_url
        ]);

    } catch (Exception $e) {
        error_log('PayNow payment creation error: ' . $e->getMessage());
        wp_send_json_error(['message' => $e->getMessage()]);
    }
}

function handle_check_payment_status() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'check_payment_nonce')) {
        wp_die('Security check failed');
    }

    try {
        // Get Stripe settings
        $stripe_settings = get_option('woocommerce_stripe_settings');
        $test_mode = isset($stripe_settings['testmode']) && 'yes' === $stripe_settings['testmode'];
        $secret_key = $test_mode ?
            (isset($stripe_settings['test_secret_key']) ? $stripe_settings['test_secret_key'] : '') :
            (isset($stripe_settings['secret_key']) ? $stripe_settings['secret_key'] : '');

        \Stripe\Stripe::setApiKey($secret_key);

        $payment_intent_id = sanitize_text_field($_POST['payment_intent_id']);
        $payment_intent = \Stripe\PaymentIntent::retrieve($payment_intent_id);

        wp_send_json_success([
            'status' => $payment_intent->status,
            'payment_intent' => $payment_intent
        ]);

    } catch (Exception $e) {
        error_log('Payment status check error: ' . $e->getMessage());
        wp_send_json_error(['message' => $e->getMessage()]);
    }
}

function generate_paynow_qr($client_secret) {
    // For PayNow QR generation, you can use:
    // 1. Stripe's built-in QR generation (if available)
    // 2. Third-party QR code library
    // 3. Singapore's PayNow QR specification

    // Simple QR code generation using Google Charts API (for demo)
    $qr_data = urlencode("stripe://payment_intent/{$client_secret}");
    return "https://chart.googleapis.com/chart?chs=200x200&cht=qr&chl={$qr_data}";

    // For production, implement proper PayNow QR generation according to:
    // https://www.mas.gov.sg/development/e-payments/paynow
}


/**
 * Add custom classes to body
 */
function ipt_home_body_classes($classes) {
    // Add default class to all pages
    $classes[] = "font-inter";

    // Add specific class for home page
    if (is_front_page() || is_page_template('page-home.php')) {

    }

    // You can add more conditional classes here

    return $classes;
}
add_filter('body_class', 'ipt_home_body_classes');

/**
 * Register navigation menus
 */
function ipt_home_register_menus() {
    register_nav_menus(
        array(
            'main-menu' => esc_html__('Header Menu', 'ipt-home'),
            'footer-menu' => esc_html__('Footer Menu', 'ipt-home'),
        )
    );
}
add_action('after_setup_theme', 'ipt_home_register_menus');

/**
 * Add theme support
 */
function ipt_home_setup() {
    /*
     * Make theme available for translation.
     */
    load_theme_textdomain('ipt-home', get_stylesheet_directory() . '/languages');

    /*
     * Let WordPress manage the document title.
     */
    add_theme_support('title-tag');

    /*
     * Enable support for Post Thumbnails on posts and pages.
     */
    add_theme_support('post-thumbnails');

    /*
     * Switch default core markup for search form, comment form, and comments
     * to output valid HTML5.
     */
    add_theme_support(
        'html5',
        array(
            'search-form',
            'comment-form',
            'comment-list',
            'gallery',
            'caption',
            'style',
            'script',
        )
    );

    /*
     * Add support for core custom logo.
     */
    add_theme_support(
        'custom-logo',
        array(
            'height'      => 250,
            'width'       => 250,
            'flex-width'  => true,
            'flex-height' => true,
        )
    );
}
add_action('after_setup_theme', 'ipt_home_setup');

/**
 * Register widget areas
 */
function ipt_home_register_widgets() {
    register_sidebar(array(
        'name'          => esc_html__('Custom Login', 'ipt-home'),
        'id'            => 'custom-login',
        'description'   => esc_html__('Widget area for custom login functionality', 'ipt-home'),
        'before_widget' => '<div id="%1$s" class="widget %2$s sso-widget">',
        'after_widget'  => '</div>',
        'before_title'  => '',
        'after_title'   => '',
    ));
}
add_action('widgets_init', 'ipt_home_register_widgets');

function ipt_home_enqueue_styles() {
    $parent_style = 'astra-style'; // This is 'astra-style' for the Astra theme.

    // Enqueue the parent theme's stylesheet
    wp_enqueue_style($parent_style, get_template_directory_uri() . '/style.css');

    // Enqueue the child theme's stylesheet
    wp_enqueue_style('ipt-home-style',
        get_stylesheet_directory_uri() . '/style.css',
        array($parent_style),
        wp_get_theme()->get('Version')
    );

    // Tailwind css
    wp_enqueue_style(
        'ipt-home-tailwind-config',
        get_stylesheet_directory_uri() . '/assets/css/tailwind-output.css',
        array(),
        filemtime(get_stylesheet_directory() . '/assets/css/tailwind-output.css')
    );

    // jquery
    wp_enqueue_script('jquery');

    // Menu js
    wp_enqueue_script(
        'ipt-home-menu',
        get_stylesheet_directory_uri() . '/assets/js/menu.js',
        array('jquery'),
        '1.0.0',
        true
    );

    // Firebase auth handlers (only on login/register pages)
    if (is_page_template('page-login.php') || is_page_template('page-register.php') || is_page('login') || is_page('register')) {
        wp_enqueue_script(
            'ipt-home-firebase-auth',
            get_stylesheet_directory_uri() . '/assets/js/firebase-auth-handlers.js',
            array(),
            '1.0.0',
            true
        );
    }

    // Truyền biến ajax_url sang JS
    wp_localize_script('ipt-home-menu', 'iptHomeAjax', array(
        'ajax_url' => admin_url('admin-ajax.php')
    ));
}
// Hook the function to enqueue scripts and styles
add_action('wp_enqueue_scripts', 'ipt_home_enqueue_styles');

/**
 * Display custom menu with Tailwind CSS structure
 *
 * @param string $theme_location Registered menu location
 * @param string $nav_class CSS classes for nav element
 */
function ipt_home_custom_nav_menu($theme_location = 'main-menu', $nav_class = 'flex gap-6 items-center max-md:hidden') {
    // Get menu ID by location
    $menu_locations = get_nav_menu_locations();

    // Check if menu exists
    if (!isset($menu_locations[$theme_location])) {
        return;
    }

    $menu_id = $menu_locations[$theme_location];

    // Get menu items
    $menu_items = wp_get_nav_menu_items($menu_id);

    if (!$menu_items) {
        return;
    }

    foreach ($menu_items as $item) {
        $active_class = 'text-neutral-light nav-item';
        if(empty(get_queried_object_id())) {
            $active_class = 'text-neutral-strong nav-item';
        } else if ($item->object_id == get_queried_object_id()) {
            $active_class = 'text-neutral-strong nav-item active';
        }
        echo '<li class=""><a href="' . esc_url($item->url) . '" class="text-[16px] text-center md:w-[106px] py-[20px] block font-semibold ' . esc_attr($active_class) . ' hover:!text-neutral-strong active:!text-neutral-strong focus:!text-neutral-strong" aria-current="page">';
        echo esc_html($item->title);
        echo '</a></li>';
    }

}

function features_to_map($features) {
    $map = [];
    foreach ($features as $f) {
        $map[$f['id']] = $f['value'];
    }
    return $map;
}


/**
 * Disable WordPress auto-updates
 */
function disable_automatic_updates() {
    // Disable WordPress core updates
    add_filter( 'auto_update_core', '__return_false' );

    // Disable plugin auto-updates
    add_filter( 'auto_update_plugin', '__return_false' );

    // Disable theme auto-updates
    add_filter( 'auto_update_theme', '__return_false' );

    // Disable translation auto-updates
    add_filter( 'auto_update_translation', '__return_false' );

    // Disable update emails
    add_filter( 'auto_core_update_send_email', '__return_false' );

    // Disable plugin update notifications
    remove_action( 'load-update-core.php', 'wp_update_plugins' );
    add_filter( 'pre_site_transient_update_plugins', '__return_null' );

    // Disable theme update notifications
    remove_action( 'load-update-core.php', 'wp_update_themes' );
    add_filter( 'pre_site_transient_update_themes', '__return_null' );
}
add_action( 'init', 'disable_automatic_updates' );

/**
 * Remove update menu items
 */
function remove_update_menu_items() {
    // Remove WordPress update notification
    remove_action( 'admin_notices', 'update_nag', 3 );

    // Remove plugin update count from menu
    global $submenu;
    if ( isset( $submenu['index.php'] ) ) {
        foreach ( $submenu['index.php'] as $key => $menu ) {
            if ( strpos( $menu[2], 'update-core.php' ) !== false ) {
                unset( $submenu['index.php'][$key] );
                break;
            }
        }
    }
}
add_action( 'admin_menu', 'remove_update_menu_items', 999 );

/**
 * Hide admin toolbar for all user roles
 */
add_filter('show_admin_bar', function($show) {
    // Hide admin toolbar for all users, including administrators
    return false;
});
/**
 * Create WordPress user after successful GraphQL registration
 */
function ipt_home_create_wp_user() {
    // Verify request
    if (!isset($_POST['email']) || !isset($_POST['password'])) {
        wp_send_json_error(['message' => 'Missing required fields']);
        return;
    }

    $email = sanitize_email($_POST['email']);
    $password = $_POST['password'];
    
    // Check if user already exists
    if (email_exists($email)) {
        wp_send_json_success(['message' => 'User already exists']);
        return;
    }
    
    // Generate username from email
    $username = sanitize_user(current(explode('@', $email)), true);
    
    // Make sure username is unique
    $counter = 1;
    $original_username = $username;
    while (username_exists($username)) {
        $username = $original_username . $counter;
        $counter++;
    }
    
    // Create user
    $user_data = [
        'user_login' => $username,
        'user_email' => $email,
        'user_pass' => $password,
        'role' => 'customer'
    ];
    
    $user_id = wp_insert_user($user_data);
    
    if (is_wp_error($user_id)) {
        wp_send_json_error(['message' => $user_id->get_error_message()]);
    } else {
        error_log("🔥 Custom registration: User created successfully, ID: {$user_id}");

        // Auto-assign trial to new registered user
        if (function_exists('ipt_home_auto_assign_trial_to_new_customer')) {
            error_log("🎯 Calling auto-assign function for user: {$user_id}");
            ipt_home_auto_assign_trial_to_new_customer($user_id);
            error_log("✅ Auto-assign function completed for user: {$user_id}");
        } else {
            error_log("❌ Auto-assign function not found!");
        }

        // Successfully created WordPress user
        wp_send_json_success(['message' => 'User created successfully', 'user_id' => $user_id]);
    }
}
add_action('wp_ajax_nopriv_create_wp_user', 'ipt_home_create_wp_user');
add_action('wp_ajax_create_wp_user', 'ipt_home_create_wp_user');

/**
 * Handle login via Ajax
 */
function ipt_home_login() {
    // Check if nonce exists before verifying
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'ipt_login_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed. Please refresh the page and try again.'));
        return;
    }
    
    // Get login information
    $email = isset($_POST['email']) ? sanitize_email($_POST['email']) : '';
    $password = isset($_POST['password']) ? $_POST['password'] : '';
    
    // Check email
    if (empty($email)) {
        wp_send_json_error(array('message' => 'Please enter your email.'));
        return;
    }
    
    // Check password
    if (empty($password)) {
        wp_send_json_error(array('message' => 'Please enter your password.'));
        return;
    }
    
    // Get user by email
    $user = get_user_by('email', $email);
    
    // If user not found, try by username
    if (!$user) {
        $user = get_user_by('login', $email);
    }
    
    // Check user and password
    if (!$user || !wp_check_password($password, $user->user_pass, $user->ID)) {
        wp_send_json_error(array('message' => 'Invalid email or password.'));
        return;
    }
    
    // Check if user is a customer (optional, remove if you want to allow all user types)
    if (!in_array('customer', (array) $user->roles)) {
        wp_send_json_error(array('message' => 'This login form is for customers only.'));
        return;
    }
    
    // Use WordPress core function to log in the user
    $user_signon = wp_signon(
        array(
            'user_login'    => $user->user_login,
            'user_password' => $password,
            'remember'      => true
        ),
        is_ssl()
    );
    
    if (is_wp_error($user_signon)) {
        wp_send_json_error(array('message' => $user_signon->get_error_message()));
        return;
    }
    
    // Set auth cookie and current user
    wp_set_auth_cookie($user->ID, true);
    wp_set_current_user($user->ID);
    
    // Apply filters for redirect URL (respects login_redirect filter)
    $redirect_url = apply_filters('login_redirect', home_url('customer/dashboard') , '', $user);
    
    // Return success result
    wp_send_json_success(array(
        'message' => 'Login successful.',
        'redirect' => $redirect_url
    ));
}
add_action('wp_ajax_nopriv_ipt_home_login', 'ipt_home_login');
add_action('wp_ajax_ipt_home_login', 'ipt_home_login');

/**
 * Handle customer login via Ajax with GraphQL customer_id
 */
function ipt_customer_login() {
    // Check if nonce exists before verifying
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'ipt_login_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed. Please refresh the page and try again.'));
        return;
    }

    // Get login information
    $email = isset($_POST['email']) ? sanitize_email($_POST['email']) : '';
    $password = isset($_POST['password']) ? $_POST['password'] : '';
    $customer_id = isset($_POST['customer_id']) ? sanitize_text_field($_POST['customer_id']) : '';

    // Check email
    if (empty($email)) {
        wp_send_json_error(array('message' => 'Invalid email or password.'));
        return;
    }

    // Check password
    if (empty($password)) {
        wp_send_json_error(array('message' => 'Invalid email or password.'));
        return;
    }

    // Check customer_id (should be provided from GraphQL response)
    if (empty($customer_id)) {
        wp_send_json_error(array('message' => 'Invalid email or password.'));
        return;
    }

    // Get user by email
    $user = get_user_by('email', $email);

    // If user not found, try by username
    if (!$user) {
        $user = get_user_by('login', $email);
    }

    // Check user and password
    if (!$user || !wp_check_password($password, $user->user_pass, $user->ID)) {
        wp_send_json_error(array('message' => 'Invalid email or password.'));
        return;
    }

    // Check if user is an administrator (block administrators from using this login form)
    if (in_array('administrator', (array) $user->roles)) {
        wp_send_json_error(array('message' => 'Invalid email or password.'));
        return;
    }

    // Use WordPress core function to log in the user
    $user_signon = wp_signon(
        array(
            'user_login'    => $user->user_login,
            'user_password' => $password,
            'remember'      => true
        ),
        is_ssl()
    );

    if (is_wp_error($user_signon)) {
        wp_send_json_error(array('message' => 'Invalid email or password.'));
        return;
    }

    // Set auth cookie and current user
    wp_set_auth_cookie($user->ID, true);
    wp_set_current_user($user->ID);

    // Save customer_id as user meta
    $meta_saved = update_user_meta($user->ID, 'customer_id', $customer_id);

    // Log the meta save operation
    error_log("Customer Login: Saving customer_id {$customer_id} for user {$user->ID}. Result: " . ($meta_saved ? 'success' : 'failed'));

    // Determine redirect URL - always redirect to customer dashboard
    $redirect_url = apply_filters('login_redirect', home_url('customer/dashboard') , '', $user);

    // Return success result
    wp_send_json_success(array(
        'message' => 'Login successful.',
        'redirect' => $redirect_url,
        'customer_id' => $customer_id,
        'user_id' => $user->ID
    ));
}
add_action('wp_ajax_nopriv_ipt_customer_login', 'ipt_customer_login');
add_action('wp_ajax_ipt_customer_login', 'ipt_customer_login');

/**
 * Enqueue scripts and styles.
 */
function ipt_home_scripts() {
    // Các script và style khác...
    
    // Enqueue toast notification script - Sửa đường dẫn và handle
    wp_enqueue_script(
        'ipt-toast', 
        get_stylesheet_directory_uri() . '/assets/js/toast.js', 
        array('jquery'), 
        filemtime(get_stylesheet_directory() . '/assets/js/toast.js'), 
        true
    );
}
add_action('wp_enqueue_scripts', 'ipt_home_scripts');

/**
 * Redirect customers from wp-admin and wp-login to customer dashboard
 */
function ipt_home_redirect_customers() {
    // Check if user is logged in
    // if (is_user_logged_in()) {
    //     $user = wp_get_current_user();
        
    //     // Check if user has customer role
    //     if (in_array('customer', (array) $user->roles)) {
    //         // Get current screen
    //         $current_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
    //         $admin_url = admin_url();
    //         $login_url = wp_login_url();
            
    //         // Check if current URL contains wp-admin or wp-login.php
    //         if (strpos($current_url, '/wp-admin') !== false || strpos($current_url, '/wp-login.php') !== false) {
    //             // Only redirect if not an AJAX request
    //             if (!wp_doing_ajax()) {
    //                 wp_redirect(home_url('customer/dashboard'));
    //                 exit;
    //             }
    //         }
    //     }
    // }
}
add_action('init', 'ipt_home_redirect_customers', 1);

/**
 * Prevent customers from accessing admin pages
 */
function ipt_home_restrict_admin_access() {
    if (is_admin() && !wp_doing_ajax() && is_user_logged_in()) {
        $user = wp_get_current_user();
        
        // If user is a customer, redirect to customer dashboard
        if (in_array('customer', (array) $user->roles) && !current_user_can('edit_posts')) {
            wp_redirect(home_url('customer/dashboard'));
            exit;
        }
    }
}
add_action('admin_init', 'ipt_home_restrict_admin_access');

/**
 * Modify login redirect for customers
 */
function ipt_home_login_redirect($redirect_to, $request, $user) {
    // Check if user is logged in and is a customer
    if (isset($user->roles) && is_array($user->roles) && in_array('customer', $user->roles)) {
        // Redirect to customer dashboard
        return home_url('customer/dashboard');
    }
    
    // Return default redirect URL for other users
    return $redirect_to;
}
add_filter('login_redirect', 'ipt_home_login_redirect', 10, 3);

/**
 * Ensure users are redirected to home page after logout
 */
function ipt_home_logout_redirect($redirect_to, $requested_redirect_to, $user) {
    // Always redirect to home page after logout
    return home_url();
}
add_filter('logout_redirect', 'ipt_home_logout_redirect', 10, 3);

/**
 * Kiểm tra và chuyển hướng người dùng dựa trên vai trò khi truy cập trang customer
 */
function ipt_home_check_customer_access() {
    // Chỉ kiểm tra khi đang ở trang customer
    if (isset($_GET['customer_page'])) {
        if (!is_user_logged_in()) {
            // Người dùng chưa đăng nhập, chuyển hướng đến trang đăng nhập
            wp_redirect(home_url().'?page_id=41');
            exit;
        } else {
            $current_user = wp_get_current_user();
            $user_roles = (array) $current_user->roles;

            // Kiểm tra vai trò người dùng
            if (in_array('customer', $user_roles)) {
                // Người dùng có vai trò customer, cho phép truy cập
                return;
            } elseif (in_array('administrator', $user_roles)) {
                // Người dùng là administrator thì di chuyển đến wp-admin dashboard của wordpress mặc định
                wp_redirect(admin_url());
                exit;
            } else {
                // Người dùng có vai trò khác, không cho phép truy cập
                wp_redirect(home_url());
                exit;
            }
        }
    }
}
add_action('template_redirect', 'ipt_home_check_customer_access', 1);

/**
 * Update WordPress user password after successful GraphQL reset
 */
function ipt_home_update_wp_user_password() {
    // Verify request
    if (!isset($_POST['email']) || !isset($_POST['password'])) {
        wp_send_json_error(['message' => 'Missing required fields']);
        return;
    }

    $email = sanitize_email($_POST['email']);
    $password = $_POST['password'];
    
    // Check if user exists
    $user = get_user_by('email', $email);
    if (!$user) {
        wp_send_json_error(['message' => 'User not found']);
        return;
    }
    
    // Update user password
    wp_set_password($password, $user->ID);
    
    // Return success
    wp_send_json_success(['message' => 'Password updated successfully']);
}
add_action('wp_ajax_update_wp_user_password', 'ipt_home_update_wp_user_password');
add_action('wp_ajax_nopriv_update_wp_user_password', 'ipt_home_update_wp_user_password');

/**
 * Create GraphQL logs table on theme activation
 */
function ipt_home_create_graphql_logs_table() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'ipt_graphql_logs';

    $charset_collate = $wpdb->get_charset_collate();

    $sql = "CREATE TABLE $table_name (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        api_url varchar(500) NOT NULL,
        request_data longtext NOT NULL,
        response_data longtext NOT NULL,
        status_code varchar(10) DEFAULT NULL,
        execution_time float DEFAULT NULL,
        user_id bigint(20) DEFAULT NULL,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY user_id (user_id),
        KEY created_at (created_at)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}
add_action('after_switch_theme', 'ipt_home_create_graphql_logs_table');

// Create table immediately if it doesn't exist
function ipt_home_ensure_graphql_logs_table() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'ipt_graphql_logs';

    // Check if table exists
    if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
        ipt_home_create_graphql_logs_table();
    }
}
add_action('init', 'ipt_home_ensure_graphql_logs_table');

/**
 * Add admin menu for GraphQL logs
 */
function ipt_home_add_graphql_logs_menu() {
    add_menu_page(
        'GraphQL API Logs',           // Page title
        'GraphQL Logs',               // Menu title
        'manage_options',             // Capability
        'ipt-graphql-logs',          // Menu slug
        'ipt_home_graphql_logs_page', // Function
        'dashicons-database-view',    // Icon
        30                           // Position
    );
}
add_action('admin_menu', 'ipt_home_add_graphql_logs_menu');

/**
 * Display GraphQL logs admin page
 */
function ipt_home_graphql_logs_page() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'ipt_graphql_logs';

    // Handle delete action
    if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['log_id'])) {
        $log_id = intval($_GET['log_id']);
        $wpdb->delete($table_name, array('id' => $log_id), array('%d'));
        echo '<div class="notice notice-success"><p>Log deleted successfully.</p></div>';
    }

    // Handle clear all action
    if (isset($_GET['action']) && $_GET['action'] === 'clear_all') {
        $wpdb->query("TRUNCATE TABLE $table_name");
        echo '<div class="notice notice-success"><p>All logs cleared successfully.</p></div>';
    }

    // Filters
    $status_filter = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : '';
    $date_filter = isset($_GET['date']) ? sanitize_text_field($_GET['date']) : '';
    $search = isset($_GET['s']) ? sanitize_text_field($_GET['s']) : '';

    // Build WHERE clause
    $where_conditions = array();
    $where_values = array();

    if ($status_filter) {
        $where_conditions[] = "status_code = %s";
        $where_values[] = $status_filter;
    }

    if ($date_filter) {
        $where_conditions[] = "DATE(created_at) = %s";
        $where_values[] = $date_filter;
    }

    if ($search) {
        // Search in serialized data requires different approach
        $where_conditions[] = "(request_data LIKE %s OR response_data LIKE %s OR api_url LIKE %s)";
        $search_term = '%' . $wpdb->esc_like($search) . '%';
        $where_values[] = $search_term;
        $where_values[] = $search_term;
        $where_values[] = $search_term;
    }

    $where_clause = '';
    if (!empty($where_conditions)) {
        $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
    }

    // Pagination
    $per_page = 20;
    $current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
    $offset = ($current_page - 1) * $per_page;

    // Get total count with filters
    $count_query = "SELECT COUNT(*) FROM $table_name $where_clause";
    if (!empty($where_values)) {
        $total_logs = $wpdb->get_var($wpdb->prepare($count_query, $where_values));
    } else {
        $total_logs = $wpdb->get_var($count_query);
    }
    $total_pages = ceil($total_logs / $per_page);

    // Get logs with pagination and filters
    $logs_query = "SELECT * FROM $table_name $where_clause ORDER BY created_at DESC LIMIT %d OFFSET %d";
    $query_values = array_merge($where_values, array($per_page, $offset));
    $logs = $wpdb->get_results($wpdb->prepare($logs_query, $query_values));

    ?>
    <div class="wrap">
        <h1>GraphQL API Logs</h1>

        <!-- Filters -->
        <div class="alignleft actions" style="margin-bottom: 10px;">
            <form method="get" style="display: inline-block;">
                <input type="hidden" name="page" value="ipt-graphql-logs">

                <input type="text" name="s" placeholder="Search logs..." value="<?php echo esc_attr($search); ?>" style="width: 200px;">

                <select name="status">
                    <option value="">All Status Codes</option>
                    <option value="200" <?php selected($status_filter, '200'); ?>>200 - Success</option>
                    <option value="400" <?php selected($status_filter, '400'); ?>>400 - Bad Request</option>
                    <option value="401" <?php selected($status_filter, '401'); ?>>401 - Unauthorized</option>
                    <option value="500" <?php selected($status_filter, '500'); ?>>500 - Server Error</option>
                    <option value="ERROR" <?php selected($status_filter, 'ERROR'); ?>>ERROR - Network Error</option>
                </select>

                <input type="date" name="date" value="<?php echo esc_attr($date_filter); ?>">

                <input type="submit" class="button" value="Filter">

                <?php if ($search || $status_filter || $date_filter): ?>
                    <a href="<?php echo admin_url('admin.php?page=ipt-graphql-logs'); ?>" class="button">Clear Filters</a>
                <?php endif; ?>
            </form>
        </div>
        <div style="clear: both;"></div>

        <div class="tablenav top">
            <div class="alignleft actions">
                <a href="<?php echo admin_url('admin.php?page=ipt-graphql-logs&action=clear_all'); ?>"
                   class="button button-secondary"
                   onclick="return confirm('Are you sure you want to clear all logs?');">
                    Clear All Logs
                </a>
            </div>

            <div class="tablenav-pages">
                <span class="displaying-num"><?php echo $total_logs; ?> items</span>
                <?php if ($total_pages > 1): ?>
                    <span class="pagination-links">
                        <?php if ($current_page > 1): ?>
                            <a class="prev-page button" href="<?php echo admin_url('admin.php?page=ipt-graphql-logs&paged=' . ($current_page - 1)); ?>">‹</a>
                        <?php endif; ?>

                        <span class="paging-input">
                            <span class="tablenav-paging-text">
                                <?php echo $current_page; ?> of <span class="total-pages"><?php echo $total_pages; ?></span>
                            </span>
                        </span>

                        <?php if ($current_page < $total_pages): ?>
                            <a class="next-page button" href="<?php echo admin_url('admin.php?page=ipt-graphql-logs&paged=' . ($current_page + 1)); ?>">›</a>
                        <?php endif; ?>
                    </span>
                <?php endif; ?>
            </div>
        </div>

        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th scope="col" style="width: 60px;">ID</th>
                    <th scope="col" style="width: 150px;">Date/Time</th>
                    <th scope="col" style="width: 200px;">API URL</th>
                    <th scope="col" style="width: 80px;">Status</th>
                    <th scope="col" style="width: 100px;">Execution Time</th>
                    <th scope="col" style="width: 100px;">User</th>
                    <th scope="col">Request/Response</th>
                    <th scope="col" style="width: 100px;">Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($logs)): ?>
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 20px;">No logs found.</td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($logs as $log): ?>
                        <tr>
                            <td><?php echo esc_html($log->id); ?></td>
                            <td><?php echo esc_html(date('Y-m-d H:i:s', strtotime($log->created_at))); ?></td>
                            <td>
                                <span title="<?php echo esc_attr($log->api_url); ?>">
                                    <?php echo esc_html(substr($log->api_url, 0, 30) . (strlen($log->api_url) > 30 ? '...' : '')); ?>
                                </span>
                            </td>
                            <td>
                                <span class="status-badge status-<?php echo esc_attr(strtolower($log->status_code)); ?>">
                                    <?php echo esc_html($log->status_code); ?>
                                </span>
                            </td>
                            <td><?php echo esc_html(number_format($log->execution_time, 3)); ?>s</td>
                            <td>
                                <?php
                                if ($log->user_id) {
                                    $user = get_user_by('id', $log->user_id);
                                    echo $user ? esc_html($user->display_name) : 'Unknown';
                                } else {
                                    echo 'Guest';
                                }
                                ?>
                            </td>
                            <td>
                                <button type="button" class="button button-small toggle-details" data-log-id="<?php echo esc_attr($log->id); ?>">
                                    View Details
                                </button>
                                <div id="details-<?php echo esc_attr($log->id); ?>" class="log-details" style="display: none; margin-top: 10px;">
                                    <?php
                                    // Try to decode JSON first, then try unserialize for backward compatibility
                                    $request_data = json_decode($log->request_data, true);
                                    if ($request_data !== null) {
                                        // New format with headers
                                        if (isset($request_data['headers']) && isset($request_data['body'])) {
                                            echo '<h4>Headers:</h4>';
                                            echo '<pre style="background: #e8f4fd; padding: 10px; max-height: 150px; overflow: auto; font-size: 12px;">';
                                            echo esc_html(json_encode($request_data['headers'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                                            echo '</pre>';

                                            // Check if Bearer token is present
                                            if (isset($request_data['headers']['Authorization'])) {
                                                $auth_header = $request_data['headers']['Authorization'];
                                                if (strpos($auth_header, 'Bearer ') === 0) {
                                                    echo '<p style="color: green; font-size: 12px;">✓ Bearer token detected: ' .
                                                         esc_html(substr($auth_header, 0, 20)) . '...</p>';
                                                } else {
                                                    echo '<p style="color: orange; font-size: 12px;">⚠ Authorization header present but not Bearer token</p>';
                                                }
                                            } else {
                                                echo '<p style="color: red; font-size: 12px;">✗ No Authorization header found</p>';
                                            }

                                            echo '<h4>Request Body:</h4>';
                                            echo '<pre style="background: #f1f1f1; padding: 10px; max-height: 200px; overflow: auto; font-size: 12px;">';
                                            echo esc_html(json_encode($request_data['body'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                                            echo '</pre>';
                                        } else {
                                            // Old format without headers
                                            echo '<h4>Request (Legacy Format):</h4>';
                                            echo '<pre style="background: #f1f1f1; padding: 10px; max-height: 200px; overflow: auto; font-size: 12px;">';
                                            echo esc_html(json_encode($request_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                                            echo '</pre>';
                                        }
                                    } else {
                                        $request_data = @unserialize($log->request_data);
                                        if ($request_data !== false) {
                                            echo '<h4>Request (Serialized):</h4>';
                                            echo '<pre style="background: #f1f1f1; padding: 10px; max-height: 200px; overflow: auto; font-size: 12px;">';
                                            echo esc_html(print_r($request_data, true));
                                            echo '</pre>';
                                        } else {
                                            echo '<h4>Request (Raw):</h4>';
                                            echo '<pre style="background: #f1f1f1; padding: 10px; max-height: 200px; overflow: auto; font-size: 12px;">';
                                            echo esc_html($log->request_data);
                                            echo '</pre>';
                                        }
                                    }
                                    ?>

                                    <h4>Response:</h4>
                                    <pre style="background: #f1f1f1; padding: 10px; max-height: 200px; overflow: auto; font-size: 12px;"><?php
                                        // Try to decode JSON first, then try unserialize for backward compatibility
                                        $response_data = json_decode($log->response_data, true);
                                        if ($response_data !== null) {
                                            echo esc_html(json_encode($response_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                                        } else {
                                            $response_data = @unserialize($log->response_data);
                                            if ($response_data !== false) {
                                                echo esc_html(print_r($response_data, true));
                                            } else {
                                                echo esc_html($log->response_data);
                                            }
                                        }
                                    ?></pre>
                                </div>
                            </td>
                            <td>
                                <a href="<?php echo admin_url('admin.php?page=ipt-graphql-logs&action=delete&log_id=' . $log->id); ?>"
                                   class="button button-small button-link-delete"
                                   onclick="return confirm('Are you sure you want to delete this log?');">
                                    Delete
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <style>
        .status-badge {
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
        }
        .status-200 { background: #d4edda; color: #155724; }
        .status-error { background: #f8d7da; color: #721c24; }
        .status-400, .status-401, .status-403, .status-404, .status-500 { background: #f8d7da; color: #721c24; }
        .log-details pre {
            white-space: pre-wrap;
            word-wrap: break-word;
        }
    </style>

    <script>
        jQuery(document).ready(function($) {
            $('.toggle-details').click(function() {
                var logId = $(this).data('log-id');
                var details = $('#details-' + logId);

                if (details.is(':visible')) {
                    details.hide();
                    $(this).text('View Details');
                } else {
                    details.show();
                    $(this).text('Hide Details');
                }
            });
        });
    </script>
    <?php
}

/**
 * AJAX handler to get log details
 */
function ipt_home_get_log_details() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    $log_id = isset($_POST['log_id']) ? intval($_POST['log_id']) : 0;

    if (!$log_id) {
        wp_send_json_error('Invalid log ID');
        return;
    }

    global $wpdb;
    $table_name = $wpdb->prefix . 'ipt_graphql_logs';

    $log = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $table_name WHERE id = %d",
        $log_id
    ));

    if (!$log) {
        wp_send_json_error('Log not found');
        return;
    }

    // Try JSON decode first, then unserialize for backward compatibility
    $request_data = json_decode($log->request_data, true);
    if ($request_data === null) {
        $request_data = @unserialize($log->request_data);
    }

    $response_data = json_decode($log->response_data, true);
    if ($response_data === null) {
        $response_data = @unserialize($log->response_data);
    }

    wp_send_json_success(array(
        'request' => $request_data !== false && $request_data !== null ?
                    (is_array($request_data) ? json_encode($request_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) : print_r($request_data, true)) :
                    $log->request_data,
        'response' => $response_data !== false && $response_data !== null ?
                     (is_array($response_data) ? json_encode($response_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) : print_r($response_data, true)) :
                     $log->response_data,
        'api_url' => $log->api_url,
        'status_code' => $log->status_code,
        'execution_time' => $log->execution_time,
        'created_at' => $log->created_at
    ));
}
add_action('wp_ajax_get_log_details', 'ipt_home_get_log_details');

/**
 * Get customer ID from user meta for GraphQL API
 * This gets the customer_id that was saved during login from GraphQL auth_login response
 */
function ipt_home_get_customer_id_from_user($user_id) {
    if (!$user_id) {
        return null;
    }

    // Get customer_id from user meta (saved during login)
    $customer_id = get_user_meta($user_id, 'customer_id', true);

    if (empty($customer_id)) {
        error_log("No customer_id found in user meta for user {$user_id}. User may need to login again.");
        return null;
    }

    error_log("Retrieved customer_id {$customer_id} from user meta for user {$user_id}");
    return (int) $customer_id;
}

/**
 * Create transaction record via GraphQL API
 */
function ipt_home_create_transaction($customer_id, $plan_id, $price, $status_id = null, $order = null) {
    // Set default status to PENDING if not provided
    if ($status_id === null) {
        $status_id = IPT_STATUS_ID_PENDING;
    }

    // Calculate dates based on subscription type
    $start_date = date('Y-m-d'); // Current date

    // Log order information for debugging
    if ($order) {
        error_log("Processing transaction for order {$order->get_id()}: total={$order->get_total()}, is_trial_order=" . $order->get_meta('_is_trial_order'));
    }

    // Check if this is a trial order and use appropriate duration
    if ($order && $order->get_meta('_is_trial_order') === 'yes') {
        // This is a trial order - use trial days
        $trial_days = (int) $order->get_meta('_trial_days');
        if ($trial_days > 0) {
            $end_date = date('Y-m-d', strtotime('+' . $trial_days . ' days'));
            error_log("Transaction dates for trial order: start={$start_date}, end={$end_date} (trial_days={$trial_days})");
        } else {
            // Fallback to 30 days if trial days not found
            $end_date = date('Y-m-d', strtotime('+30 days'));
            error_log("Trial order but no trial_days found, using 30 days fallback");
        }
    } else {
        // Check if any products in the order are trial products (fallback detection)
        $trial_days_from_product = 0;
        if ($order) {
            foreach ($order->get_items() as $item) {
                $product = $item->get_product();
                if ($product) {
                    $subscription_type_id = $product->get_meta('subscription_type_id');
                    $subscription_trial_days = (int) $product->get_meta('subscription_trial_days');

                    // If this is a trial product (type 1) with trial days and order total is 0
                    if ($subscription_type_id == '1' && $subscription_trial_days > 0 && $order->get_total() == 0) {
                        $trial_days_from_product = $subscription_trial_days;
                        error_log("Detected trial product in order: trial_days={$trial_days_from_product}");
                        break;
                    }
                }
            }
        }

        if ($trial_days_from_product > 0) {
            // Use trial days from product
            $end_date = date('Y-m-d', strtotime('+' . $trial_days_from_product . ' days'));
            error_log("Transaction dates using product trial days: start={$start_date}, end={$end_date} (trial_days={$trial_days_from_product})");
        } else {
            // Regular subscription - use 30 days
            $end_date = date('Y-m-d', strtotime('+30 days'));
            error_log("Transaction dates for regular order: start={$start_date}, end={$end_date} (30 days)");
        }
    }

    // Prepare info field with WooCommerce transaction data
    $info_data = [];

    if ($order && is_a($order, 'WC_Order')) {
        // Collect comprehensive order data
        $info_data = [
            'order_id' => $order->get_id(),
            'order_number' => $order->get_order_number(),
            'order_key' => $order->get_order_key(),
            'order_status' => $order->get_status(),
            'order_date' => $order->get_date_created() ? $order->get_date_created()->format('Y-m-d H:i:s') : null,
            'order_total' => $order->get_total(),
            'order_subtotal' => $order->get_subtotal(),
            'order_tax' => $order->get_total_tax(),
            'order_shipping' => $order->get_shipping_total(),
            'order_discount' => $order->get_total_discount(),
            'currency' => $order->get_currency(),

            // Payment information
            'payment_method' => $order->get_payment_method(),
            'payment_method_title' => $order->get_payment_method_title(),
            'transaction_id' => $order->get_transaction_id(),
            'date_paid' => $order->get_date_paid() ? $order->get_date_paid()->format('Y-m-d H:i:s') : null,

            // Customer information
            'customer_id' => $order->get_customer_id(),
            'customer_email' => $order->get_billing_email(),
            'customer_phone' => $order->get_billing_phone(),

            // Billing address
            'billing_address' => [
                'first_name' => $order->get_billing_first_name(),
                'last_name' => $order->get_billing_last_name(),
                'company' => $order->get_billing_company(),
                'address_1' => $order->get_billing_address_1(),
                'address_2' => $order->get_billing_address_2(),
                'city' => $order->get_billing_city(),
                'state' => $order->get_billing_state(),
                'postcode' => $order->get_billing_postcode(),
                'country' => $order->get_billing_country(),
            ],

            // Shipping address
            'shipping_address' => [
                'first_name' => $order->get_shipping_first_name(),
                'last_name' => $order->get_shipping_last_name(),
                'company' => $order->get_shipping_company(),
                'address_1' => $order->get_shipping_address_1(),
                'address_2' => $order->get_shipping_address_2(),
                'city' => $order->get_shipping_city(),
                'state' => $order->get_shipping_state(),
                'postcode' => $order->get_shipping_postcode(),
                'country' => $order->get_shipping_country(),
            ],

            // Order items
            'items' => [],

            // Stripe specific data
            'stripe_data' => []
        ];

        // Add order items
        foreach ($order->get_items() as $item_id => $item) {
            $product = $item->get_product();
            $info_data['items'][] = [
                'item_id' => $item_id,
                'product_id' => $item->get_product_id(),
                'variation_id' => $item->get_variation_id(),
                'name' => $item->get_name(),
                'quantity' => $item->get_quantity(),
                'subtotal' => $item->get_subtotal(),
                'total' => $item->get_total(),
                'tax' => $item->get_total_tax(),
                'sku' => $product ? $product->get_sku() : '',
                'price' => $product ? $product->get_price() : 0
            ];
        }

        // Add Stripe specific metadata
        $stripe_intent_id = $order->get_meta('_stripe_intent_id');
        $stripe_charge_id = $order->get_meta('_stripe_charge_id');
        $stripe_payment_intent = $order->get_meta('_stripe_payment_intent');
        $stripe_source_id = $order->get_meta('_stripe_source_id');

        if ($stripe_intent_id || $stripe_charge_id || $stripe_payment_intent || $stripe_source_id) {
            $info_data['stripe_data'] = [
                'intent_id' => $stripe_intent_id,
                'charge_id' => $stripe_charge_id,
                'payment_intent' => $stripe_payment_intent,
                'source_id' => $stripe_source_id,
                'card_last4' => $order->get_meta('_stripe_card_last4'),
                'card_brand' => $order->get_meta('_stripe_card_brand'),
                'card_exp_month' => $order->get_meta('_stripe_card_exp_month'),
                'card_exp_year' => $order->get_meta('_stripe_card_exp_year'),
            ];
        }

        // Add custom meta data
        $meta_data = $order->get_meta_data();
        $custom_meta = [];
        foreach ($meta_data as $meta) {
            $key = $meta->get_data()['key'];
            $value = $meta->get_data()['value'];
            // Only include non-private meta (not starting with _)
            if (strpos($key, '_') !== 0) {
                $custom_meta[$key] = $value;
            }
        }
        if (!empty($custom_meta)) {
            $info_data['custom_meta'] = $custom_meta;
        }
    }

    // Keep info data as object for GraphQL JSON type
    $info_object = $info_data;

    // GraphQL mutation with info field enabled
    $mutation = '
        mutation Webhooks_transactions_create(
            $customer_id: Int!,
            $plan_id: Int!,
            $type_id: Int!,
            $price: Float!,
            $status_id: Int!,
            $start_date: String!,
            $end_date: String!,
            $info: JSON
        ) {
            webhooks_transactions_create(
                input: {
                    customer_id: $customer_id,
                    plan_id: $plan_id,
                    type_id: $type_id,
                    price: $price,
                    status_id: $status_id,
                    start_date: $start_date,
                    end_date: $end_date,
                    info: $info
                }
            ) {
                id
            }
        }
    ';

    // Variables for the mutation with info field enabled
    $variables = [
        'customer_id' => (int) $customer_id,
        'plan_id' => (int) $plan_id,
        'type_id' => 1, // Always 1 as specified
        'price' => (float) $price,
        'status_id' => (int) $status_id,
        'start_date' => $start_date,
        'end_date' => $end_date,
        'info' => $info_object
    ];

    try {
        // Call GraphQL API
        $result = ipt_home_fetch_graphql_data($mutation, $variables);

        if (isset($result['data']['webhooks_transactions_create']['id'])) {
            error_log('Transaction created successfully with ID: ' . $result['data']['webhooks_transactions_create']['id']);
            return [
                'success' => true,
                'transaction_id' => $result['data']['webhooks_transactions_create']['id'],
                'data' => $result['data']
            ];
        } else {
            error_log('Failed to create transaction: ' . print_r($result, true));
            return [
                'success' => false,
                'error' => 'Failed to create transaction',
                'data' => $result
            ];
        }
    } catch (Exception $e) {
        error_log('Exception creating transaction: ' . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * AJAX handler for creating transaction
 */
function ipt_home_create_transaction_ajax() {
    // Check nonce
    if (!wp_verify_nonce($_POST['nonce'], 'create_transaction_nonce')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    // Check if user is logged in
    if (!is_user_logged_in()) {
        wp_send_json_error(['message' => 'User not logged in']);
        return;
    }

    // Get parameters
    $customer_id = get_current_user_id();
    $plan_id = isset($_POST['plan_id']) ? intval($_POST['plan_id']) : 0;
    $price = isset($_POST['price']) ? floatval($_POST['price']) : 0;
    $status_id = isset($_POST['status_id']) ? intval($_POST['status_id']) : IPT_STATUS_ID_PENDING;
    $order_id = isset($_POST['order_id']) ? intval($_POST['order_id']) : 0;

    // Validate required parameters
    if (!$plan_id || !$price) {
        wp_send_json_error(['message' => 'Missing required parameters: plan_id and price']);
        return;
    }

    // Get order object if order_id is provided
    $order = null;
    if ($order_id) {
        $order = wc_get_order($order_id);
    }

    // Create transaction
    $result = ipt_home_create_transaction($customer_id, $plan_id, $price, $status_id, $order);

    if ($result['success']) {
        wp_send_json_success($result);
    } else {
        wp_send_json_error($result);
    }
}
add_action('wp_ajax_create_transaction', 'ipt_home_create_transaction_ajax');
add_action('wp_ajax_nopriv_create_transaction', 'ipt_home_create_transaction_ajax');

/**
 * Create transaction when WooCommerce order payment is completed
 */
function ipt_home_woocommerce_payment_complete($order_id, $transaction_id = '') {
    $order = wc_get_order($order_id);

    if (!$order) {
        error_log('Order not found: ' . $order_id);
        return;
    }

    // Only process Stripe payments
    if ($order->get_payment_method() !== 'stripe') {
        return;
    }

    // Check if transaction already created for this order
    $existing_transaction = $order->get_meta('_ipt_transaction_id');
    if ($existing_transaction) {
        error_log('Transaction already exists for order: ' . $order_id);
        return;
    }

    $user_id = $order->get_user_id();
    if (!$user_id) {
        error_log('No user ID found for order: ' . $order_id);
        return;
    }

    // Get customer_id from user meta (saved during GraphQL login)
    $customer_id = ipt_home_get_customer_id_from_user($user_id);
    if (!$customer_id) {
        error_log('No customer_id found in user meta for order: ' . $order_id . ', user: ' . $user_id);
        return;
    }

    // Get order details
    $total = $order->get_total();
    $plan_id = null;

    // Get plan_id from subscription_plan_id meta of purchased products
    foreach ($order->get_items() as $item) {
        $product = $item->get_product();
        if ($product) {
            $subscription_plan_id = $product->get_meta('subscription_plan_id');
            if ($subscription_plan_id) {
                $plan_id = (int) $subscription_plan_id;
                break; // Use first product's subscription_plan_id
            }
        }
    }

    // If no subscription_plan_id found, skip transaction creation
    if (!$plan_id) {
        error_log('No subscription_plan_id found for order items in order: ' . $order_id);
        return;
    }

    // Create transaction with ACTIVE status since payment is completed
    $result = ipt_home_create_transaction($customer_id, $plan_id, $total, IPT_STATUS_ID_ACTIVE, $order);

    if ($result['success']) {
        // Store transaction ID in order meta
        $order->update_meta_data('_ipt_transaction_id', $result['transaction_id']);
        $order->save();

        error_log('Transaction created successfully for order ' . $order_id . ': ' . $result['transaction_id']);

        // Add order note
        $order->add_order_note(
            sprintf(
                __('Transaction created in external system. Transaction ID: %s', 'ipt-home'),
                $result['transaction_id']
            )
        );
    } else {
        error_log('Failed to create transaction for order ' . $order_id . ': ' . print_r($result, true));

        // Add order note about failure
        $order->add_order_note(
            __('Failed to create transaction in external system. Please check logs.', 'ipt-home')
        );
    }
}
add_action('woocommerce_payment_complete', 'ipt_home_woocommerce_payment_complete', 10, 2);

/**
 * Create transaction when order status changes to processing (for pending payments)
 */
function ipt_home_woocommerce_order_status_processing($order_id) {
    $order = wc_get_order($order_id);

    if (!$order) {
        return;
    }

    // Only process Stripe payments
    if ($order->get_payment_method() !== 'stripe') {
        return;
    }

    // Check if transaction already created
    $existing_transaction = $order->get_meta('_ipt_transaction_id');
    if ($existing_transaction) {
        return;
    }

    $user_id = $order->get_user_id();
    if (!$user_id) {
        return;
    }

    // Get customer_id from user meta (saved during GraphQL login)
    $customer_id = ipt_home_get_customer_id_from_user($user_id);
    if (!$customer_id) {
        error_log('No customer_id found in user meta for processing order: ' . $order_id . ', user: ' . $user_id);
        return;
    }

    // Get order details
    $total = $order->get_total();
    $plan_id = null;

    // Get plan_id from subscription_plan_id meta of purchased products
    foreach ($order->get_items() as $item) {
        $product = $item->get_product();
        if ($product) {
            $subscription_plan_id = $product->get_meta('subscription_plan_id');
            if ($subscription_plan_id) {
                $plan_id = (int) $subscription_plan_id;
                break; // Use first product's subscription_plan_id
            }
        }
    }

    // If no subscription_plan_id found, skip transaction creation
    if (!$plan_id) {
        error_log('No subscription_plan_id found for order items in order: ' . $order->get_id());
        return;
    }

    // Calculate extended end date if user has existing active plans
    $user_id = $order->get_user_id();
    if ($user_id && $total > 0) {
        // Get all user's orders to find existing active plans
        $user_orders = wc_get_orders([
            'customer_id' => $user_id,
            'status' => ['completed', 'processing'],
            'limit' => -1,
            'orderby' => 'date',
            'order' => 'DESC'
        ]);

        $existing_plan_end_date = null;
        foreach ($user_orders as $user_order) {
            // Skip the current order
            if ($user_order->get_id() === $order->get_id()) {
                continue;
            }

            // Check if this order has a plan
            if ($user_order->get_total() > 0) {
                // First check if this order has a saved extended end date
                $saved_extended_date = $user_order->get_meta('_extended_end_date');
                if ($saved_extended_date) {
                    $order_end_date = new DateTime($saved_extended_date);
                    error_log("Found order {$user_order->get_id()} with saved extended end date: " . $order_end_date->format('Y-m-d H:i:s'));
                } else {
                    // Calculate normal end date (order date + 30 days)
                    $order_end_date = clone $user_order->get_date_created();
                    $order_end_date->add(new DateInterval('P30D'));
                    error_log("Calculated normal end date for order {$user_order->get_id()}: " . $order_end_date->format('Y-m-d H:i:s'));
                }

                // Check if this plan is still active (not expired)
                $today = new DateTime();
                if ($order_end_date > $today) {
                    $existing_plan_end_date = $order_end_date;
                    error_log("Found existing plan for order {$order_id} ending on: " . $existing_plan_end_date->format('Y-m-d H:i:s'));
                    break; // Use the first (most recent) active plan found
                }
            }
        }

        // If we found an existing plan, calculate extended end date
        if ($existing_plan_end_date) {
            $extended_end_date = clone $existing_plan_end_date;
            $extended_end_date->add(new DateInterval('P30D'));

            // Save the extended end date to order meta
            $order->update_meta_data('_extended_end_date', $extended_end_date->format('Y-m-d H:i:s'));
            $order->save();

            error_log("ORDER PROCESSING DEBUG: Found existing plan ending {$existing_plan_end_date->format('d/m/Y')}, extended to {$extended_end_date->format('d/m/Y')} for order {$order_id}");

            $order->add_order_note(
                sprintf(
                    __('Extended subscription end date calculated: %s (extended from existing plan ending %s)', 'ipt-home'),
                    $extended_end_date->format('d/m/Y'),
                    $existing_plan_end_date->format('d/m/Y')
                )
            );
        } else {
            error_log("ORDER PROCESSING DEBUG: No existing active plan found for order {$order_id}, will use normal 30-day calculation");
        }
    }

    // Create transaction with PENDING status for processing orders
    $result = ipt_home_create_transaction($customer_id, $plan_id, $total, IPT_STATUS_ID_PENDING, $order);

    if ($result['success']) {
        $order->update_meta_data('_ipt_transaction_id', $result['transaction_id']);
        $order->save();

        error_log('Transaction created (PENDING) for processing order ' . $order_id . ': ' . $result['transaction_id']);

        $order->add_order_note(
            sprintf(
                __('Transaction created in external system (PENDING). Transaction ID: %s', 'ipt-home'),
                $result['transaction_id']
            )
        );
    }
}
add_action('woocommerce_order_status_processing', 'ipt_home_woocommerce_order_status_processing');

/**
 * Update transaction status when order is completed
 */
function ipt_home_woocommerce_order_status_completed($order_id) {
    $order = wc_get_order($order_id);

    if (!$order) {
        return;
    }

    // Only process Stripe payments
    if ($order->get_payment_method() !== 'stripe') {
        return;
    }

    // Get existing transaction ID
    $transaction_id = $order->get_meta('_ipt_transaction_id');
    if (!$transaction_id) {
        // If no transaction exists, create one with ACTIVE status
        ipt_home_woocommerce_payment_complete($order_id);
        return;
    }

    // TODO: Add function to update transaction status to ACTIVE
    // This would require a GraphQL mutation to update existing transaction
    error_log('Order completed, transaction should be updated to ACTIVE: ' . $transaction_id);

    $order->add_order_note(
        sprintf(
            __('Transaction status updated to ACTIVE. Transaction ID: %s', 'ipt-home'),
            $transaction_id
        )
    );
}
add_action('woocommerce_order_status_completed', 'ipt_home_woocommerce_order_status_completed');

/**
 * Hook into WooCommerce Stripe Gateway webhook processing
 * This leverages the existing webhook handling from the Stripe plugin
 */
function ipt_home_stripe_gateway_webhook_handler($order) {
    if (!$order || !is_a($order, 'WC_Order')) {
        return;
    }

    // Check if transaction already created
    $existing_transaction = $order->get_meta('_ipt_transaction_id');
    if ($existing_transaction) {
        return;
    }

    $user_id = $order->get_user_id();
    if (!$user_id) {
        return;
    }

    // Get customer_id from user meta (saved during GraphQL login)
    $customer_id = ipt_home_get_customer_id_from_user($user_id);
    if (!$customer_id) {
        error_log('No customer_id found in user meta for webhook order: ' . $order->get_id() . ', user: ' . $user_id);
        return;
    }

    // Get order details
    $total = $order->get_total();
    $plan_id = null;

    // Get plan_id from subscription_plan_id meta of purchased products
    foreach ($order->get_items() as $item) {
        $product = $item->get_product();
        if ($product) {
            $subscription_plan_id = $product->get_meta('subscription_plan_id');
            if ($subscription_plan_id) {
                $plan_id = (int) $subscription_plan_id;
                break; // Use first product's subscription_plan_id
            }
        }
    }

    // If no subscription_plan_id found, skip transaction creation
    if (!$plan_id) {
        error_log('No subscription_plan_id found for order items in order: ' . $order->get_id());
        return;
    }

    // Create transaction with ACTIVE status since webhook indicates successful payment
    $result = ipt_home_create_transaction($customer_id, $plan_id, $total, IPT_STATUS_ID_ACTIVE, $order);

    if ($result['success']) {
        $order->update_meta_data('_ipt_transaction_id', $result['transaction_id']);
        $order->save();

        error_log('Transaction created via Stripe webhook for order ' . $order->get_id() . ': ' . $result['transaction_id']);
    }
}
add_action('wc_gateway_stripe_process_webhook_payment', 'ipt_home_stripe_gateway_webhook_handler', 10, 1);

/**
 * Handle Stripe payment success redirect
 */
function ipt_home_handle_stripe_payment_success() {
    // Check if this is a Stripe payment success redirect
    if (isset($_GET['payment_intent']) && isset($_GET['redirect_status']) && $_GET['redirect_status'] === 'succeeded') {
        // Check if user is logged in
        if (!is_user_logged_in()) {
            // If not logged in, redirect to login page
            wp_redirect(wp_login_url());
            exit;
        }

        // Check if this is a domain payment by looking at the current URL
        $current_url = $_SERVER['REQUEST_URI'];
        if (strpos($current_url, '/customer/create_domain_payment/') !== false) {
            // This is a domain payment, let the domain payment page handle the redirect
            return;
        }

        // Check if we're already on the subscription page to avoid infinite redirects
        if (isset($_GET['customer_page']) && $_GET['customer_page'] === 'subscription') {
            return;
        }

        // Check if we're on the dashboard page and need to redirect to subscription
        if (isset($_GET['customer_page']) && $_GET['customer_page'] === 'dashboard') {
            // Preserve payment parameters for success message
            $redirect_url = add_query_arg(array(
                'customer_page' => 'subscription',
                'payment_intent' => $_GET['payment_intent'],
                'redirect_status' => $_GET['redirect_status']
            ), home_url());

            // Remove payment_intent_client_secret for security
            wp_redirect($redirect_url);
            exit;
        }

        // If no customer_page is set, redirect to subscription page (only for non-domain payments)
        if (!isset($_GET['customer_page'])) {
            $redirect_url = add_query_arg(array(
                'customer_page' => 'subscription',
                'payment_intent' => $_GET['payment_intent'],
                'redirect_status' => $_GET['redirect_status']
            ), home_url());

            wp_redirect($redirect_url);
            exit;
        }
    }
}
add_action('template_redirect', 'ipt_home_handle_stripe_payment_success', 5);

/**
 * Create subscription plan via GraphQL API when WooCommerce product is created
 */
function ipt_home_create_subscription_plan($product_id) {
    $product = wc_get_product($product_id);

    if (!$product) {
        error_log('Product not found: ' . $product_id);
        return false;
    }

    // Check if subscription plan already created for this product
    $existing_plan_id = $product->get_meta('subscription_plan_id');
    if ($existing_plan_id) {
        error_log('Subscription plan already exists for product: ' . $product_id . ', plan ID: ' . $existing_plan_id);
        return false;
    }

    // Prepare subscription plan data - ensure all required fields have values
    $name = $product->get_name() ?: 'Untitled Product';
    $desc = $product->get_description() ?: $product->get_short_description() ?: 'No description available';

    // Get price information from WooCommerce product
    $price = (float) $product->get_price() ?: 0.0; // Current selling price (after discounts)
    $original_price = (float) $product->get_regular_price() ?: $price; // Original price (before discounts)

    $status_id = $product->get_status() === 'publish' ? 2 : 1; // 2 for active, 1 for inactive

    // Get custom meta fields
    $subscription_type_id = $product->get_meta('subscription_type_id') ?: '1'; // Default to Trial
    $subscription_trial_days = (int) $product->get_meta('subscription_trial_days') ?: 0;
    $subscription_display_order = (int) $product->get_meta('subscription_display_order') ?: 1;

    // Use subscription_display_order if set, otherwise fallback to menu_order
    $display_order = $subscription_display_order ?: $product->get_menu_order() ?: 0;
    $type_id = (int) $subscription_type_id; // Use subscription_type_id from meta

    // GraphQL mutation - all fields are required (!)
    $mutation = '
        mutation Webhooks_subscription_plan_create(
            $name: String!,
            $desc: String!,
            $status_id: Int!,
            $price: Float!,
            $original_price: Float,
            $display_order: Int!,
            $type_id: Int!,
            $trial_days: Int!
        ) {
            webhooks_subscription_plan_create(
                body: {
                    name: $name
                    desc: $desc
                    status_id: $status_id
                    price: $price
                    original_price: $original_price
                    display_order: $display_order
                    type_id: $type_id
                    trial_days: $trial_days
                }
            ) {
                id
            }
        }
    ';

    // Variables for the mutation
    $variables = [
        'name' => $name,
        'desc' => $desc,
        'status_id' => $status_id,
        'price' => $price,
        'original_price' => $original_price,
        'display_order' => $display_order,
        'type_id' => $type_id,
        'trial_days' => $subscription_trial_days
    ];

    try {
        // Call GraphQL API
        $result = ipt_home_fetch_graphql_data($mutation, $variables);

        // Check for both possible response formats
        $plan_id = null;
        if (isset($result['data']['webhooks_subscription_plan_create']['id'])) {
            $plan_id = $result['data']['webhooks_subscription_plan_create']['id'];
        } elseif (isset($result['data']['subscription_plan_create']['id'])) {
            $plan_id = $result['data']['subscription_plan_create']['id'];
        }

        if ($plan_id) {
            // Store subscription plan ID in product meta (use direct meta update to avoid recursion)
            update_post_meta($product_id, 'subscription_plan_id', $plan_id);
            update_post_meta($product_id, '_ipt_sync_created', current_time('mysql'));

            // Store current field values for future change detection
            ipt_home_store_product_sync_state($product_id);

            error_log('Subscription plan created successfully for product ' . $product_id . ': ' . $plan_id);

            return [
                'success' => true,
                'plan_id' => $plan_id,
                'data' => $result['data']
            ];
        } else {
            error_log('Failed to create subscription plan for product ' . $product_id . ': ' . print_r($result, true));
            return [
                'success' => false,
                'error' => 'Failed to create subscription plan',
                'data' => $result
            ];
        }
    } catch (Exception $e) {
        error_log('Exception creating subscription plan for product ' . $product_id . ': ' . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Hook into WooCommerce product creation
 */
function ipt_home_woocommerce_product_created($product_id) {
    try {
        // Only sync published products
        $product = wc_get_product($product_id);
        if (!$product || $product->get_status() !== 'publish') {
            return;
        }

        // Create subscription plan
        $result = ipt_home_create_subscription_plan($product_id);

        if ($result && $result['success']) {
            // Add product note about subscription plan creation
            $product->add_meta_data('_ipt_sync_note', 'Subscription plan created: ' . $result['plan_id']);
            $product->save();
        } else {
            // Log failure but don't block product creation
            error_log('Failed to create subscription plan for product ' . $product_id . ', but product creation continues');
            $product->add_meta_data('_ipt_sync_note', 'Subscription plan creation failed - check logs');
            $product->save();
        }
    } catch (Exception $e) {
        // Ensure WooCommerce product creation is not blocked by any errors
        error_log('Exception in product creation hook for product ' . $product_id . ': ' . $e->getMessage());
    }
}
add_action('woocommerce_new_product', 'ipt_home_woocommerce_product_created');

/**
 * Hook into WooCommerce product status change (draft to publish)
 */
function ipt_home_woocommerce_product_status_changed($new_status, $old_status, $post) {
    // Only sync when product becomes published and it's a product post type
    if ($new_status === 'publish' && $old_status !== 'publish' && $post->post_type === 'product') {
        $product_id = $post->ID;

        // Get WooCommerce product object
        $product = wc_get_product($product_id);
        if (!$product) {
            return;
        }

        // Check if subscription plan already exists
        $existing_plan_id = $product->get_meta('subscription_plan_id');
        if (!$existing_plan_id) {
            // Create subscription plan
            $result = ipt_home_create_subscription_plan($product_id);

            if ($result && $result['success']) {
                // Add product note about subscription plan creation (use direct meta update to avoid recursion)
                update_post_meta($product_id, '_ipt_sync_note', 'Subscription plan created on publish: ' . $result['plan_id']);
            }
        }
    }
}
add_action('transition_post_status', 'ipt_home_woocommerce_product_status_changed', 10, 3);

/**
 * Update subscription plan via GraphQL API when WooCommerce product is updated
 */
function ipt_home_update_subscription_plan($product_id) {
    $product = wc_get_product($product_id);

    if (!$product) {
        error_log('Product not found: ' . $product_id);
        return false;
    }

    // Get existing subscription plan ID
    $plan_id = $product->get_meta('subscription_plan_id');
    if (!$plan_id) {
        // If no plan exists, create one
        return ipt_home_create_subscription_plan($product_id);
    }

    // Prepare updated data - ensure all required fields have values
    $name = $product->get_name() ?: 'Untitled Product';
    $desc = $product->get_description() ?: $product->get_short_description() ?: 'No description available';
    $price = (float) $product->get_price() ?: 0.0;
    $regular_price = (float) $product->get_regular_price() ?: $price;
    $woo_subscription_id = $product->get_id();

    $status_id = $product->get_status() === 'publish' ? 2 : 1; // 2 for active, 1 for inactive

    // Get custom meta fields
    $subscription_type_id = $product->get_meta('subscription_type_id') ?: '1';
    $subscription_trial_days = (int) $product->get_meta('subscription_trial_days') ?: 0;
    $subscription_display_order = (int) $product->get_meta('subscription_display_order') ?: 1;

    // Use subscription_display_order if set, otherwise fallback to menu_order
    $display_order = $subscription_display_order ?: $product->get_menu_order() ?: 0;

    // GraphQL mutation for updating subscription plan
    $mutation = '
        mutation Webhooks_subscription_plan_update(
            $id: Int!,
            $name: String!,
            $desc: String!,
            $price: Float!,
            $original_price: Float!,
            $woo_subscription_id: String,
            $display_order: Int!,
            $type_id: Int!,
            $trial_days: Int!
            $status_id: Int!
        ) {
            webhooks_subscription_plan_update(
                id: $id
                body: {
                    name: $name
                    desc: $desc
                    price: $price
                    original_price: $original_price
                    woo_subscription_id: $woo_subscription_id
                    display_order: $display_order
                    type_id: $type_id
                    trial_days: $trial_days
                    status_id: $status_id
                }
            ) {
                id
            }
        }
    ';

    // Variables for the mutation
    $variables = [
        'id' => (int) $plan_id,
        'name' => $name,
        'desc' => $desc,
        'price' => $price,
        'original_price' => $regular_price,
        'woo_subscription_id' => (string)$woo_subscription_id,
        'status_id' => $status_id,
        'display_order' => $display_order,
        'type_id' => (int) $subscription_type_id,
        'trial_days' => $subscription_trial_days
    ];

    try {
        // Call GraphQL API
        $result = ipt_home_fetch_graphql_data($mutation, $variables);

        // Check for both possible response formats
        $updated_plan_id = null;
        if (isset($result['data']['webhooks_subscription_plan_update']['id'])) {
            $updated_plan_id = $result['data']['webhooks_subscription_plan_update']['id'];
        } elseif (isset($result['data']['subscription_plan_update']['id'])) {
            $updated_plan_id = $result['data']['subscription_plan_update']['id'];
        }

        if ($updated_plan_id) {
            // Update sync timestamp (use direct meta update to avoid recursion)
            update_post_meta($product_id, '_ipt_sync_updated', current_time('mysql'));

            // Store current field values for future change detection
            ipt_home_store_product_sync_state($product_id);

            error_log('Subscription plan updated successfully for product ' . $product_id . ': ' . $updated_plan_id);

            return [
                'success' => true,
                'plan_id' => $updated_plan_id,
                'data' => $result['data']
            ];
        } else {
            error_log('Failed to update subscription plan for product ' . $product_id . ': ' . print_r($result, true));
            return [
                'success' => false,
                'error' => 'Failed to update subscription plan',
                'data' => $result
            ];
        }
    } catch (Exception $e) {
        error_log('Exception updating subscription plan for product ' . $product_id . ': ' . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Hook into WooCommerce product update
 */
function ipt_home_woocommerce_product_updated($product_id) {
    try {
        // Only sync published products
        $product = wc_get_product($product_id);
        if (!$product) {
            return;
        }

        // Check if we should skip this update (prevent duplicate calls)
        $transient_key = 'ipt_updating_product_' . $product_id;
        if (get_transient($transient_key)) {
            error_log('Skipping duplicate update for product ' . $product_id);
            return;
        }

        // Set transient to prevent duplicate calls for 30 seconds
        set_transient($transient_key, true, 30);

        // Check if relevant fields have changed
        if (!ipt_home_product_needs_sync($product_id)) {
            delete_transient($transient_key);
            return;
        }

        // Update subscription plan
        $result = ipt_home_update_subscription_plan($product_id);

        if ($result && $result['success']) {
            // Update product meta with sync info (use direct meta update to avoid recursion)
            update_post_meta($product_id, '_ipt_sync_updated', current_time('mysql'));

            // Store current field values for future change detection
            ipt_home_store_product_sync_state($product_id);
        } else {
            // Log failure but don't block product update
            error_log('Failed to update subscription plan for product ' . $product_id . ', but product update continues');
        }

        // Clear transient
        delete_transient($transient_key);

    } catch (Exception $e) {
        // Ensure WooCommerce product update is not blocked by any errors
        error_log('Exception in product update hook for product ' . $product_id . ': ' . $e->getMessage());

        // Clear transient on error
        delete_transient('ipt_updating_product_' . $product_id);
    }
}
add_action('woocommerce_update_product', 'ipt_home_woocommerce_product_updated');

/**
 * Check if product needs sync based on field changes
 */
function ipt_home_product_needs_sync($product_id) {
    $product = wc_get_product($product_id);
    if (!$product) {
        return false;
    }

    // Get current field values
    $current_values = [
        'name' => $product->get_name(),
        'description' => $product->get_description(),
        'short_description' => $product->get_short_description(),
        'price' => $product->get_price(),
        'regular_price' => $product->get_regular_price(),
        'status' => $product->get_status(),
        'subscription_type_id' => $product->get_meta('subscription_type_id'),
        'subscription_trial_days' => $product->get_meta('subscription_trial_days'),
        'subscription_display_order' => $product->get_meta('subscription_display_order'),
    ];

    // Get stored values from last sync
    $stored_values = get_post_meta($product_id, '_ipt_sync_state', true);

    // If no stored values, this is first sync
    if (empty($stored_values)) {
        return true;
    }

    // Compare current vs stored values
    foreach ($current_values as $key => $value) {
        if (!isset($stored_values[$key]) || $stored_values[$key] !== $value) {
            error_log("Product {$product_id} field '{$key}' changed: '{$stored_values[$key]}' → '{$value}'");
            return true;
        }
    }

    error_log("Product {$product_id} has no relevant changes, skipping sync");
    return false;
}

/**
 * Store current product field values for change detection
 */
function ipt_home_store_product_sync_state($product_id) {
    $product = wc_get_product($product_id);
    if (!$product) {
        return;
    }

    $current_values = [
        'name' => $product->get_name(),
        'description' => $product->get_description(),
        'short_description' => $product->get_short_description(),
        'price' => $product->get_price(),
        'regular_price' => $product->get_regular_price(),
        'status' => $product->get_status(),
        'subscription_type_id' => $product->get_meta('subscription_type_id'),
        'subscription_trial_days' => $product->get_meta('subscription_trial_days'),
        'subscription_display_order' => $product->get_meta('subscription_display_order'),
    ];

    update_post_meta($product_id, '_ipt_sync_state', $current_values);
}

/**
 * Delete subscription plan via GraphQL API when WooCommerce product is deleted
 */
function ipt_home_delete_subscription_plan($product_id) {
    $product = wc_get_product($product_id);

    if (!$product) {
        error_log('Product not found for deletion: ' . $product_id);
        return false;
    }

    // Get existing subscription plan ID
    $plan_id = $product->get_meta('subscription_plan_id');
    if (!$plan_id) {
        error_log('No subscription plan ID found for product: ' . $product_id);
        return false;
    }

    // GraphQL mutation for deleting subscription plan
    $mutation = '
        mutation Webhooks_subscription_plan_delete($id: Int!) {
            webhooks_subscription_plan_delete(id: $id)
        }
    ';

    // Variables for the mutation
    $variables = [
        'id' => (int) $plan_id
    ];

    try {
        // Call GraphQL API
        $result = ipt_home_fetch_graphql_data($mutation, $variables);

        // Check if deletion was successful
        if (isset($result['data']['webhooks_subscription_plan_delete']) ||
            isset($result['data']['subscription_plan_delete'])) {

            error_log('Subscription plan deleted successfully for product ' . $product_id . ': ' . $plan_id);

            return [
                'success' => true,
                'plan_id' => $plan_id,
                'data' => $result['data']
            ];
        } else {
            error_log('Failed to delete subscription plan for product ' . $product_id . ': ' . print_r($result, true));
            return [
                'success' => false,
                'error' => 'Failed to delete subscription plan',
                'data' => $result
            ];
        }
    } catch (Exception $e) {
        error_log('Exception deleting subscription plan for product ' . $product_id . ': ' . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Hook into WooCommerce product deletion
 */
function ipt_home_woocommerce_product_deleted($product_id) {
    // Delete subscription plan before product is completely removed
    $result = ipt_home_delete_subscription_plan($product_id);

    if ($result && $result['success']) {
        error_log('Subscription plan deletion completed for product: ' . $product_id);
    }
}
add_action('before_delete_post', 'ipt_home_woocommerce_product_deleted');

/**
 * Hook into WooCommerce product trash (soft delete)
 */
function ipt_home_woocommerce_product_trashed($product_id) {
    // Check if this is a product
    if (get_post_type($product_id) !== 'product') {
        return;
    }

    // Delete subscription plan when product is trashed
    $result = ipt_home_delete_subscription_plan($product_id);

    if ($result && $result['success']) {
        error_log('Subscription plan deleted for trashed product: ' . $product_id);
    }
}
add_action('wp_trash_post', 'ipt_home_woocommerce_product_trashed');

/**
 * AJAX handler for manual product sync
 */
function ipt_home_sync_product_ajax() {
    // Check permissions
    if (!current_user_can('manage_woocommerce')) {
        wp_send_json_error(['message' => 'Insufficient permissions']);
        return;
    }

    // Check nonce
    if (!wp_verify_nonce($_POST['nonce'], 'sync_product_nonce')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;

    if (!$product_id) {
        wp_send_json_error(['message' => 'Invalid product ID']);
        return;
    }

    $product = wc_get_product($product_id);
    if (!$product) {
        wp_send_json_error(['message' => 'Product not found']);
        return;
    }

    // Check if subscription plan already exists
    $existing_plan_id = $product->get_meta('subscription_plan_id');

    if ($existing_plan_id) {
        // Update existing plan
        $result = ipt_home_update_subscription_plan($product_id);
        $action = 'updated';
    } else {
        // Create new plan
        $result = ipt_home_create_subscription_plan($product_id);
        $action = 'created';
    }

    if ($result && $result['success']) {
        wp_send_json_success([
            'message' => 'Subscription plan ' . $action . ' successfully',
            'plan_id' => $result['plan_id'],
            'action' => $action
        ]);
    } else {
        wp_send_json_error([
            'message' => 'Failed to sync product',
            'error' => isset($result['error']) ? $result['error'] : 'Unknown error'
        ]);
    }
}
add_action('wp_ajax_sync_product', 'ipt_home_sync_product_ajax');

/**
 * AJAX handler for testing product update
 */
function ipt_home_test_update_product_ajax() {
    // Check permissions
    if (!current_user_can('manage_woocommerce')) {
        wp_send_json_error(['message' => 'Insufficient permissions']);
        return;
    }

    // Check nonce
    if (!wp_verify_nonce($_POST['nonce'], 'test_update_nonce')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;

    if (!$product_id) {
        wp_send_json_error(['message' => 'Invalid product ID']);
        return;
    }

    // Test update subscription plan
    $result = ipt_home_update_subscription_plan($product_id);

    if ($result && $result['success']) {
        wp_send_json_success([
            'message' => 'Subscription plan updated successfully',
            'plan_id' => $result['plan_id']
        ]);
    } else {
        wp_send_json_error([
            'message' => 'Failed to update subscription plan',
            'error' => isset($result['error']) ? $result['error'] : 'Unknown error'
        ]);
    }
}
add_action('wp_ajax_test_update_product', 'ipt_home_test_update_product_ajax');

/**
 * AJAX handler for testing product delete
 */
function ipt_home_test_delete_product_ajax() {
    // Check permissions
    if (!current_user_can('manage_woocommerce')) {
        wp_send_json_error(['message' => 'Insufficient permissions']);
        return;
    }

    // Check nonce
    if (!wp_verify_nonce($_POST['nonce'], 'test_delete_nonce')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;

    if (!$product_id) {
        wp_send_json_error(['message' => 'Invalid product ID']);
        return;
    }

    // Test delete subscription plan
    $result = ipt_home_delete_subscription_plan($product_id);

    if ($result && $result['success']) {
        wp_send_json_success([
            'message' => 'Subscription plan deleted successfully',
            'plan_id' => $result['plan_id']
        ]);
    } else {
        wp_send_json_error([
            'message' => 'Failed to delete subscription plan',
            'error' => isset($result['error']) ? $result['error'] : 'Unknown error'
        ]);
    }
}
add_action('wp_ajax_test_delete_product', 'ipt_home_test_delete_product_ajax');

/**
 * AJAX handler to get latest Stripe order for subscription page
 */
function ipt_home_get_latest_stripe_order_ajax() {
    // Check nonce
    if (!wp_verify_nonce($_POST['security'], 'ipt-subscription-nonce')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    // Check if user is logged in
    if (!is_user_logged_in()) {
        wp_send_json_error(['message' => 'User not logged in']);
        return;
    }

    $user_id = get_current_user_id();

    // Get ALL recent orders for current user (not just Stripe)
    $all_orders = wc_get_orders(array(
        'customer_id' => $user_id,
        'status' => array('completed', 'processing', 'on-hold', 'pending'), // Include all relevant statuses
        'limit' => 30, // Get more orders to analyze
        'orderby' => 'date',
        'order' => 'DESC'
    ));

    error_log('Found ' . count($all_orders) . ' total orders for user ' . $user_id);

    // Filter orders that have subscription products or are payment-related
    $relevant_orders = array();
    foreach ($all_orders as $order) {
        $has_subscription_product = false;
        $order_items = $order->get_items();

        foreach ($order_items as $item) {
            $product = $item->get_product();
            if ($product) {
                $subscription_type_id = $product->get_meta('subscription_type_id');
                if (!empty($subscription_type_id)) {
                    $has_subscription_product = true;
                    break;
                }
            }
        }

        // Include order if it has subscription products OR has payment method OR is recent
        $order_date = $order->get_date_created();
        $hours_ago = (time() - $order_date->getTimestamp()) / 3600;

        if ($has_subscription_product ||
            !empty($order->get_payment_method()) ||
            $hours_ago < 24) { // Include orders from last 24 hours
            $relevant_orders[] = $order;
        }
    }

    $orders = $relevant_orders;
    error_log('Filtered to ' . count($orders) . ' relevant orders');

    if (empty($orders)) {
        wp_send_json_error(['message' => 'No Stripe orders found']);
        return;
    }

    // Find the latest non-trial order first, fallback to latest order
    $selected_order = null;
    $latest_paid_order = null;
    $latest_trial_order = null;

    error_log('Analyzing ' . count($orders) . ' orders for user ' . $user_id);

    foreach ($orders as $order) {
        $is_trial_order = $order->get_meta('_is_trial_order') === 'yes';
        $order_total = floatval($order->get_total());
        $order_id = $order->get_id();
        $order_date = $order->get_date_created()->format('Y-m-d H:i:s');

        error_log("Order #{$order_id} - Date: {$order_date}, Total: {$order_total}, Is Trial: " . ($is_trial_order ? 'yes' : 'no'));

        if (!$is_trial_order && $order_total > 0) {
            // This is a paid order (not trial)
            if (!$latest_paid_order) {
                $latest_paid_order = $order;
                error_log("Found latest paid order: #{$order_id}");
            }
        } elseif ($is_trial_order && $order_total == 0) {
            // This is a trial order
            if (!$latest_trial_order) {
                $latest_trial_order = $order;
                error_log("Found latest trial order: #{$order_id}");
            }
        } elseif ($order_total > 0) {
            // This is a paid order without trial meta (could be upgraded trial)
            if (!$latest_paid_order) {
                $latest_paid_order = $order;
                error_log("Found latest paid order (no trial meta): #{$order_id}");
            }
        }
    }

    // Priority: Latest paid order > Latest trial order > Latest order
    if ($latest_paid_order) {
        $selected_order = $latest_paid_order;
        error_log('Using latest paid order: ' . $selected_order->get_id() . ' (Total: ' . $selected_order->get_total() . ')');
    } elseif ($latest_trial_order) {
        $selected_order = $latest_trial_order;
        error_log('Using latest trial order: ' . $selected_order->get_id() . ' (Total: ' . $selected_order->get_total() . ')');
    } else {
        $selected_order = $orders[0];
        error_log('Using latest order (fallback): ' . $selected_order->get_id() . ' (Total: ' . $selected_order->get_total() . ')');
    }

    $order = $selected_order;

    // Get order details
    $order_data = array(
        'order_id' => $order->get_id(),
        'status' => $order->get_status(),
        'total' => $order->get_total(),
        'currency' => $order->get_currency(),
        'date_created' => $order->get_date_created()->format('Y-m-d H:i:s'),
        'payment_method' => $order->get_payment_method_title(),
        'amount' => wc_price($order->get_total()),
    );

    // Get subscription plan info from order items
    $plan_name = 'Unknown Plan';
    $subscription_plan_id = null;
    $is_trial_order = false;
    $trial_days = 0;
    $subscription_type_id = null;

    // Check if this is a trial order
    $is_trial_order = $order->get_meta('_is_trial_order') === 'yes';
    $trial_days = (int) $order->get_meta('_trial_days');

    foreach ($order->get_items() as $item) {
        $product = $item->get_product();
        if ($product) {
            $plan_name = $product->get_name();
            $subscription_plan_id = $product->get_meta('subscription_plan_id');

            // Get subscription type
            $subscription_type_id = $product->get_meta('subscription_type_id');
            $type_labels = ['1' => 'Trial', '2' => 'Basic', '3' => 'Premium'];

            error_log("Order #{$order->get_id()} - Product: {$plan_name}, Type ID: {$subscription_type_id}, Order Total: {$order->get_total()}, Is Trial Order: " . ($is_trial_order ? 'yes' : 'no'));

            // Only add type label for actual trial orders or when explicitly needed
            if ($subscription_type_id && isset($type_labels[$subscription_type_id])) {
                // For trial products: only show "(Trial)" if it's actually a trial order with $0 total
                if ($subscription_type_id == '1') {
                    if ($is_trial_order && $order->get_total() == 0) {
                        $plan_name .= ' (' . $type_labels[$subscription_type_id] . ')';
                        error_log("Added (Trial) label to plan name");
                    } else {
                        error_log("Skipped (Trial) label - not a trial order or has payment");
                    }
                    // For paid trial products, don't add "(Trial)" - they're upgraded
                } else {
                    // For Basic/Premium products, always show the type
                    $plan_name .= ' (' . $type_labels[$subscription_type_id] . ')';
                    error_log("Added ({$type_labels[$subscription_type_id]}) label to plan name");
                }
            }

            // If not trial order but product is trial type, get trial days from product
            if (!$is_trial_order && $subscription_type_id == '1') {
                $trial_days = (int) $product->get_meta('subscription_trial_days');
            }

            // Check if plan is cancelled
            $is_plan_cancelled = $order->get_meta('_plan_cancelled') === 'yes';

            // Override plan name based on order total for paid orders
            if (!$is_trial_order && $order->get_total() > 0) {
                $total = floatval($order->get_total());
                error_log("Overriding plan name based on order total: $" . $total);

                // Determine the original plan name first
                if ($total >= 50 && $total <= 60) {
                    $original_plan_name = 'Basic Plan';
                    error_log("Original plan: Basic Plan");
                } elseif ($total >= 90 && $total <= 110) {
                    $original_plan_name = 'Premium Plan';
                    error_log("Original plan: Premium Plan");
                } else {
                    // Keep original name but remove (Trial) if present
                    $original_plan_name = str_replace(' (Trial)', '', $plan_name);
                    error_log("Original plan name: " . $original_plan_name);
                }

                // For cancelled plans, check if expired
                if ($is_plan_cancelled) {
                    // Check if plan has expired
                    $renewal_date_check = clone $order->get_date_created();
                    $renewal_date_check->add(new DateInterval('P30D'));
                    $today = new DateTime();

                    if ($renewal_date_check <= $today) {
                        // Plan has expired - make it empty
                        $plan_name = '';
                        error_log("Plan is cancelled and expired - showing empty plan name");
                    } else {
                        // Plan still has time remaining - show original plan name
                        $plan_name = $original_plan_name;
                        error_log("Plan is cancelled but still active - showing original plan name: " . $plan_name);
                    }
                } else {
                    $plan_name = $original_plan_name;
                    error_log("Plan is active - showing: " . $plan_name);
                }
            } elseif ($is_plan_cancelled) {
                // Handle cancelled trial orders - keep original trial name
                $plan_name = str_replace(' (Trial)', '', $plan_name);
                error_log("Trial plan is cancelled - showing original trial name: " . $plan_name);
            }

            error_log("Final plan name: {$plan_name}");

            break; // Use first product
        }
    }

    // Calculate renewal/expiry date based on trial status
    // Only consider it trial if it's actually a trial order (with $0 total) OR if it's a trial product with unpaid trial
    $is_active_trial = false;

    if ($is_trial_order && $order->get_total() == 0) {
        // This is an actual trial order with $0 total
        $is_active_trial = true;

        // Use trial end date from order meta if available
        $trial_end_date = $order->get_meta('_trial_end_date');
        if ($trial_end_date) {
            $renewal_date = new DateTime($trial_end_date);
        } else {
            // Fallback: calculate from order date + trial days
            $renewal_date = $order->get_date_created();
            $renewal_date->add(new DateInterval('P' . $trial_days . 'D'));
        }
        $renewal_label = 'Trial Expires';

    } elseif ($subscription_type_id == '1' && $trial_days > 0 && $order->get_total() == 0) {
        // This is a trial product with $0 order (unpaid trial)
        $is_active_trial = true;
        $renewal_date = $order->get_date_created();
        $renewal_date->add(new DateInterval('P' . $trial_days . 'D'));
        $renewal_label = 'Trial Expires';

    } else {
        // This is a paid order (even if it's a trial product that was upgraded)
        $is_active_trial = false;

        // Check if user has an existing active plan to extend from
        $existing_plan_end_date = null;
        $user_id = get_current_user_id();

        if ($user_id) {
            // Get all user's orders to find the latest active plan
            $user_orders = wc_get_orders([
                'customer_id' => $user_id,
                'status' => ['completed', 'processing'],
                'limit' => -1,
                'orderby' => 'date',
                'order' => 'DESC'
            ]);

            foreach ($user_orders as $user_order) {
                // Skip the current order we're processing
                if ($user_order->get_id() === $order->get_id()) {
                    continue;
                }

                // Check if this order has a plan (cancelled or active)
                if ($user_order->get_total() > 0) {
                    // First check if this order has a saved extended end date
                    $saved_extended_date = $user_order->get_meta('_extended_end_date');
                    if ($saved_extended_date) {
                        $order_end_date = new DateTime($saved_extended_date);
                        error_log("Found order {$user_order->get_id()} with saved extended end date: " . $order_end_date->format('Y-m-d H:i:s'));
                    } else {
                        // Calculate normal end date (order date + 30 days)
                        $order_end_date = clone $user_order->get_date_created();
                        $order_end_date->add(new DateInterval('P30D'));
                        error_log("Calculated normal end date for order {$user_order->get_id()}: " . $order_end_date->format('Y-m-d H:i:s'));
                    }

                    // Check if this plan is still active OR was recently cancelled with remaining time
                    $today = new DateTime();
                    $is_cancelled = $user_order->get_meta('_plan_cancelled') === 'yes';

                    if ($order_end_date > $today) {
                        // Plan is still active (not expired)
                        $existing_plan_end_date = $order_end_date;
                        error_log("Found existing plan ending on: " . $existing_plan_end_date->format('Y-m-d H:i:s') . " (cancelled: " . ($is_cancelled ? 'yes' : 'no') . ")");
                        break; // Use the first (latest) plan found with remaining time
                    }
                }
            }
        }

        // Set renewal date based on whether we found an existing active plan
        if ($existing_plan_end_date) {
            // Extend from the end of existing plan
            $renewal_date = clone $existing_plan_end_date;
            $renewal_date->add(new DateInterval('P30D'));
            error_log("EXTENSION DEBUG: Extending from existing plan end date: " . $existing_plan_end_date->format('d/m/Y') . " to new end date: " . $renewal_date->format('d/m/Y'));

            // Save the extended end date as order meta for future reference
            $order->update_meta_data('_extended_end_date', $renewal_date->format('Y-m-d H:i:s'));
            $order->save();
            error_log("EXTENSION DEBUG: Saved extended end date to order meta: " . $renewal_date->format('d/m/Y'));
        } else {
            // Check if this order already has an extended end date saved
            $saved_extended_date = $order->get_meta('_extended_end_date');
            if ($saved_extended_date) {
                $renewal_date = new DateTime($saved_extended_date);
                error_log("EXTENSION DEBUG: Using saved extended end date: " . $renewal_date->format('d/m/Y'));
            } else {
                // No existing active plan, start from order creation date
                $renewal_date = $order->get_date_created();
                $renewal_date->add(new DateInterval('P30D'));
                error_log("EXTENSION DEBUG: No existing plan found. Starting from order date: " . $order->get_date_created()->format('d/m/Y') . " to: " . $renewal_date->format('d/m/Y'));
            }
        }

        // Check if plan is cancelled and calculate remaining days
        if ($is_plan_cancelled) {
            // Calculate remaining days for cancelled plans
            $today = new DateTime();
            $days_remaining = $today->diff($renewal_date)->days;

            if ($renewal_date > $today) {
                if ($days_remaining > 0) {
                    $renewal_label = $days_remaining . ' days remaining';
                    error_log("Cancelled plan has {$days_remaining} days remaining");
                } else {
                    // Less than 1 day remaining
                    $renewal_label = 'Expires today';
                    error_log("Cancelled plan expires today");
                }
            } else {
                // Plan has expired - make it empty
                $renewal_label = '';
                error_log("Cancelled plan has expired - showing empty");
            }
        } else {
            $renewal_label = 'Expiration Date';
        }
    }

    // Get transaction ID if exists
    $transaction_id = $order->get_meta('_ipt_transaction_id');

    // Prepare debug info for all orders
    $debug_orders = array();
    foreach ($orders as $debug_order) {
        $debug_orders[] = array(
            'id' => $debug_order->get_id(),
            'date' => $debug_order->get_date_created()->format('Y-m-d H:i:s'),
            'total' => $debug_order->get_total(),
            'status' => $debug_order->get_status(),
            'payment_method' => $debug_order->get_payment_method(),
            'is_trial_meta' => $debug_order->get_meta('_is_trial_order'),
        );
    }

    // Prepare response data
    $response_data = array(
        'plan_name' => $plan_name,
        'renewal_date' => $renewal_date->format('d/m/Y'),
        'renewal_label' => $renewal_label,
        'is_trial' => $is_active_trial, // Use the corrected trial detection
        'trial_days' => $trial_days,
        'amount' => wc_price($order->get_total()),
        'payment_method' => $order->get_payment_method_title() ?: 'Stripe',
        'order_id' => $order->get_id(),
        'subscription_plan_id' => $subscription_plan_id,
        'transaction_id' => $transaction_id,
        'status' => ucfirst($order->get_status()),
        'order_date' => $order->get_date_created()->format('d/m/Y'),
        'next_billing' => $renewal_date->format('d/m/Y'),
        'order_total' => $order->get_total(), // Add order total for debugging
        'is_trial_order_meta' => $is_trial_order ? 'yes' : 'no', // Add for debugging
        'subscription_type_id' => $subscription_type_id, // Add for debugging
        'is_plan_cancelled' => $is_plan_cancelled, // Add cancelled status
        'expiration_date' => $renewal_date->format('d/m/Y'), // Same as renewal_date but clearer for cancelled plans
        'debug_orders' => $debug_orders, // Add all orders for debugging
        'selected_order_logic' => array(
            'latest_paid_order_id' => $latest_paid_order ? $latest_paid_order->get_id() : null,
            'latest_trial_order_id' => $latest_trial_order ? $latest_trial_order->get_id() : null,
            'selected_order_id' => $order->get_id(),
            'total_orders_found' => count($orders)
        )
    );

    wp_send_json_success($response_data);
}
add_action('wp_ajax_ipt_get_latest_stripe_order', 'ipt_home_get_latest_stripe_order_ajax');

/**
 * Mark user as having used trial (prevent future trial access)
 */
function ipt_home_mark_trial_used($user_id) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }

    if ($user_id) {
        update_user_meta($user_id, '_ipt_trial_used', true);
        update_user_meta($user_id, '_ipt_trial_used_date', current_time('mysql'));
        error_log("Trial usage marked for user {$user_id}");
    }
}

/**
 * Check if user has already used trial
 */
function ipt_home_has_used_trial($user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }

    if (!$user_id) {
        return false;
    }

    $trial_used = get_user_meta($user_id, '_ipt_trial_used', true);
    return !empty($trial_used);
}

/**
 * Handle empty cart and custom redirect for add to cart from pricing page
 */
function ipt_home_handle_pricing_add_to_cart() {
    // Only process if we have add-to-cart parameter
    if (!isset($_GET['add-to-cart']) || !is_numeric($_GET['add-to-cart'])) {
        return;
    }

    error_log('Processing add-to-cart request: product_id=' . $_GET['add-to-cart']);

    // Handle empty cart parameter
    if (isset($_GET['empty-cart']) && $_GET['empty-cart'] == '1') {
        WC()->cart->empty_cart();
        error_log('Cart emptied before adding new product');
    }

    // Handle custom redirect parameter
    if (isset($_GET['redirect'])) {
        $redirect_url = urldecode($_GET['redirect']);


        // Validate redirect URL is from our domain
        $parsed_url = parse_url($redirect_url);
        $site_url = parse_url(site_url());

        error_log('🔍 Parsed redirect URL: ' . print_r($parsed_url, true));
        error_log('🔍 Site URL: ' . print_r($site_url, true));

        if ($parsed_url && isset($parsed_url['host']) && $parsed_url['host'] === $site_url['host']) {
            // Store redirect URL in session for after add to cart
            WC()->session->set('ipt_custom_redirect', $redirect_url);
            error_log('✅ Custom redirect URL stored in session: ' . $redirect_url);
        } else {
            error_log('❌ Invalid redirect URL - domain mismatch');
            error_log('Expected host: ' . (isset($site_url['host']) ? $site_url['host'] : 'NONE'));
            error_log('Actual host: ' . (isset($parsed_url['host']) ? $parsed_url['host'] : 'NONE'));

        }
    }

    // Add product to cart immediately since we're not on a product page
    $product_id = intval($_GET['add-to-cart']);
    $quantity = 1;

    error_log('🛒 Attempting to add product to cart: ' . $product_id);
    $added = WC()->cart->add_to_cart($product_id, $quantity);

    if ($added) {
        error_log('✅ Product added to cart successfully: ' . $product_id);

        // Check if we have a custom redirect
        $custom_redirect = WC()->session->get('ipt_custom_redirect');
        error_log('🔍 Checking for custom redirect in session: ' . ($custom_redirect ? $custom_redirect : 'NONE'));

        if ($custom_redirect) {
            error_log('🚀 Redirecting to custom URL: ' . $custom_redirect);
            WC()->session->__unset('ipt_custom_redirect');
            wp_redirect($custom_redirect);
            exit;
        } else {
            error_log('⚠️ No custom redirect found in session');
        }
    } else {
        error_log('❌ Failed to add product to cart: ' . $product_id);

    }
}
add_action('woocommerce_before_add_to_cart_form', 'ipt_home_handle_pricing_add_to_cart');
add_action('init', 'ipt_home_handle_pricing_add_to_cart', 5);

/**
 * Custom redirect after add to cart success
 */
function ipt_home_custom_add_to_cart_redirect($url) {
    // Check if we have a custom redirect stored
    $custom_redirect = WC()->session->get('ipt_custom_redirect');

    if ($custom_redirect) {
        // Clear the session
        WC()->session->__unset('ipt_custom_redirect');

        // Return custom redirect URL
        return $custom_redirect;
    }

    return $url;
}
add_filter('woocommerce_add_to_cart_redirect', 'ipt_home_custom_add_to_cart_redirect');

/**
 * Force redirect after add to cart when custom redirect is set
 */
function ipt_home_force_add_to_cart_redirect() {
    // Check if we have add-to-cart and redirect parameters
    if (isset($_GET['add-to-cart']) && isset($_GET['redirect'])) {
        // Enable cart redirect temporarily
        add_filter('option_woocommerce_cart_redirect_after_add', function($value) {
            return 'yes';
        });
    }
}
add_action('init', 'ipt_home_force_add_to_cart_redirect', 1);

/**
 * Allow free products (price = 0) to be added to cart
 */
function ipt_home_allow_free_products_in_cart($passed, $product_id, $quantity) {
    $product = wc_get_product($product_id);

    if (!$product) {
        return $passed;
    }

    // Allow products with 0 price to be added to cart
    if ($product->get_price() === '' || $product->get_price() == 0) {
        error_log("Allowing free product to be added to cart: Product ID {$product_id}, Price: " . $product->get_price());
        return true;
    }

    return $passed;
}
add_filter('woocommerce_add_to_cart_validation', 'ipt_home_allow_free_products_in_cart', 10, 3);

/**
 * Allow checkout for free products (price = 0)
 */
function ipt_home_allow_free_checkout($passed, $fields, $errors) {
    // Check if cart contains only free products
    $cart_total = WC()->cart->get_total('raw');

    if ($cart_total == 0) {
        error_log("Allowing checkout for free cart. Cart total: " . $cart_total);
        return true;
    }

    return $passed;
}
add_filter('woocommerce_checkout_process', 'ipt_home_allow_free_checkout', 10, 3);

/**
 * Skip payment for free products
 */
function ipt_home_skip_payment_for_free_products() {
    // Check if WooCommerce and cart are available
    if (!function_exists('WC') || !WC() || !WC()->cart) {
        return;
    }

    if (WC()->cart->get_total('raw') == 0) {
        // Remove payment gateways for free orders
        add_filter('woocommerce_available_payment_gateways', function($gateways) {
            return array(); // No payment gateways needed for free orders
        });

        error_log("Skipping payment for free cart");
    }
}
add_action('woocommerce_checkout_init', 'ipt_home_skip_payment_for_free_products');

/**
 * Handle free product checkout on create_payment page
 */
function ipt_home_handle_free_product_checkout() {
    // Check if we're on the create_payment page
    if (isset($_GET['customer_page']) && $_GET['customer_page'] === 'create_payment') {
        // Check if WooCommerce and cart are available
        if (!function_exists('WC') || !WC() || !WC()->cart) {
            return;
        }

        // Check if cart is empty or has free products
        if (WC()->cart->is_empty()) {
            error_log("Cart is empty on create_payment page, redirecting to pricing");
            wp_redirect(home_url('/pricing/'));
            exit;
        }

        $cart_total = WC()->cart->get_total('raw');
        if ($cart_total == 0) {
            error_log("Free product detected on create_payment page, processing free order");

            // Create order for free product
            $order = wc_create_order();

            // Add cart items to order
            foreach (WC()->cart->get_cart() as $cart_item) {
                $product = $cart_item['data'];
                $quantity = $cart_item['quantity'];
                $order->add_product($product, $quantity);
            }

            // Set order status to completed for free orders
            $order->set_status('completed');
            $order->save();

            // Empty cart
            WC()->cart->empty_cart();

            // Redirect to subscription page
            wp_redirect(home_url('/?customer_page=subscription&order_id=' . $order->get_id()));
            exit;
        }
    }
}
add_action('template_redirect', 'ipt_home_handle_free_product_checkout', 5);

/**
 * Auto-assign free trial product to new registered customers
 */
function ipt_home_auto_assign_trial_to_new_customer($user_id) {
    // Only for users with customer role
    $user = get_user_by('id', $user_id);
    if (!$user || !in_array('customer', $user->roles)) {
        return;
    }

    // Check if user already has orders (prevent duplicate assignment)
    $existing_orders = wc_get_orders(array(
        'customer_id' => $user_id,
        'limit' => 1
    ));

    if (!empty($existing_orders)) {
        error_log("⚠️ User {$user_id} already has orders, skipping auto-assignment");
        return;
    }

    error_log("🎯 Auto-assigning trial to new customer: User ID {$user_id}");

    // Find free product by SKU
    $free_product_id = wc_get_product_id_by_sku('free');
    if (!$free_product_id) {
        error_log("❌ Free product (SKU: 'free') not found, skipping auto-assignment");
        return;
    }

    error_log("✅ Found free product ID: {$free_product_id}");

    $free_product = wc_get_product($free_product_id);
    if (!$free_product) {
        error_log("❌ Free product object not found for ID: {$free_product_id}");
        return;
    }

    // Check if product is published (we'll bypass purchasable check for auto-assignment)
    if ($free_product->get_status() !== 'publish') {
        error_log("❌ Free product not published. Status: " . $free_product->get_status());
        return;
    }

    error_log("✅ Found free product: ID {$free_product_id}, Name: " . $free_product->get_name() . ", Status: " . $free_product->get_status() . ", Price: " . $free_product->get_price());

    // Create WooCommerce order for the new customer
    error_log("🛒 Creating WooCommerce order for user: {$user_id}");

    try {
        $order = wc_create_order(array(
            'customer_id' => $user_id,
            'status' => 'completed'
        ));

        if (is_wp_error($order)) {
            error_log("❌ Failed to create order for user {$user_id}: " . $order->get_error_message());
            return;
        }

        error_log("✅ Order created successfully: Order ID " . $order->get_id());

    } catch (Exception $e) {
        error_log("❌ Exception creating order for user {$user_id}: " . $e->getMessage());
        return;
    }

    // Add free product to order
    $order->add_product($free_product, 1);

    // Set order details
    $order->set_address(array(
        'first_name' => $user->first_name ?: $user->display_name,
        'last_name'  => $user->last_name ?: '',
        'email'      => $user->user_email,
    ), 'billing');

    // Calculate totals and save order
    $order->calculate_totals();
    $order->save();

    error_log("✅ Auto-assigned trial order created: Order ID {$order->get_id()} for User ID {$user_id}");

    // Add order note
    $order->add_order_note('Auto-assigned trial subscription for new customer registration.');
}

/**
 * Hook auto-assignment to user registration (regular registration)
 * NOTE: Disabled because we use custom AJAX registration
 */
// function ipt_home_auto_assign_trial_on_registration($user_id) {
//     error_log("🔥 user_register hook fired for user ID: {$user_id}");
//     ipt_home_auto_assign_trial_to_new_customer($user_id);
// }
// add_action('user_register', 'ipt_home_auto_assign_trial_on_registration');

/**
 * Additional hook for WooCommerce customer registration
 */
function ipt_home_auto_assign_trial_on_wc_registration($customer_id, $new_customer_data, $password_generated) {
    error_log("🔥 woocommerce_created_customer hook fired for customer ID: {$customer_id}");
    ipt_home_auto_assign_trial_to_new_customer($customer_id);
}
add_action('woocommerce_created_customer', 'ipt_home_auto_assign_trial_on_wc_registration', 10, 3);

/**
 * Test function to check free product configuration
 * Call this in browser: yoursite.com/?test_free_product=1
 */
function ipt_home_test_free_product() {
    if (isset($_GET['test_free_product']) && current_user_can('administrator')) {
        echo "<h2>Free Product Test</h2>";

        // Find free product by SKU
        $free_product_id = wc_get_product_id_by_sku('free');
        echo "<p>Free product ID: " . ($free_product_id ? $free_product_id : 'NOT FOUND') . "</p>";

        if ($free_product_id) {
            $free_product = wc_get_product($free_product_id);
            if ($free_product) {
                echo "<p>Product Name: " . $free_product->get_name() . "</p>";
                echo "<p>Product Status: " . $free_product->get_status() . "</p>";
                echo "<p>Product Price: " . $free_product->get_price() . "</p>";
                echo "<p>Is Purchasable: " . ($free_product->is_purchasable() ? 'YES' : 'NO') . "</p>";
                echo "<p>Trial Days: " . $free_product->get_meta('subscription_trial_days') . "</p>";
                echo "<p>Subscription Type ID: " . $free_product->get_meta('subscription_type_id') . "</p>";
            } else {
                echo "<p>Product object not found</p>";
            }
        }

        exit;
    }
}
add_action('init', 'ipt_home_test_free_product');

/**
 * Tạo Stripe Payment Intent sử dụng WC_Stripe_API
 */
function ipt_create_payment_intent() {
    // Kiểm tra nonce bảo mật
    check_ajax_referer('ipt-payment-nonce', 'security');

    try {
        // Kiểm tra xem WC_Stripe_API đã được load chưa
        if (!class_exists('WC_Stripe_API')) {
            // Load WC_Stripe_API
            $stripe_api_path = WP_PLUGIN_DIR . '/woocommerce-gateway-stripe/includes/class-wc-stripe-api.php';
            if (file_exists($stripe_api_path)) {
                require_once($stripe_api_path);
                error_log('Loaded WC_Stripe_API');
            } else {
                error_log('Could not find WC_Stripe_API');
                throw new Exception('WooCommerce Stripe Gateway API not found.');
            }
        }

        // Kiểm tra xem Stripe Gateway đã được kích hoạt chưa
        $stripe_settings = get_option('woocommerce_stripe_settings');
        if (!isset($stripe_settings['enabled']) || $stripe_settings['enabled'] !== 'yes') {
            error_log('Stripe Gateway is not enabled');
            throw new Exception('Stripe payment gateway is not enabled.');
        }

        // Kiểm tra API keys
        $test_mode = isset($stripe_settings['testmode']) && $stripe_settings['testmode'] === 'yes';
        $secret_key = $test_mode ?
            (isset($stripe_settings['test_secret_key']) ? $stripe_settings['test_secret_key'] : '') :
            (isset($stripe_settings['secret_key']) ? $stripe_settings['secret_key'] : '');

        if (empty($secret_key)) {
            error_log('Stripe Secret Key is not set');
            throw new Exception('Stripe API keys are not properly configured.');
        }

        // Lấy tổng giỏ hàng
        $total = WC()->cart->get_total('');
        error_log('Cart Total: ' . $total);

        // Kiểm tra giá trị giỏ hàng
        if (empty($total) || $total <= 0) {
            error_log('Invalid cart total: ' . $total);
            throw new Exception('Invalid cart total. Please add items to your cart.');
        }

        $currency = get_woocommerce_currency();
        error_log('Currency: ' . $currency);

        // Check if cart contains trial subscription
        $is_trial_subscription = false;
        $trial_days = 0;

        foreach (WC()->cart->get_cart() as $cart_item) {
            $product = $cart_item['data'];
            $subscription_type_id = $product->get_meta('subscription_type_id');
            $subscription_trial_days = (int) $product->get_meta('subscription_trial_days');

            if ($subscription_type_id == '1' && $subscription_trial_days > 0) {
                $is_trial_subscription = true;
                $trial_days = $subscription_trial_days;
                error_log('Trial subscription detected: ' . $trial_days . ' days');
                break;
            }
        }

        // Chuyển đổi tổng thành cents/smallest currency unit
        $amount = round($total * 100);
        error_log('Amount in cents: ' . $amount);

        // Handle trial subscriptions differently
        if ($is_trial_subscription && $trial_days > 0) {
            // For trial subscriptions, create a setup intent instead of payment intent
            // This will save payment method without charging
            $request = [
                'payment_method_types' => ['card'],
                'usage' => 'off_session',
                'metadata' => [
                    'order_id' => 'trial_cart_' . WC()->session->get_customer_id(),
                    'customer_id' => get_current_user_id(),
                    'trial_days' => $trial_days,
                    'amount' => $amount,
                    'currency' => strtolower($currency),
                ],
                'description' => 'Trial setup for ' . get_bloginfo('name'),
            ];

            // Thêm thông tin khách hàng nếu đã đăng nhập
            $current_user = wp_get_current_user();
            if ($current_user->ID) {
                // Add customer info to metadata instead of customer field
                $request['metadata']['customer_email'] = $current_user->user_email;
                $request['metadata']['customer_name'] = $current_user->display_name;
            }

            error_log('Setup Intent Request for Trial: ' . print_r($request, true));

            // Đặt API key cho Stripe
            WC_Stripe_API::set_secret_key($secret_key);

            $response = WC_Stripe_API::request($request, 'setup_intents');

            if (!empty($response->error)) {
                error_log('Stripe Setup Intent Error: ' . print_r($response->error, true));
                throw new Exception($response->error->message);
            }

            error_log('Setup Intent created for trial: ' . $response->id);

            // Store trial information in session
            WC()->session->set('ipt_trial_setup_intent_id', $response->id);
            WC()->session->set('ipt_trial_days', $trial_days);
            WC()->session->set('ipt_trial_amount', $amount);

            // Return setup intent client secret
            wp_send_json_success([
                'client_secret' => $response->client_secret,
                'is_trial' => true,
                'trial_days' => $trial_days,
                'amount_after_trial' => $total,
                'setup_intent_id' => $response->id,
            ]);

        } else {
            // Regular payment intent for non-trial subscriptions
            $request = [
                'amount' => $amount,
                'currency' => strtolower($currency),
                'payment_method_types' => ['card'],
                'metadata' => [
                    'order_id' => 'cart_' . WC()->session->get_customer_id(),
                    'customer_id' => get_current_user_id(),
                ],
                'description' => 'Order from ' . get_bloginfo('name'),
            ];

            // Thêm thông tin khách hàng nếu đã đăng nhập
            $current_user = wp_get_current_user();
            if ($current_user->ID) {
                $request['receipt_email'] = $current_user->user_email;
                // Add customer metadata instead of customer field
                $request['metadata']['customer_email'] = $current_user->user_email;
            }

            error_log('Payment Intent Request: ' . print_r($request, true));

            // Đặt API key cho Stripe
            WC_Stripe_API::set_secret_key($secret_key);

            $response = WC_Stripe_API::request($request, 'payment_intents');
        }

        if (!empty($response->error)) {
            error_log('Stripe API Error: ' . print_r($response->error, true));
            throw new Exception($response->error->message);
        }

        error_log('Payment Intent created: ' . $response->id);
        error_log('Client Secret: ' . (isset($response->client_secret) ? 'exists' : 'missing'));

        // Kiểm tra client_secret
        if (empty($response->client_secret)) {
            error_log('Client secret is missing in the response');
            throw new Exception('Client secret is missing in the response from Stripe.');
        }

        // Trả về client secret
        wp_send_json_success([
            'client_secret' => $response->client_secret,
        ]);
    } catch (Exception $e) {
        error_log('Stripe Payment Intent Error: ' . $e->getMessage());
        error_log('Error trace: ' . $e->getTraceAsString());
        wp_send_json_error([
            'message' => $e->getMessage(),
        ]);
    }

    wp_die();
}
add_action('wp_ajax_ipt_create_payment_intent', 'ipt_create_payment_intent');
add_action('wp_ajax_nopriv_ipt_create_payment_intent', 'ipt_create_payment_intent');

/**
 * AJAX handler to confirm payment and create order
 */
function ipt_home_confirm_payment_ajax() {
    // Check nonce
    if (!wp_verify_nonce($_POST['security'], 'ipt-payment-nonce')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    // Check if user is logged in
    if (!is_user_logged_in()) {
        wp_send_json_error(['message' => 'User not logged in']);
        return;
    }

    // Get payment intent ID
    $payment_intent_id = sanitize_text_field($_POST['payment_intent_id']);
    if (empty($payment_intent_id)) {
        wp_send_json_error(['message' => 'Payment intent ID is required']);
        return;
    }

    // Check if cart has items
    if (WC()->cart->is_empty()) {
        wp_send_json_error(['message' => 'Cart is empty']);
        return;
    }

    try {
        // Get Stripe gateway
        $stripe_gateway = new WC_Gateway_Stripe();
        $secret_key = $stripe_gateway->get_option('secret_key');

        if (empty($secret_key)) {
            wp_send_json_error(['message' => 'Stripe not configured']);
            return;
        }

        // Set Stripe API key
        \Stripe\Stripe::setApiKey($secret_key);

        // Retrieve payment intent from Stripe
        $payment_intent = \Stripe\PaymentIntent::retrieve($payment_intent_id);

        // Check if payment was successful
        if ($payment_intent->status !== 'succeeded') {
            wp_send_json_error(['message' => 'Payment not completed. Status: ' . $payment_intent->status]);
            return;
        }

        // Create WooCommerce order
        $order = wc_create_order();

        // Add cart items to order
        foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
            $product = $cart_item['data'];
            $quantity = $cart_item['quantity'];

            $order->add_product($product, $quantity);
        }

        // Set order details
        $order->set_customer_id(get_current_user_id());
        $order->set_payment_method('stripe');
        $order->set_payment_method_title('Stripe');
        $order->set_transaction_id($payment_intent->id);

        // Calculate totals
        $order->calculate_totals();

        // Add order meta
        $order->add_meta_data('_stripe_payment_intent_id', $payment_intent_id);
        $order->add_meta_data('_stripe_charge_id', isset($payment_intent->latest_charge) ? $payment_intent->latest_charge : '');

        // Set order status to processing
        $order->update_status('processing', 'Payment completed via Stripe');

        // Save order
        $order->save();

        // Empty cart
        WC()->cart->empty_cart();

        // Clear payment intent from session
        WC()->session->__unset('ipt_payment_intent_id');

        // Trigger WooCommerce payment complete action
        do_action('woocommerce_payment_complete', $order->get_id());

        wp_send_json_success([
            'order_id' => $order->get_id(),
            'order_key' => $order->get_order_key(),
            'redirect_url' => site_url('/?customer_page=subscription')
        ]);

    } catch (\Stripe\Exception\ApiErrorException $e) {
        error_log('Stripe API Error in confirm payment: ' . $e->getMessage());
        wp_send_json_error(['message' => 'Stripe error: ' . $e->getMessage()]);
    } catch (Exception $e) {
        error_log('Payment Confirmation Error: ' . $e->getMessage());
        wp_send_json_error(['message' => 'Could not confirm payment: ' . $e->getMessage()]);
    }
}
add_action('wp_ajax_ipt_confirm_payment', 'ipt_home_confirm_payment_ajax');

/**
 * AJAX handler to confirm trial setup and create trial order
 */
function ipt_home_confirm_trial_setup_ajax() {
    // Check nonce
    if (!wp_verify_nonce($_POST['security'], 'ipt-payment-nonce')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    // Check if user is logged in
    if (!is_user_logged_in()) {
        wp_send_json_error(['message' => 'User not logged in']);
        return;
    }

    // Get setup intent ID
    $setup_intent_id = sanitize_text_field($_POST['setup_intent_id']);
    if (empty($setup_intent_id)) {
        wp_send_json_error(['message' => 'Setup intent ID is required']);
        return;
    }

    // Check if cart has items
    if (WC()->cart->is_empty()) {
        wp_send_json_error(['message' => 'Cart is empty']);
        return;
    }

    try {
        // Get trial information from session
        $trial_days = WC()->session->get('ipt_trial_days');
        $trial_amount = WC()->session->get('ipt_trial_amount');

        if (!$trial_days || !$trial_amount) {
            wp_send_json_error(['message' => 'Trial information not found']);
            return;
        }

        // Create WooCommerce order for trial
        $order = wc_create_order();

        // Add cart items to order
        foreach (WC()->cart->get_cart() as $cart_item) {
            $product = $cart_item['data'];
            $quantity = $cart_item['quantity'];

            $order->add_product($product, $quantity);
        }

        // Set order details
        $order->set_customer_id(get_current_user_id());
        $order->set_payment_method('stripe');
        $order->set_payment_method_title('Stripe (Trial)');
        $order->set_transaction_id($setup_intent_id);

        // Set order total to 0 for trial
        $order->set_total(0);

        // Add order meta for trial
        $order->add_meta_data('_stripe_setup_intent_id', $setup_intent_id);
        $order->add_meta_data('_trial_days', $trial_days);
        $order->add_meta_data('_trial_amount', $trial_amount / 100); // Convert back from cents
        $order->add_meta_data('_trial_end_date', date('Y-m-d H:i:s', strtotime('+' . $trial_days . ' days')));
        $order->add_meta_data('_is_trial_order', 'yes');

        // Set order status to processing (trial active)
        $order->update_status('processing', 'Trial period started for ' . $trial_days . ' days');

        // Save order
        $order->save();

        // Empty cart
        WC()->cart->empty_cart();

        // Clear trial information from session
        WC()->session->__unset('ipt_trial_setup_intent_id');
        WC()->session->__unset('ipt_trial_days');
        WC()->session->__unset('ipt_trial_amount');

        // Schedule payment after trial period (using WordPress cron)
        $trial_end_timestamp = strtotime('+' . $trial_days . ' days');
        wp_schedule_single_event($trial_end_timestamp, 'ipt_process_trial_end_payment', array($order->get_id()));

        // Mark user as having used trial
        ipt_home_mark_trial_used(get_current_user_id());

        // Trigger custom action for trial start
        do_action('ipt_trial_started', $order->get_id(), $trial_days);

        wp_send_json_success([
            'order_id' => $order->get_id(),
            'order_key' => $order->get_order_key(),
            'trial_days' => $trial_days,
            'trial_end_date' => date('Y-m-d', $trial_end_timestamp),
            'redirect_url' => site_url('/?customer_page=subscription')
        ]);

    } catch (Exception $e) {
        error_log('Trial Setup Confirmation Error: ' . $e->getMessage());
        wp_send_json_error(['message' => 'Could not confirm trial setup: ' . $e->getMessage()]);
    }
}
add_action('wp_ajax_ipt_confirm_trial_setup', 'ipt_home_confirm_trial_setup_ajax');

/**
 * Process payment when trial period ends
 */
function ipt_home_process_trial_end_payment($order_id) {
    $order = wc_get_order($order_id);

    if (!$order || $order->get_meta('_is_trial_order') !== 'yes') {
        error_log('Invalid trial order for payment processing: ' . $order_id);
        return;
    }

    try {
        $setup_intent_id = $order->get_meta('_stripe_setup_intent_id');
        $trial_amount = $order->get_meta('_trial_amount');

        if (!$setup_intent_id || !$trial_amount) {
            error_log('Missing trial payment information for order: ' . $order_id);
            return;
        }

        // Get Stripe settings
        $stripe_settings = get_option('woocommerce_stripe_settings');
        $test_mode = isset($stripe_settings['testmode']) && $stripe_settings['testmode'] === 'yes';
        $secret_key = $test_mode ?
            $stripe_settings['test_secret_key'] :
            $stripe_settings['secret_key'];

        if (empty($secret_key)) {
            error_log('Stripe secret key not found for trial payment processing');
            return;
        }

        // Load WC_Stripe_API
        if (!class_exists('WC_Stripe_API')) {
            $stripe_api_path = WP_PLUGIN_DIR . '/woocommerce-gateway-stripe/includes/class-wc-stripe-api.php';
            if (file_exists($stripe_api_path)) {
                require_once($stripe_api_path);
            }
        }

        WC_Stripe_API::set_secret_key($secret_key);

        // Retrieve setup intent to get payment method
        $setup_intent = WC_Stripe_API::request([], 'setup_intents/' . $setup_intent_id);

        if (empty($setup_intent->payment_method)) {
            error_log('No payment method found in setup intent: ' . $setup_intent_id);
            return;
        }

        // Create payment intent with saved payment method
        $payment_request = [
            'amount' => round($trial_amount * 100), // Convert to cents
            'currency' => strtolower(get_woocommerce_currency()),
            'payment_method' => $setup_intent->payment_method,
            'confirmation_method' => 'manual',
            'confirm' => true,
            'off_session' => true,
            'metadata' => [
                'order_id' => $order_id,
                'trial_end_payment' => 'yes',
            ],
            'description' => 'Trial end payment for order #' . $order_id,
        ];

        $payment_intent = WC_Stripe_API::request($payment_request, 'payment_intents');

        if (!empty($payment_intent->error)) {
            error_log('Trial end payment failed for order ' . $order_id . ': ' . $payment_intent->error->message);

            // Update order status to failed
            $order->update_status('failed', 'Trial end payment failed: ' . $payment_intent->error->message);

            // Send notification to customer
            do_action('ipt_trial_payment_failed', $order_id, $payment_intent->error->message);

        } else if ($payment_intent->status === 'succeeded') {
            // Payment successful
            $order->set_total($trial_amount);
            $order->set_transaction_id($payment_intent->id);
            $order->add_meta_data('_stripe_payment_intent_id', $payment_intent->id);
            $order->update_status('completed', 'Trial end payment completed');
            $order->save();

            error_log('Trial end payment successful for order: ' . $order_id);

            // Mark user as having used trial (in case not already marked)
            ipt_home_mark_trial_used($order->get_user_id());

            // Trigger payment complete actions
            do_action('woocommerce_payment_complete', $order_id);
            do_action('ipt_trial_payment_completed', $order_id);

        } else {
            error_log('Trial end payment status not succeeded for order ' . $order_id . ': ' . $payment_intent->status);
            $order->update_status('on-hold', 'Trial end payment requires action: ' . $payment_intent->status);
        }

    } catch (Exception $e) {
        error_log('Exception in trial end payment processing for order ' . $order_id . ': ' . $e->getMessage());
        $order->update_status('failed', 'Trial end payment exception: ' . $e->getMessage());
    }
}
add_action('ipt_process_trial_end_payment', 'ipt_home_process_trial_end_payment');

/**
 * Add custom subscription meta fields to WooCommerce product edit page
 */
function ipt_home_add_subscription_meta_fields() {
    global $post;

    echo '<div class="options_group">';

    // Subscription Type ID
    woocommerce_wp_select(array(
        'id' => 'subscription_type_id',
        'label' => __('Subscription Type', 'woocommerce'),
        'description' => __('Select the subscription type for this product.', 'woocommerce'),
        'desc_tip' => true,
        'options' => array(
            '1' => __('Trial', 'woocommerce'),
            '2' => __('Basic', 'woocommerce'),
            '3' => __('Premium', 'woocommerce')
        ),
        'value' => get_post_meta($post->ID, 'subscription_type_id', true)
    ));

    // Subscription Trial Days
    woocommerce_wp_text_input(array(
        'id' => 'subscription_trial_days',
        'label' => __('Trial Days', 'woocommerce'),
        'description' => __('Number of trial days for this subscription.', 'woocommerce'),
        'desc_tip' => true,
        'type' => 'number',
        'custom_attributes' => array(
            'min' => '0',
            'step' => '1'
        ),
        'value' => get_post_meta($post->ID, 'subscription_trial_days', true)
    ));

    // Subscription Display Order
    woocommerce_wp_text_input(array(
        'id' => 'subscription_display_order',
        'label' => __('Display Order', 'woocommerce'),
        'description' => __('Display order for this subscription (0 or higher).', 'woocommerce'),
        'desc_tip' => true,
        'type' => 'number',
        'custom_attributes' => array(
            'min' => '0',
            'step' => '1'
        ),
        'value' => get_post_meta($post->ID, 'subscription_display_order', true) ?: '1'
    ));

    echo '</div>';
}
add_action('woocommerce_product_options_general_product_data', 'ipt_home_add_subscription_meta_fields');

/**
 * Save custom subscription meta fields
 */
function ipt_home_save_subscription_meta_fields($post_id) {
    // Subscription Type ID
    if (isset($_POST['subscription_type_id'])) {
        $subscription_type_id = sanitize_text_field($_POST['subscription_type_id']);
        if (in_array($subscription_type_id, ['1', '2', '3'])) {
            update_post_meta($post_id, 'subscription_type_id', $subscription_type_id);
        }
    }

    // Subscription Trial Days
    if (isset($_POST['subscription_trial_days'])) {
        $trial_days = intval($_POST['subscription_trial_days']);
        if ($trial_days >= 0) {
            update_post_meta($post_id, 'subscription_trial_days', $trial_days);
        }
    }

    // Subscription Display Order
    if (isset($_POST['subscription_display_order'])) {
        $display_order = intval($_POST['subscription_display_order']);
        if ($display_order >= 0) {
            update_post_meta($post_id, 'subscription_display_order', $display_order);
        }
    }
}
add_action('woocommerce_process_product_meta', 'ipt_home_save_subscription_meta_fields');

/**
 * Add custom column to WooCommerce products list
 */
function ipt_home_add_product_sync_column($columns) {
    $columns['ipt_sync_status'] = 'Subscription Plan';
    return $columns;
}
add_filter('manage_edit-product_columns', 'ipt_home_add_product_sync_column');

/**
 * Display sync status in custom column
 */
function ipt_home_display_product_sync_column($column, $post_id) {
    if ($column === 'ipt_sync_status') {
        $product = wc_get_product($post_id);
        if ($product) {
            $plan_id = $product->get_meta('subscription_plan_id');
            $sync_note = $product->get_meta('_ipt_sync_note');
            $sync_updated = $product->get_meta('_ipt_sync_updated');

            if ($plan_id) {
                echo '<span style="color: green;">✓ Synced</span><br>';
                echo '<small>Plan ID: ' . esc_html($plan_id) . '</small>';
                if ($sync_updated) {
                    echo '<br><small>Updated: ' . esc_html(date('Y-m-d H:i', strtotime($sync_updated))) . '</small>';
                }
            } else {
                echo '<span style="color: red;">✗ Not Synced</span>';
            }

            // Add sync button
            echo '<br><button type="button" class="button button-small sync-product-btn" data-product-id="' . esc_attr($post_id) . '">';
            echo $plan_id ? 'Re-sync' : 'Sync Now';
            echo '</button>';

            if ($sync_note) {
                echo '<br><small title="' . esc_attr($sync_note) . '">ℹ️ ' . esc_html(substr($sync_note, 0, 30)) . '...</small>';
            }
        }
    }
}
add_action('manage_product_posts_custom_column', 'ipt_home_display_product_sync_column', 10, 2);

/**
 * Add JavaScript for sync buttons
 */
function ipt_home_product_sync_admin_script() {
    $screen = get_current_screen();
    if ($screen && $screen->id === 'edit-product') {
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            $('.sync-product-btn').click(function() {
                var button = $(this);
                var productId = button.data('product-id');
                var originalText = button.text();

                button.text('Syncing...').prop('disabled', true);

                $.post(ajaxurl, {
                    action: 'sync_product',
                    product_id: productId,
                    nonce: '<?php echo wp_create_nonce('sync_product_nonce'); ?>'
                }, function(response) {
                    if (response.success) {
                        button.closest('td').html(
                            '<span style="color: green;">✓ Synced</span><br>' +
                            '<small>Plan ID: ' + response.data.plan_id + '</small><br>' +
                            '<button type="button" class="button button-small sync-product-btn" data-product-id="' + productId + '">Re-sync</button>'
                        );

                        // Show success message
                        $('<div class="notice notice-success is-dismissible"><p>' + response.data.message + '</p></div>')
                            .insertAfter('.wp-header-end').delay(3000).fadeOut();
                    } else {
                        button.text(originalText).prop('disabled', false);

                        // Show error message
                        $('<div class="notice notice-error is-dismissible"><p>Error: ' + response.data.message + '</p></div>')
                            .insertAfter('.wp-header-end').delay(5000).fadeOut();
                    }
                }).fail(function() {
                    button.text(originalText).prop('disabled', false);

                    // Show error message
                    $('<div class="notice notice-error is-dismissible"><p>Network error occurred</p></div>')
                        .insertAfter('.wp-header-end').delay(5000).fadeOut();
                });
            });
        });
        </script>
        <?php
    }
}
add_action('admin_footer', 'ipt_home_product_sync_admin_script');

/**
 * Clear all user subscription data (for testing/reset purposes)
 */
function ipt_home_clear_user_subscription_data() {
    // Check if user is logged in and is admin
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }

    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : get_current_user_id();

    // Clear user meta
    delete_user_meta($user_id, 'customer_id');
    delete_user_meta($user_id, '_ipt_has_used_trial');

    // Clear WooCommerce sessions
    if (class_exists('WC_Session_Handler')) {
        $session_handler = new WC_Session_Handler();
        $session_handler->destroy_session();
    }

    // Clear any cached data
    wp_cache_flush();

    error_log("Cleared subscription data for user {$user_id}");

    wp_send_json_success(['message' => 'User subscription data cleared successfully']);
}
add_action('wp_ajax_ipt_home_clear_user_data', 'ipt_home_clear_user_subscription_data');

/**
 * Redirect /my-account/ and WooCommerce account pages to customer dashboard
 */
function ipt_home_redirect_my_account() {
    // Get current URL path
    $current_path = trim(parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH), '/');

    // Check if we're on any my-account related page
    $account_pages = [
        'my-account',
        'my-account/',
        'my-account/dashboard',
        'my-account/orders',
        'my-account/downloads',
        'my-account/edit-address',
        'my-account/edit-account',
        'my-account/customer-logout',
        'my-account/lost-password',
        'my-account/reset-password'
    ];

    // Check if current path matches any account page
    $is_account_page = false;
    foreach ($account_pages as $page) {
        if ($current_path === $page || strpos($current_path, $page) === 0) {
            $is_account_page = true;
            break;
        }
    }

    // Also check WordPress functions
    if (!$is_account_page && (is_page('my-account') || is_wc_endpoint_url() || is_account_page())) {
        $is_account_page = true;
    }

    if ($is_account_page) {
        // Check if user is logged in
        if (is_user_logged_in()) {
            // Check user role and redirect accordingly
            if (current_user_can('administrator')) {
                // Redirect admin users to WordPress admin dashboard
                wp_safe_redirect(admin_url('index.php'));
                exit;
            } else {
                // Redirect regular users to customer dashboard
                wp_safe_redirect(home_url('/customer/dashboard/'));
                exit;
            }
        } else {
            // Redirect non-logged-in users to login page
            wp_safe_redirect(home_url('/login/'));
            exit;
        }
    }
}
add_action('template_redirect', 'ipt_home_redirect_my_account');

/**
 * Redirect logged-in users away from login/signup pages
 */
function ipt_home_redirect_logged_in_users() {
    // Check if user is logged in
    if (is_user_logged_in()) {
        // Check if we're on login or register pages
        if (is_page_template('page-login.php') || is_page_template('page-register.php') ||
            is_page('login') || is_page('register') || is_page('sign-in') || is_page('sign-up')) {

            // Redirect all logged-in users to homepage
            wp_safe_redirect(home_url('/'));
            exit;
        }
    }
}
add_action('template_redirect', 'ipt_home_redirect_logged_in_users');

/**
 * Generate password that meets GraphQL API requirements
 */
function generate_api_compliant_password($length = 12) {
    // Characters sets
    $lowercase = 'abcdefghijklmnopqrstuvwxyz';
    $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $numbers = '**********';
    $special = '!@#$%^&*()_+-=[]{}|;:,.<>?';

    // Ensure we have at least one of each required type
    $password = '';
    $password .= $lowercase[random_int(0, strlen($lowercase) - 1)]; // At least one lowercase
    $password .= $uppercase[random_int(0, strlen($uppercase) - 1)]; // At least one uppercase
    $password .= $numbers[random_int(0, strlen($numbers) - 1)];     // At least one number
    $password .= $special[random_int(0, strlen($special) - 1)];     // At least one special character

    // Fill the rest with random characters from all sets
    $all_chars = $lowercase . $uppercase . $numbers . $special;
    for ($i = 4; $i < $length; $i++) {
        $password .= $all_chars[random_int(0, strlen($all_chars) - 1)];
    }

    // Shuffle the password to randomize the order
    return str_shuffle($password);
}

/**
 * Sync user to GraphQL API (for Firebase social login)
 */
function sync_user_to_graphql_api($email, $display_name, $provider = 'social') {
    // Generate a password that meets API requirements:
    // - 12 characters
    // - Contains numbers and characters
    // - Contains at least one special character
    $password = generate_api_compliant_password(12);

    error_log("Firebase Social Login: Generated API-compliant password for {$email} (length: " . strlen($password) . ")");

    // Call GraphQL auth_register mutation
    $query = '
        mutation Auth_register($body: RegisterInputDto!) {
            auth_register(
                body: $body
            )
        }
    ';

    $variables = [
        'body' => [
            'first_name' => $display_name,
            'last_name' => $display_name,
            'email' => $email,
            'password' => $password,
            'confirm_password' => $password,
            'role_id' => 2 // Customer role
        ]
    ];

    // Make GraphQL API call
    $response = ipt_home_fetch_graphql_data($query, $variables);

    if (isset($response['data']['auth_register'])) {
        error_log("Firebase Social Login: Successfully synced user {$email} to GraphQL API via {$provider}");
        return ['success' => true, 'message' => 'User synced to API'];
    } else {
        $error_message = 'Unknown error';
        if (isset($response['errors']) && is_array($response['errors'])) {
            $error_message = isset($response['errors'][0]['message']) ? $response['errors'][0]['message'] : 'GraphQL error';
        }
        error_log("Firebase Social Login: Failed to sync user {$email} to GraphQL API: {$error_message}");
        return ['success' => false, 'message' => $error_message];
    }
}

/**
 * Get customer_id from GraphQL API by email (for Firebase social login)
 */
function get_customer_id_from_graphql_login($email) {
    // For social login users, we need to query the API to find the customer by email
    // since they don't have a password for auth_login

    // Try to query customer by email
    $query = '
        query Customer_list($filters: [String!]) {
            customer_list(body: { filters: $filters }) {
                id
                email
            }
        }
    ';

    $variables = [
        'filters' => ["email:eq({$email})"]
    ];

    // Make GraphQL API call
    $response = ipt_home_fetch_graphql_data($query, $variables);

    if (isset($response['data']['customer_list']) && !empty($response['data']['customer_list'])) {
        $customer = $response['data']['customer_list'][0];
        $customer_id = $customer['id'];
        error_log("Firebase Social Login: Found customer_id {$customer_id} for {$email}");
        return $customer_id;
    } else {
        error_log("Firebase Social Login: No customer found for {$email} in GraphQL API");
        return null;
    }
}

/**
 * Add admin menu for clearing user data
 */
function ipt_home_add_admin_menu() {
    add_management_page(
        'Clear User Data',
        'Clear User Data',
        'manage_options',
        'ipt-clear-user-data',
        'ipt_home_admin_clear_user_data_page'
    );
}
add_action('admin_menu', 'ipt_home_add_admin_menu');

/**
 * Admin page callback
 */
function ipt_home_admin_clear_user_data_page() {
    include get_stylesheet_directory() . '/admin-clear-user-data.php';
}


// Manual test - visit: yoursite.com/?manual_test_trial=USER_ID
function manual_test_trial() {
    if (isset($_GET['manual_test_trial']) && current_user_can('administrator')) {
        $user_id = intval($_GET['manual_test_trial']);
        echo "<h2>Manual Trial Test for User ID: {$user_id}</h2>";
        
        if (function_exists('ipt_home_auto_assign_trial_to_new_customer')) {
            ipt_home_auto_assign_trial_to_new_customer($user_id);
            echo "<p>Auto-assignment function called. Check error logs for details.</p>";
        } else {
            echo "<p>Auto-assignment function not found!</p>";
        }
        exit;
    }
}
add_action('init', 'manual_test_trial');

/**
 * Get the appropriate URL for the "Get Started" button
 * Returns sign-up page for non-logged users, dashboard for logged users
 */
function get_started_button_url() {
    if (is_user_logged_in()) {
        return site_url('/customer/dashboard/');
    } else {
        return site_url('/sign-up/');
    }
}