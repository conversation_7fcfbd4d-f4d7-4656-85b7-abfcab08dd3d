# Production Setup Guide - Using IPT_API_KEY

## 🚀 **Simple Server-to-Server API Calls**

### **Step 1: Use Your Existing IPT_API_KEY**
The system now uses your existing `IPT_API_KEY` constant:
```php
define('IPT_API_KEY', 'ASiOTwM9W9ToZ0zOSDsMF1OQYUmRumys');
```

This ensures consistency across all your systems and no need to manage separate API keys.

### **Step 2: Production Setup cURL Commands**

#### **Method 1: Using API Key Header (Recommended)**
```bash
# Complete production setup
curl -X POST "https://yoursite.com/wp-json/domain-pricing/v1/production-setup" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ASiOTwM9W9ToZ0zOSDsMF1OQYUmRumys"

# With force update (if data already exists)
curl -X POST "https://yoursite.com/wp-json/domain-pricing/v1/production-setup" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ASiOTwM9W9ToZ0zOSDsMF1OQYUmRumys" \
  -d '{"force_update": "true"}'
```

#### **Method 2: Using Secret Parameter (Simple)**
```bash
# Complete production setup
curl -X POST "https://yoursite.com/wp-json/domain-pricing/v1/production-setup?secret=ASiOTwM9W9ToZ0zOSDsMF1OQYUmRumys"

# With force update
curl -X POST "https://yoursite.com/wp-json/domain-pricing/v1/production-setup?secret=ASiOTwM9W9ToZ0zOSDsMF1OQYUmRumys" \
  -H "Content-Type: application/json" \
  -d '{"force_update": "true"}'
```

#### **Method 3: Using Bearer Token**
```bash
# Complete production setup
curl -X POST "https://yoursite.com/wp-json/domain-pricing/v1/production-setup" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ASiOTwM9W9ToZ0zOSDsMF1OQYUmRumys" \
  -d '{"force_update": "true"}'
```

### **Step 3: Verify Setup**
```bash
# Check statistics
curl "https://yoursite.com/wp-json/domain-pricing/v1/stats" \
  -H "X-API-Key: ASiOTwM9W9ToZ0zOSDsMF1OQYUmRumys"

# Or with secret parameter
curl "https://yoursite.com/wp-json/domain-pricing/v1/stats?secret=ASiOTwM9W9ToZ0zOSDsMF1OQYUmRumys"
```

## 📋 **Complete Production Deployment Script**

Save this as `deploy-pricing.sh`:

```bash
#!/bin/bash

# Configuration - UPDATE YOUR SITE URL
SITE_URL="https://yoursite.com"
API_KEY="ASiOTwM9W9ToZ0zOSDsMF1OQYUmRumys"

echo "🚀 Starting production pricing setup..."

# Step 1: Production setup
echo "📦 Running production setup..."
SETUP_RESPONSE=$(curl -s -X POST "$SITE_URL/wp-json/domain-pricing/v1/production-setup" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: $API_KEY" \
  -d '{"force_update": "true"}')

echo "Setup Response:"
echo "$SETUP_RESPONSE" | python -m json.tool 2>/dev/null || echo "$SETUP_RESPONSE"

# Step 2: Verify setup
echo ""
echo "✅ Verifying setup..."
STATS_RESPONSE=$(curl -s "$SITE_URL/wp-json/domain-pricing/v1/stats" \
  -H "X-API-Key: $API_KEY")

echo "Stats Response:"
echo "$STATS_RESPONSE" | python -m json.tool 2>/dev/null || echo "$STATS_RESPONSE"

echo ""
echo "🎉 Production setup complete!"
```

Make it executable and run:
```bash
chmod +x deploy-pricing.sh
./deploy-pricing.sh
```

## 🎯 **Individual API Endpoints**

### **Import from File Only**
```bash
curl -X POST "https://yoursite.com/wp-json/domain-pricing/v1/import-from-file" \
  -H "X-API-Key: YOUR_API_KEY_HERE"
```

### **Update from API Only**
```bash
curl -X POST "https://yoursite.com/wp-json/domain-pricing/v1/update-from-api" \
  -H "X-API-Key: YOUR_API_KEY_HERE"
```

### **Get All Pricing Data**
```bash
curl "https://yoursite.com/wp-json/domain-pricing/v1/pricing-data" \
  -H "X-API-Key: YOUR_API_KEY_HERE"
```

## 📱 **Expected Responses**

### **Success Response:**
```json
{
    "success": true,
    "message": "Production setup completed successfully",
    "data": {
        "database_setup": true,
        "pricing_import": true,
        "import_method": "file",
        "cron_setup": true,
        "final_stats": {
            "total_tlds": 250,
            "average_price": 12.45,
            "newest_update": "2024-01-15 10:30:00"
        }
    }
}
```

### **Error Response:**
```json
{
    "code": "rest_forbidden",
    "message": "You do not have permission to access this endpoint. Use WordPress admin login, API key, or secret key.",
    "data": {
        "status": 403
    }
}
```

## 🔐 **Authentication Methods Summary**

| Method | Header/Parameter | Example |
|--------|------------------|---------|
| **API Key** | `X-API-Key: key` | `curl -H "X-API-Key: abc123..."` |
| **Bearer Token** | `Authorization: Bearer key` | `curl -H "Authorization: Bearer abc123..."` |
| **Secret Key** | `?secret=key` or `X-Secret: key` | `curl "url?secret=xyz789"` |

## ⚠️ **Important Notes**

1. **No cookies required** - Perfect for server-to-server calls
2. **No WordPress login needed** - Use API keys instead
3. **Secure authentication** - Keys are auto-generated and can be regenerated
4. **Multiple auth methods** - Choose what works best for your setup
5. **Production ready** - Handles database setup, imports, and cron scheduling

## 🚀 **Quick Start**

1. **Get your API key** from WordPress Admin → Tools → Domain Pricing
2. **Replace `YOUR_API_KEY_HERE`** in the commands above
3. **Replace `yoursite.com`** with your actual domain
4. **Run the production setup command**
5. **Verify with the stats command**

**That's it! No cookies, no complex authentication, just simple API keys.**

## 🔧 **Troubleshooting**

- **403 Forbidden:** Check your API key is correct
- **404 Not Found:** Verify the URL and endpoint path
- **500 Server Error:** Check WordPress error logs
- **File not found:** Ensure `namecheap_price.md` exists in `/wp-content/themes/ipt_home/md/`

The production setup will automatically:
- ✅ Create database table
- ✅ Import pricing from file (or API fallback)
- ✅ Schedule automatic updates
- ✅ Return complete status
