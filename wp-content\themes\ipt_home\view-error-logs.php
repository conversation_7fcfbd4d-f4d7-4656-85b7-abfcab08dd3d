<?php
/**
 * Simple Error Log Viewer
 * 
 * Access via: yoursite.com/wp-content/themes/ipt_home/view-error-logs.php
 */

// Load WordPress
require_once('../../../../wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

echo "<h1>WordPress Error Log Viewer</h1>";

// Common error log locations
$possible_log_files = [
    ABSPATH . 'wp-content/debug.log',
    ABSPATH . 'debug.log',
    ABSPATH . 'error_log',
    ABSPATH . 'wp-content/error_log',
    ini_get('error_log'),
    '/tmp/error_log',
    '/var/log/apache2/error.log',
    '/var/log/nginx/error.log'
];

echo "<h2>Checking Common Error Log Locations:</h2>";

$found_logs = [];

foreach ($possible_log_files as $log_file) {
    if ($log_file && file_exists($log_file) && is_readable($log_file)) {
        $size = filesize($log_file);
        echo "<p>✅ <strong>$log_file</strong> - Size: " . number_format($size) . " bytes</p>";
        $found_logs[] = $log_file;
    } else {
        echo "<p>❌ $log_file - Not found or not readable</p>";
    }
}

if (empty($found_logs)) {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0;'>";
    echo "<h3>No Error Logs Found</h3>";
    echo "<p>To enable WordPress error logging, add these lines to your wp-config.php:</p>";
    echo "<pre>";
    echo "define('WP_DEBUG', true);\n";
    echo "define('WP_DEBUG_LOG', true);\n";
    echo "define('WP_DEBUG_DISPLAY', false);\n";
    echo "</pre>";
    echo "</div>";
} else {
    // Show the most recent log file
    $main_log = $found_logs[0];
    echo "<h2>Recent Entries from: $main_log</h2>";
    
    if (filesize($main_log) > 0) {
        // Get last 50 lines
        $lines = file($main_log);
        $recent_lines = array_slice($lines, -50);
        
        echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; font-family: monospace; white-space: pre-wrap; max-height: 400px; overflow-y: scroll;'>";
        
        foreach ($recent_lines as $line) {
            // Highlight domain pricing related errors
            if (strpos($line, 'Pricing') !== false || strpos($line, 'domain') !== false || strpos($line, 'GraphQL') !== false) {
                echo "<span style='background: yellow;'>" . htmlspecialchars($line) . "</span>";
            } else {
                echo htmlspecialchars($line);
            }
        }
        
        echo "</div>";
        
        echo "<p><strong>Showing last 50 lines.</strong> Full log has " . count($lines) . " lines.</p>";
    } else {
        echo "<p>Log file is empty.</p>";
    }
}

// PHP Error Log Info
echo "<h2>PHP Configuration:</h2>";
echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
echo "<tr><td><strong>log_errors</strong></td><td>" . (ini_get('log_errors') ? 'On' : 'Off') . "</td></tr>";
echo "<tr><td><strong>error_log</strong></td><td>" . (ini_get('error_log') ?: 'Not set') . "</td></tr>";
echo "<tr><td><strong>display_errors</strong></td><td>" . (ini_get('display_errors') ? 'On' : 'Off') . "</td></tr>";
echo "</table>";

// WordPress Debug Info
echo "<h2>WordPress Debug Configuration:</h2>";
echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
echo "<tr><td><strong>WP_DEBUG</strong></td><td>" . (defined('WP_DEBUG') && WP_DEBUG ? 'True' : 'False') . "</td></tr>";
echo "<tr><td><strong>WP_DEBUG_LOG</strong></td><td>" . (defined('WP_DEBUG_LOG') && WP_DEBUG_LOG ? 'True' : 'False') . "</td></tr>";
echo "<tr><td><strong>WP_DEBUG_DISPLAY</strong></td><td>" . (defined('WP_DEBUG_DISPLAY') && WP_DEBUG_DISPLAY ? 'True' : 'False') . "</td></tr>";
echo "</table>";

echo "<hr>";
echo "<p><a href='" . admin_url('tools.php?page=domain-pricing') . "'>← Back to Domain Pricing Admin</a></p>";
echo "<p><a href='#' onclick='location.reload()'>🔄 Refresh Logs</a></p>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
h1 { color: #333; border-bottom: 2px solid #0073aa; padding-bottom: 10px; }
h2 { color: #666; margin-top: 30px; }
p { margin: 10px 0; }
table { margin: 10px 0; }
pre { background: #f1f1f1; padding: 10px; border-radius: 4px; }
a { color: #0073aa; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
