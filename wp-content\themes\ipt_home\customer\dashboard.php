<?php include get_stylesheet_directory() . '/customer/header-customer.php'; ?>
<?php include get_stylesheet_directory() . '/customer/header-dashboard.php'; ?>
<style>
/* Custom dropdown arrow styling */
.select-custom {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 16px center;
    background-repeat: no-repeat;
    background-size: 20px 20px;
}

.select-custom:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%232BA990' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
}
</style>
<div class="flex min-h-screen bg-neutral-light">
   <?php include get_stylesheet_directory() . '/customer/sidebar.php'; ?>
   <div class="flex-1 flex flex-col min-h-screen">
      <?php include get_stylesheet_directory() . '/customer/breadcrumb-dashboard.php'; ?>
      <!-- Subscription -->
      <div class="w-full flex flex-row gap-4 px-4 pb-4 pt-4 bg-db-dark-2">
         <!-- Card 1: Subscription -->
         <div class="flex-1 bg-white rounded-[16px] shadow-elevation-bottom-100 p-6 flex flex-col justify-between relative min-w-[220px]">
            <div class="flex items-center gap-3">
               <span class="w-8 h-8 flex items-center justify-center rounded-full bg-[#F7F9FA]">
               <i class="fa-solid fa-award text-neutral-medium text-lg"></i>
               </span>
               <!-- <span class="absolute right-6 top-4 text-[10px] text-neutral-medium font-normal text-right leading-tight">
               Renewal date:<br><span id="dashboard-renewal-date">Loading...</span>
               </span> -->
            </div>
            <div class="mt-6">
               <div class="text-neutral-light text-16 font-normal uppercase tracking-wide mb-1">Subscription</div>
               <div class="text-secondary-main text-32 font-bold" id="dashboard-plan-type"></div>
            </div>
         </div>
         <!-- Card 2: Total Websites -->
         <div class="flex-1 bg-white rounded-[16px] shadow-elevation-bottom-100 p-6 flex flex-col justify-between min-w-[220px]">
            <!-- <div class="flex items-center gap-3">
               <span class="w-8 h-8 flex items-center justify-center rounded-full bg-[#F7F9FA]">
               <i class="fa-solid fa-globe text-neutral-medium text-lg"></i>
               </span>
            </div>
            <div class="mt-6">
               <div class="text-neutral-light text-16 font-normal uppercase tracking-wide mb-1">Total Websites</div>
               <div class="text-secondary-main text-32 font-bold" id="dash-total-sites"></div>
            </div> -->
         </div>
         <!-- Card 3: Last Backup -->
         <!-- <div class="flex-1 bg-white rounded-[16px] shadow-elevation-bottom-100 p-6 flex flex-col justify-between min-w-[220px]">
            <div class="flex items-center gap-3">
               <span class="w-8 h-8 flex items-center justify-center rounded-full bg-[#F7F9FA]">
               <i class="fa-solid fa-cloud text-neutral-medium text-lg"></i>
               </span>
            </div>
            <div class="mt-6">
               <div class="text-neutral-light text-16 font-normal uppercase tracking-wide mb-1">Last Backup</div>
               <div class="text-secondary-main text-32 font-bold">Today, <?php echo date('h:i A'); ?></div>
            </div>
         </div> -->
      </div>
      <!-- Notification -->
      <!-- <div class="w-full flex flex-row gap-4 px-4 pb-4 pt-4 bg-db-dark-2">
         <div class="flex-1 rounded-[16px] shadow-elevation-bottom-100 p-6 flex flex-col justify-between relative min-w-[220px] bg-label-success text-white">
            <div class="flex items-center gap-3">
               <span class="w-8 h-8 flex items-center justify-center rounded-full ">
                  <i class="fa-solid fa-exclamation-circle text-neutral-medium text-lg"></i>
               </span>  
               Your website is fully managed with daily backups.
            </div>
         </div>
      </div> -->
      <!-- <div class="w-full flex flex-row p-0 bg-db-dark-2 px-4 ">
         <section class="w-full flex items-center bg-white rounded-[10px] shadow-elevation-bottom-100 px-4 py-4 mb-2">
            <div class="flex-shrink-0 mr-4">
               <img src="https://cdn-icons-png.flaticon.com/512/3135/3135715.png" alt="Website Illustration" class="w-16 h-16 object-contain rounded-md bg-[#F7F9FA]"/>
            </div>
            <div class="flex-1 flex flex-col justify-center">
               <div class="font-semibold text-db-dark text-base mb-1">Ready to Build Your Next Website?</div>
               <div class="text-semibold text-db-dark text-base">Start your fully managed website with no hassle.</div>
            </div>
            <div class="flex items-center gap-3 ml-4">
      
               <button  class="align-middle mx-auto sm:mx-0 text-16-2 font-semibold bg-white hover:bg-white hover:opacity-80 rounded-[8px] border border-neutral-medium hover:border-neutral-medium w-auto h-[48px] md:h-[48px] text-primary-main">
               Create New Website
               </button>
               <button   class="align-middle mx-auto sm:mx-0 text-16-2 font-semibold bg-brand-main hover:bg-brand-main hover:opacity-80 rounded-[8px] w-auto h-[48px] md:h-[48px] text-primary-main">
               How to get started
               </button>
            </div>
         </section>
      </div> -->
      <!-- Website -->
      <main class="flex-1 p-4 bg-db-dark-2">
         <div class="bg-white rounded-lg shadow p-4">
            <div class="flex items-center justify-between mb-4">
               <!-- left side -->
               <div class="font-semibold text-lg">Recent Website</div>
               <!-- right side -->
               <div class="flex items-center gap-4">
                  <!-- Filter Dropdown -->
                  <!-- <div class="relative">
                     <select id="industry" class="appearance-none bg-white border border-bd-border hover:border-bd-border active:border-bd-border focus:border-bd-border !rounded-full px-6 py-2 pr-10 text-sm text-db-dark font-medium focus:outline-none focus:border-brand-main min-w-[160px] select-custom">
                        <option>Select a filter</option>
                     </select>
                  </div> -->
                  <!-- Search Input -->
                  <!-- <div class="relative">
                     <input
                        type="text"
                        placeholder=""
                        id="searchInput"
                        class="bg-white border border-[#E6E8EC] !rounded-full !pl-10 pr-4 text-base text-db-dark font-medium placeholder:text-neutral-light focus:outline-none focus:border-brand-main min-w-[180px]"
                     />
                     <span class="absolute left-4 top-1/2 -translate-y-1/2 text-brand-main">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                           <circle cx="11" cy="11" r="8" stroke-width="2"/>
                           <path d="M21 21l-4.35-4.35" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                     </span>
                     <button type="button" id="clearButton" class="p-3 absolute right-3 top-1/2 -translate-y-1/2 bg-brand-color-01 rounded-full !w-[18px] !h-[18px] flex items-center justify-center hover:bg-brand-color-01 hidden">
                        <i class="fa-solid fa-close text-neutral-medium text-lg"></i>
                     </button>
                  </div> -->
               </div>
              
            </div>
            <!-- Danh sách website sẽ render ở đây -->
            <!-- <div class="text-neutral-medium text-center py-12">No websites found.</div> -->

            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6" id="dashboard-websites-list">
               <!-- Websites will be loaded here by JavaScript -->
               <div class="col-span-full text-neutral-medium text-center py-12">Loading website...</div>
            </div>
         </div>

         

         <!-- activity -->
         <!-- Nhớ đã import Font Awesome vào dự án của bạn! -->
         <!-- <div class="bg-white rounded-2xl p-8 w-full mt-6">
            <div class="text-[#0A1734] text-[24px] font-semibold mb-6">Activity Feed / Tips Panel</div>
            <div class="flex gap-6">
               <div class="bg-[#F9F9F9] rounded-lg flex items-center px-8 py-6 min-w-[340px]">
                  <i class="fas fa-clock text-[#0A1734] text-2xl mr-4"></i>
                  <span class="text-[18px] text-[#202020]">Site C was created 10 minutes ago</span>
               </div>
               <div class="bg-[#F9F9F9] rounded-lg flex items-center px-8 py-6 min-w-[340px]">
                  <i class="fas fa-clock text-[#0A1734] text-2xl mr-4"></i>
                  <span class="text-[18px] text-[#202020]">New template designs available!</span>
               </div>
            </div>
         </div> -->
      </main>

   </div>
</div>

<script>
function toggleMenu(id) {
  const menu = document.getElementById(id);
  menu.classList.toggle('hidden');
}

// Global variable to store current subscription plan
let currentSubscriptionPlan = 'Trial'; // Default to Trial

// Function to clean plan name (remove "Plan" suffix and extra spaces)
function cleanPlanName(planName) {
   if (!planName) return 'Trial';

   // Remove "Plan" suffix and trim spaces
   let cleaned = planName.replace(/\s*plan\s*$/i, '').trim();

   // If empty after cleaning, default to Trial
   return cleaned || 'Trial';
}

// Fetch subscription data for dashboard
jQuery(document).ready(function($) {
  // Fetch latest order data with Stripe payment
  function fetchLatestOrderData() {
    $.ajax({
      url: iptHomeAjax.ajax_url,
      type: 'POST',
      dataType: 'json',
      data: {
        action: 'ipt_get_latest_stripe_order',
        security: '<?php echo wp_create_nonce('ipt-subscription-nonce'); ?>'
      },
      success: function(response) {
        if (response.success && response.data) {
          // Update UI with latest order data
          const planName = response.data.plan_name || 'Trial';
          $('#dashboard-plan-type').text(planName);

          // Store subscription plan globally for use in edit URLs (clean the plan name)
          currentSubscriptionPlan = cleanPlanName(planName);
         //  $('#dashboard-renewal-date').text(response.data.renewal_date || 'N/A');
        }
      },
      error: function(xhr, status, error) {
        // Set default values in case of error
        $('#dashboard-plan-type').text('Trial');
        currentSubscriptionPlan = 'Trial';
      //   $('#dashboard-renewal-date').text('<?php echo date('m/d/Y', strtotime('+30 days')); ?>');
      }
    });
  }

  // Load subscription data on page load
  fetchLatestOrderData();
});

// Dashboard websites functionality
jQuery(document).ready(function($) {
   // Get current user's customer ID
   const customerId = <?php echo json_encode(ipt_home_get_customer_id_from_user(get_current_user_id())); ?>;

   if (customerId) {
      // Fetch websites for dashboard
      fetchDashboardWebsites();
   } else {
      $('#dashboard-websites-list').html('<div class="col-span-full text-neutral-medium text-center py-12">Please login to view your websites.</div>');
   }

   // Function to get authentication token (reused from websites.php)
   async function getAuthToken() {
      const mutation = `
         mutation Auth_login($body: LoginInputDto!) {
            auth_login(body: $body) {
               id
               token {
                  access_token
               }
            }
         }
      `;

      const variables = {
         body: {
            email: "<EMAIL>",
            password: "Abc!23456789",
            role_id: 1
         }
      };

      return new Promise((resolve, reject) => {
         jQuery.ajax({
            url: iptHomeAjax.ajax_url,
            type: 'POST',
            dataType: 'json',
            data: {
               action: 'ipt_home_graphql',
               query: mutation,
               variables: JSON.stringify(variables)
            },
            success: function(response) {
               if (response.errors && response.errors.length > 0) {
                  reject(new Error(response.errors[0].message || 'Authentication failed'));
               } else if (response.data && response.data.auth_login && response.data.auth_login.token) {
                  resolve(response.data.auth_login.token.access_token);
               } else {
                  reject(new Error('Invalid authentication response'));
               }
            },
            error: function(xhr, status, error) {
               reject(new Error('Failed to authenticate'));
            }
         });
      });
   }
   // Function to fetch websites for dashboard
   async function fetchDashboardWebsites() {
      try {

         // Get authentication token
         const authToken = await getAuthToken();

         // Prepare GraphQL query
         const query = `
            query Websites_list($input: BasePaginationInput!) {
               websites_list(input: $input) {
                  totalCount
                  totalPages
                  currentPage
                  data {
                     id
                     created_by
                     updated_by
                     created_at
                     updated_at
                     customer_id
                     template_id
                     domain
                     info
                     status_id
                  }
               }
            }
         `;

         // Prepare variables with customer filter
         const variables = {
            input: {
               filters: `customer_id:=(${customerId})`
            }
         };

         // Make direct API call with Bearer token
         $.ajax({
            url: '<?php echo GRAPHQL_API_URL; ?>',
            type: 'POST',
            dataType: 'json',
            headers: {
               'Content-Type': 'application/json',
               'Authorization': 'Bearer ' + authToken
            },
            data: JSON.stringify({
               query: query,
               variables: variables
            }),
            success: function(response) {

               if (response.errors && response.errors.length > 0) {
                  $('#dashboard-websites-list').html('<div class="col-span-full text-neutral-medium text-center py-12">Error loading websites.</div>');
               } else if (response.data && response.data.websites_list) {
                  renderDashboardWebsites(response.data.websites_list.data);
                  $("#dash-total-sites").html(response.data.websites_list.totalCount);
               } else {
                  $('#dashboard-websites-list').html('<div class="col-span-full text-neutral-medium text-center py-12">Unexpected response from server.</div>');
               }
            },
            error: function(xhr, status, error) {
               $('#dashboard-websites-list').html('<div class="col-span-full text-neutral-medium text-center py-12">Failed to load websites.</div>');
            }
         });

      } catch (error) {
         $('#dashboard-websites-list').html('<div class="col-span-full text-neutral-medium text-center py-12">Error: ' + error.message + '</div>');
      }
   }

   // Function to render websites in dashboard using the selected layout
   function renderDashboardWebsites(websites) {
      let html = '';

      // Check if no websites found
      if(!websites || websites.length === 0) {
         html = '<div class="col-span-full text-neutral-medium text-center py-12">No websites found. <a href="/customer/create_website" class="text-brand-main hover:underline">Create your first website</a></div>';
         $('#dashboard-websites-list').html(html);
         return;
      }
      let count = 0;
      websites.forEach(function(website) {
         if(count > 0) {
            return;
         }
         // Parse info JSON
         let websiteInfo = {};
         try {
            websiteInfo = JSON.parse(website.info || '{}');
         } catch (e) {
            websiteInfo = {};
         }

         // Format date as dd/mm/YYYY
         const createdDate = new Date(website.created_at);
         const updatedDate = `${createdDate.getDate().toString().padStart(2, '0')}/${(createdDate.getMonth() + 1).toString().padStart(2, '0')}/${createdDate.getFullYear()}`;

         // Get website name from info or use domain
         const websiteName = websiteInfo.company_name || website.domain || 'Unnamed Website';

         // Get domains for different purposes
         const viewDomain = websiteInfo.domain; // For view button - use info.domain directly
         const editDomain = websiteInfo.domain; // For edit button - use info.domain with bypass token
         const displayDomain = websiteInfo.domain || website.domain; // For display

         // Prepare URLs
         const viewUrl = viewDomain ? (viewDomain.startsWith('http') ? viewDomain : 'https://' + viewDomain) : '#';
         const editUrl = editDomain ?
            (editDomain.startsWith('http') ? editDomain : 'https://' + editDomain) +
            '?bypass_token=ASiOTwM9W9ToZ0zOSDsMF1OQYUmRumys&plan=' + encodeURIComponent(currentSubscriptionPlan)
            : '#';

            //               

         html += `
            <div class="relative rounded-2xl bg-brand-main overflow-hidden shadow bg-white min-h-[220px] flex flex-col justify-end">
               <!-- Ảnh nền -->
               <!--  <img src="https://images.unsplash.com/photo-1506744038136-46273834b3fb" class="absolute inset-0 w-full h-full object-cover" alt="Website" /> -->
               <!-- Background image placeholder -->
               <div class="absolute inset-0 bg-gradient-to-br from-brand-main to-brand-main/70"></div>
               <!-- Overlay mờ -->
               <!-- <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent"></div> -->
               <!-- Nút xóa -->
               <button class="absolute top-4 left-4 w-9 h-9 flex items-center justify-center bg-white rounded-lg shadow !p-2">
                  <i class="fa fa-trash text-db-dark text-lg" aria-hidden="true"></i>
               </button>
               <!-- Toggle -->
               <!--<div class="absolute top-4 right-4">
                  <label class="inline-flex items-center cursor-pointer relative">
                     <input type="checkbox" class="sr-only peer" ${website.status_id === 2 ? 'checked' : ''}>
                     <div class="w-10 h-6 bg-gray-200 rounded-full peer peer-checked:bg-green-400 transition"></div>
                     <div class="absolute left-1 top-1 bg-white w-4 h-4 rounded-full shadow peer-checked:translate-x-4 transition"></div>
                  </label>
               </div>-->
               <!-- Nút Edit/View -->
               <div class="relative z-10 flex justify-center gap-3 mb-4">
                  <button class="bg-white text-db-dark font-semibold rounded-lg px-6 py-2 shadow" onclick="window.open('${editUrl}', '_blank')">Edit</button>
                  <button class="bg-brand-main text-white font-semibold rounded-lg px-6 py-2 shadow" onclick="window.open('${viewUrl}', '_blank')">View</button>
               </div>
               <!-- Tên site và ngày cập nhật -->
               <div class="relative z-10 px-4 pb-4">
                  <div class="text-white text-14 font-bold">${websiteName}</div>
                  <div class="text-white text-14 opacity-80">Updated: ${updatedDate}</div>
               </div>
            </div>
         `;
         count++;
      });

      // Add "Create New Website" card
      if(websites.length == 0) {
         html += `
            <div class="flex items-center justify-center rounded-2xl border-2 border-dashed border-brand-main min-h-[220px] bg-white cursor-pointer hover:bg-brand-main/10 transition" onclick="window.location.href='/customer/create_website'">
               <button class="flex flex-col items-center justify-center !bg-transparent !shadow-none">
                  <span class="text-brand-main text-[100px] mb-2 text-semibold">+</span>
               </button>
            </div>
         `;
      }

      $('#dashboard-websites-list').html(html);
   }

});

   jQuery(document).ready(function($) {
      let debounceTimeout;
      function fetchTemplates(keyword = "", industry_selected = "") {
         // Nếu có từ khóa thì tạo filters là mảng, không thì để null
         const filters = ["status_id:=(2)","is_kit:=(0)"];
         const search = keyword ? keyword : "<?php if(isset($_GET['keyword'])) echo $_GET['keyword']; ?>";

         const query = `
         query Template_list($filters: [String!], $search: String!) {
            template_list(body: {filters: $filters, search: $search }) {
                  totalCount
                  totalPages
                  currentPage
                  data {
                     id
                     name
                     desc
                     image_id
                     image {
                        id
                        file_name
                        file_url
                     }
                     industries {
                        id
                        name
                     }
                     info
                  }
            }
         }`;

         $.ajax({
            url: iptHomeAjax.ajax_url,
            type: 'POST',
            dataType: 'json',
            data: {
                  action: 'ipt_home_graphql',
                  query: query,
                  variables: JSON.stringify({ filters: filters, search: search })
            },
            success: function(response) {
                  if(response.data && response.data.template_list) {
                     renderTemplates(response.data.template_list.data, industry_selected);
                  }
            }
         });
      }

      // Get all templates
      // fetchTemplates("");

      $('#search-template-button').on('click', function(e) {
         e.preventDefault();
         const keyword = $('#search-template').val();
         // fetchTemplates(keyword);
      });

      $('#search-form').on('submit', function(e) {
         e.preventDefault();
         const keyword = $('#search-template').val();
         // fetchTemplates(keyword);
      });

      // Render templates
      function renderTemplates(list, industry_selected = "") {
         let html = '';

         // Check if no templates found after filtering
         if(list.length === 0) {
            html = '<div class="col-span-full text-neutral-medium text-center py-12">No templates found!</div>';
            $('#template-list').html(html);
            $('#continueBtn').hide();
            return;
         } else {
            $('#continueBtn').show();
         }

         list.forEach(function(item) {
            let industry_name = item.industries[0] ? item.industries[0].name : "";
            if(industry_selected.length > 0) {
                  if(industry_selected !== industry_name) {
                     return;
                  }
            }
            
            html += `
                  <article class="flex relative flex-col border bg-white rounded-[8px] overflow-hidden border-solid border-fifth-main h-full">
                     <div class="absolute top-2 right-2 z-[999] cursor-pointer favorite-toggle">
                        <svg class="heart-outline" width="22" height="20" viewBox="0 0 22 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                           <path fill-rule="evenodd" clip-rule="evenodd" d="M10.9929 1.71691C8.65298 -0.327979 5.19252 -0.746436 2.50521 1.54965C-0.401577 4.03328 -0.823907 8.21609 1.47471 11.1739C2.34724 12.2967 4.05023 13.9835 5.68684 15.5282C7.34261 17.0911 8.99457 18.5679 9.80923 19.2894L9.82474 19.3031C9.90133 19.371 9.99669 19.4555 10.0881 19.5244C10.1975 19.6068 10.3547 19.7091 10.5645 19.7717C10.8434 19.8549 11.1432 19.8549 11.4221 19.7717C11.6319 19.7091 11.789 19.6068 11.8984 19.5244C11.9898 19.4555 12.0852 19.371 12.1618 19.3031L12.1773 19.2894C12.992 18.5679 14.6439 17.0911 16.2997 15.5282C17.9363 13.9835 19.6393 12.2967 20.5118 11.1739C22.8016 8.22745 22.4445 4.01238 19.4709 1.54088C16.7537 -0.717541 13.3301 -0.328758 10.9929 1.71691ZM10.2333 3.78575C8.52828 1.79238 5.818 1.34975 3.80441 3.07021C1.7011 4.86733 1.41807 7.84171 3.05392 9.9467C3.81701 10.9286 5.40681 12.5137 7.05966 14.0738C8.6027 15.5303 10.1462 16.9145 10.9933 17.6663C11.8403 16.9145 13.3838 15.5303 14.9269 14.0738C16.5797 12.5137 18.1695 10.9286 18.9326 9.9467C20.5773 7.83034 20.3152 4.84319 18.1925 3.07898C16.1256 1.36105 13.4507 1.80119 11.7532 3.78575C11.5632 4.00786 11.2856 4.13573 10.9933 4.13573C10.701 4.13573 10.4233 4.00786 10.2333 3.78575Z" fill="white"/>
                        </svg>
                        <svg class="heart-filled hidden" width="22" height="20" viewBox="0 0 22 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                           <path fill-rule="evenodd" clip-rule="evenodd" d="M0 7.48C0 3.97111 2.65774 0 6.88889 0C8.71593 0 10.0661 0.710504 11 1.51082C11.9339 0.710503 13.2841 0 15.1111 0C19.3423 0 22 3.97111 22 7.48C22 9.32544 21.2854 11.0297 20.293 12.5091C19.2998 13.9897 17.9924 15.2999 16.7111 16.3798C15.4261 17.4629 14.1397 18.3372 13.1636 18.9411C12.6749 19.2435 12.2596 19.4807 11.9558 19.6447C11.8047 19.7263 11.6762 19.7924 11.5771 19.8404C11.5289 19.8637 11.4787 19.8871 11.4319 19.9068C11.4098 19.9161 11.3759 19.9299 11.3368 19.9431C11.3177 19.9496 11.2854 19.9601 11.2456 19.9699C11.2202 19.9762 11.1237 20 11 20C10.8763 20 10.7805 19.9763 10.7551 19.9701C10.7153 19.9602 10.6823 19.9496 10.6632 19.9431C10.6241 19.9299 10.5902 19.9161 10.5681 19.9068C10.5213 19.8871 10.4711 19.8637 10.4229 19.8404C10.3238 19.7924 10.1953 19.7263 10.0442 19.6447C9.74037 19.4807 9.32509 19.2435 8.83637 18.9411C7.86027 18.3372 6.57395 17.4629 5.28889 16.3798C4.00758 15.2999 2.70022 13.9897 1.70703 12.5091C0.714641 11.0297 0 9.32544 0 7.48Z" fill="#E54D2E"/>
                        </svg>
                     </div>
                     <div class="relative w-full h-[200px]">
                        <img src="${item.image ? item.image.file_url : 'https://cdn.builder.io/api/v1/image/assets/TEMP/11ce7b98d60f888e40ee582f5d48055fe0e8fdca'}"
                              alt="${item.name}" class="w-full h-full object-cover" />
                        <div class="flex h-[200px] justify-between items-center absolute px-3 py-0 top-0 inset-x-0">
                              <div class="cursor-pointer">
                                 <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                                    class="chevron-left">
                                    <path
                                          d="M10.757 12.0001L15.707 16.9501C15.8025 17.0423 15.8787 17.1526 15.9311 17.2747C15.9835 17.3967 16.0111 17.5279 16.0123 17.6607C16.0134 17.7934 15.9881 17.9251 15.9378 18.048C15.8875 18.1709 15.8133 18.2826 15.7194 18.3765C15.6255 18.4703 15.5139 18.5446 15.391 18.5949C15.2681 18.6452 15.1364 18.6705 15.0036 18.6693C14.8708 18.6682 14.7396 18.6406 14.6176 18.5882C14.4956 18.5358 14.3852 18.4596 14.293 18.3641L8.636 12.7071C8.44853 12.5195 8.34322 12.2652 8.34322 12.0001C8.34322 11.7349 8.44853 11.4806 8.636 11.2931L14.293 5.63606C14.4816 5.4539 14.7342 5.35311 14.9964 5.35538C15.2586 5.35766 15.5094 5.46283 15.6948 5.64824C15.8802 5.83365 15.9854 6.08446 15.9877 6.34666C15.99 6.60885 15.8892 6.86146 15.707 7.05006L10.757 12.0001Z"
                                          fill="white"></path>
                                 </svg>
                              </div>
                              <div class="cursor-pointer">
                                 <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                                    class="chevron-right">
                                    <path
                                          d="M13.314 12.071L8.36399 7.12098C8.18184 6.93238 8.08104 6.67978 8.08332 6.41758C8.0856 6.15538 8.19077 5.90457 8.37618 5.71916C8.56158 5.53375 8.8124 5.42859 9.07459 5.42631C9.33679 5.42403 9.58939 5.52482 9.77799 5.70698L15.435 11.364C15.6225 11.5515 15.7278 11.8058 15.7278 12.071C15.7278 12.3361 15.6225 12.5905 15.435 12.778L9.77799 18.435C9.58939 18.6171 9.33679 18.7179 9.07459 18.7157C8.8124 18.7134 8.56158 18.6082 8.37618 18.4228C8.19077 18.2374 8.0856 17.9866 8.08332 17.7244C8.08104 17.4622 8.18184 17.2096 8.36399 17.021L13.314 12.071Z"
                                          fill="white"></path>
                                 </svg>
                              </div>
                        </div>
                     </div>
                     <div class="flex flex-col items-start p-2 gap-[4px] self-stretch flex-1">
                        <div class="text-secondary-main text-16 font-normal">
                              ${item.industries[0] ? item.industries[0].name : ''}
                        </div>
                        <h3 class="text-secondary-main text-18 font-bold">
                              ${item.name}
                        </h3>
                        
                     </div>
                     <div class="flex items-start gap-2 p-2 self-stretch">
                        <button  onclick="window.open('${item.info.domain ? (item.info.domain.startsWith('http') ? item.info.domain : 'https://' + item.info.domain) : ''}', '_blank')"
                              class="border border-fourth-main flex-1 text-neutral-strong font-semibold cursor-pointer bg-transparent hover:bg-transparent py-2 rounded-[8px] text-14">
                              Preview
                        </button>
                        <button
                              class="border border-fourth-main flex-1 text-neutral-strong font-semibold cursor-pointer bg-transparent hover:bg-transparent py-2 rounded-[8px] text-14">
                              Choose
                        </button>
                     </div>
                  </article>
            `;
         });
         $('#template-list').html(html);
      }
      
      /* Industry list */
      const industry_filters = ["status_id:=(2)"];
      const industry_sort = "name:asc";
      const queryIndustry = `
         query Industry_list($filters: [String!],  $sort: String!) {
            industry_list(body: { filters: $filters, sort: $sort }) {
                  id
                  name
                  status_id
            }
         }
      `;

      $.ajax({
            url: iptHomeAjax.ajax_url,
            type: 'POST',
            dataType: 'json',
            data: {
               action: 'ipt_home_graphql',
               query: queryIndustry,
               variables: JSON.stringify({ filters: industry_filters, sort: industry_sort })

            },
            success: function(response) {
               if(response.data && response.data.industry_list) {      
                  renderIndustries(response.data.industry_list);
               } else {
                  // No data
               }
            },
            error: function(xhr, status, error) {
            }
      });

      // Hàm render ra HTML
      function renderIndustries(list) {
            let html = '<option value="">Select a filter</option>';  
            list.forEach(function(item) {
               html += `
                     <option value="${item.id}">${item.name}</option>
               `;      
            });
            $('#industry').html(html);

            let html_dropdown = '';  
            list.forEach(function(item) {
               html_dropdown += `
                  <div data-industry-id="${item.id}" class="px-4 py-2 hover:bg-ipt-bg-1 cursor-pointer transition-colors duration-150">
                        ${item.name}
                  </div>
               `;      
            });
            $('#industryDropdown').html(html_dropdown);
      }   

         // Xử lý sự kiện khi thay đổi giá trị dropdown
      $('#industry').on('change', function() {
         const selectedOption = $(this).find('option:selected');
         const industryName = selectedOption.text();
         fetchTemplates("", industryName);
      });
      
      // Xử lý sự kiện khi click vào item trong dropdown
      $(document).on('click', '#industryDropdown div', function() {
         const industryName = $(this).data('industry-name');
         const industryId = $(this).data('industry-id');
         
         // Cập nhật giá trị cho select
         $('#industry').val(industryId);
         
         // Ẩn dropdown
         $('#industryDropdown').addClass('hidden');
         
         // Gọi lại fetchTemplates với industry đã chọn
         fetchTemplates("", industryName);
      });

      // Search input clear button functionality
      const searchInput = document.getElementById('searchInput');
      const clearButton = document.getElementById('clearButton');

      if (searchInput && clearButton) {
         // Show/hide clear button based on input content
         function toggleClearButton() {
            if (searchInput.value.trim() !== '') {
               clearButton.classList.remove('hidden');
            } else {
               clearButton.classList.add('hidden');
            }
         }

         // Listen for input changes
         searchInput.addEventListener('input', toggleClearButton);
         searchInput.addEventListener('keyup', toggleClearButton);
         searchInput.addEventListener('paste', function() {
            // Delay to allow paste content to be processed
            setTimeout(toggleClearButton, 10);
         });

         // Clear button click handler
         clearButton.addEventListener('click', function() {
            searchInput.value = '';
            searchInput.focus();
            toggleClearButton();

            // Trigger any search functionality if needed
            // You can add custom search reset logic here
         });

         // Initial check on page load
         toggleClearButton();
      }

   });
</script>
<?php include get_stylesheet_directory() . '/customer/footer-customer.php'; ?>
