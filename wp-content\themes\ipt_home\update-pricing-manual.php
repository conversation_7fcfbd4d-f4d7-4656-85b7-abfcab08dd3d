<?php
/**
 * Manual Pricing Update Script
 * 
 * Run this file directly to manually update domain pricing
 * Access via: yoursite.com/wp-content/themes/ipt_home/update-pricing-manual.php
 */

// Load WordPress
require_once('../../../../wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

// Include the pricing system
require_once(get_stylesheet_directory() . '/includes/domain-pricing-system.php');

echo "<h1>Manual Domain Pricing Update</h1>";
echo "<p>Starting pricing update process...</p>";

// Create table if it doesn't exist
echo "<p>1. Creating/checking database table...</p>";
create_domain_pricing_table();
echo "<p>✅ Database table ready</p>";

// Fetch pricing from API
echo "<p>2. Fetching pricing from Namecheap API...</p>";
$pricing_data = fetch_pricing_from_namecheap_api();

if ($pricing_data && !empty($pricing_data)) {
    echo "<p>✅ Successfully fetched pricing for " . count($pricing_data) . " TLDs</p>";
    
    // Update database
    echo "<p>3. Updating database...</p>";
    $updated_count = update_domain_pricing_in_db($pricing_data);
    echo "<p>✅ Updated pricing for $updated_count TLDs in database</p>";
    
    // Show statistics
    $stats = get_domain_pricing_stats();
    echo "<h2>Final Statistics:</h2>";
    echo "<ul>";
    echo "<li>Total TLDs in database: " . $stats['total_tlds'] . "</li>";
    echo "<li>Average price: $" . number_format($stats['average_price'], 2) . "</li>";
    echo "<li>Last updated: " . $stats['newest_update'] . "</li>";
    echo "</ul>";
    
    // Show sample pricing
    echo "<h2>Sample Pricing (first 10 TLDs):</h2>";
    echo "<ul>";
    $count = 0;
    foreach ($pricing_data as $tld => $price) {
        if ($count >= 10) break;
        echo "<li>.$tld - $price</li>";
        $count++;
    }
    echo "</ul>";
    
    echo "<p><strong>✅ SUCCESS! Pricing data has been loaded into your database.</strong></p>";
    echo "<p>You can now delete this file and use the normal domain search.</p>";
    
} else {
    echo "<p>❌ Failed to fetch pricing data from API</p>";
    echo "<p>Please check:</p>";
    echo "<ul>";
    echo "<li>Your GraphQL endpoint is working: " . home_url('/graphql') . "</li>";
    echo "<li>Your Namecheap API credentials are configured</li>";
    echo "<li>Your server can make outbound HTTP requests</li>";
    echo "</ul>";
}

echo "<hr>";
echo "<p><a href='" . admin_url('tools.php?page=domain-pricing') . "'>← Back to Domain Pricing Admin</a></p>";
echo "<p><a href='" . home_url('/customer/search_domain.php') . "'>Test Domain Search →</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 40px; }
h1 { color: #333; }
h2 { color: #666; margin-top: 30px; }
p { margin: 10px 0; }
ul { margin: 10px 0 10px 20px; }
li { margin: 5px 0; }
hr { margin: 30px 0; }
a { color: #0073aa; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
