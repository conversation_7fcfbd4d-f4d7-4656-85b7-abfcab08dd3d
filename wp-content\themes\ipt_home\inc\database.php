<?php
/**
 * Database initialization and management
 * Automatically creates required tables if they don't exist
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Initialize database tables on theme activation
 */
add_action('after_switch_theme', 'ipt_create_database_tables');
add_action('init', 'ipt_check_database_version');

/**
 * Database version for tracking updates
 */
define('IPT_DB_VERSION', '1.0');

/**
 * Create all required database tables
 */
function ipt_create_database_tables() {
    global $wpdb;
    
    // Create domain purchases table
    ipt_create_domain_purchases_table();
    
    // Update database version
    update_option('ipt_db_version', IPT_DB_VERSION);
    
    error_log('IPT Database tables created/updated successfully');
}

/**
 * Check if database needs to be updated
 */
function ipt_check_database_version() {
    $installed_version = get_option('ipt_db_version', '0');

    if (version_compare($installed_version, IPT_DB_VERSION, '<')) {
        ipt_create_database_tables();
    }
}

/**
 * Ensure domain tables exist - call this on domain-related pages
 * This is a safety check in case theme was already active when database.php was added
 */
function ipt_ensure_domain_tables_exist() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'ipt_domain_purchases';

    // Check if table exists
    if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
        error_log('Domain table missing, creating now...');
        ipt_create_database_tables();
        return true; // Table was created
    }

    return false; // Table already existed
}

/**
 * Create domain_purchases table
 */
function ipt_create_domain_purchases_table() {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'ipt_domain_purchases';
    
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE $table_name (
        id int(11) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        domain_name varchar(255) NOT NULL,
        domain_type enum('purchased','custom') DEFAULT 'purchased',
        website_id int(11) DEFAULT NULL,
        server_ip varchar(45) DEFAULT NULL,
        price decimal(10,2) NOT NULL,
        is_premium tinyint(1) DEFAULT 0,
        status enum('selected','pending','failed','completed') DEFAULT 'selected',
        payment_intent_id varchar(255) DEFAULT NULL,
        selected_at datetime DEFAULT CURRENT_TIMESTAMP,
        paid_at datetime DEFAULT NULL,
        expires_at datetime DEFAULT NULL,
        auto_renewal tinyint(1) DEFAULT 1,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY idx_user_id (user_id),
        KEY idx_status (status),
        KEY idx_domain_name (domain_name),
        KEY idx_domain_type (domain_type),
        KEY idx_website_id (website_id),
        KEY idx_payment_intent (payment_intent_id),
        UNIQUE KEY unique_user_domain (user_id, domain_name)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
    
    // Check if table was created successfully
    if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name) {
        error_log("Table $table_name created successfully");
    } else {
        error_log("Failed to create table $table_name");
    }
}

/**
 * Get table name with prefix
 */
function ipt_get_domain_purchases_table() {
    global $wpdb;
    return $wpdb->prefix . 'ipt_domain_purchases';
}

/**
 * Save domain selection to database
 */
function ipt_save_domain_selection($user_id, $domain_name, $price, $is_premium = false) {
    global $wpdb;
    
    $table_name = ipt_get_domain_purchases_table();
    
    // Remove $ and /year from price
    $price_numeric = floatval(str_replace(['$', '/year'], '', $price));
    
    $result = $wpdb->replace(
        $table_name,
        array(
            'user_id' => $user_id,
            'domain_name' => $domain_name,
            'price' => $price_numeric,
            'is_premium' => $is_premium ? 1 : 0,
            'status' => 'selected',
            'selected_at' => current_time('mysql'),
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql')
        ),
        array(
            '%d', // user_id
            '%s', // domain_name
            '%f', // price
            '%d', // is_premium
            '%s', // status
            '%s', // selected_at
            '%s', // created_at
            '%s'  // updated_at
        )
    );
    
    if ($result !== false) {
        return $wpdb->insert_id ?: $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table_name WHERE user_id = %d AND domain_name = %s",
            $user_id, $domain_name
        ));
    }
    
    return false;
}

/**
 * Get user's domains
 */
function ipt_get_user_domains($user_id) {
    global $wpdb;
    
    $table_name = ipt_get_domain_purchases_table();
    
    $domains = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $table_name WHERE user_id = %d ORDER BY created_at DESC",
        $user_id
    ), ARRAY_A);
    
    // Format the data for frontend
    if ($domains) {
        foreach ($domains as &$domain) {
            $domain['price'] = '$' . number_format($domain['price'], 2) . '/year';
            $domain['is_premium'] = (bool) $domain['is_premium'];
            $domain['auto_renewal'] = (bool) $domain['auto_renewal'];
        }
    }
    
    return $domains ?: array();
}

/**
 * Update domain status
 */
function ipt_update_domain_status($domain_id, $status, $payment_intent_id = null) {
    global $wpdb;
    
    $table_name = ipt_get_domain_purchases_table();
    
    $update_data = array(
        'status' => $status,
        'updated_at' => current_time('mysql')
    );
    
    $update_format = array('%s', '%s');
    
    // Add payment intent ID if provided
    if ($payment_intent_id) {
        $update_data['payment_intent_id'] = $payment_intent_id;
        $update_format[] = '%s';
    }
    
    // Add paid_at timestamp for completed status
    if ($status === 'completed') {
        $update_data['paid_at'] = current_time('mysql');
        $update_data['expires_at'] = date('Y-m-d H:i:s', strtotime('+1 year'));
        $update_format[] = '%s';
        $update_format[] = '%s';
    }
    
    $result = $wpdb->update(
        $table_name,
        $update_data,
        array('id' => $domain_id),
        $update_format,
        array('%d')
    );
    
    return $result !== false;
}

/**
 * Remove domain from user's list
 */
function ipt_remove_domain($domain_id, $user_id) {
    global $wpdb;
    
    $table_name = ipt_get_domain_purchases_table();
    
    $result = $wpdb->delete(
        $table_name,
        array(
            'id' => $domain_id,
            'user_id' => $user_id
        ),
        array('%d', '%d')
    );
    
    return $result !== false;
}

/**
 * Get domain by payment intent ID
 */
function ipt_get_domain_by_payment_intent($payment_intent_id) {
    global $wpdb;
    
    $table_name = ipt_get_domain_purchases_table();
    
    return $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $table_name WHERE payment_intent_id = %s",
        $payment_intent_id
    ), ARRAY_A);
}

/**
 * Clean up old failed/selected domains (optional - run via cron)
 */
function ipt_cleanup_old_domains() {
    global $wpdb;
    
    $table_name = ipt_get_domain_purchases_table();
    
    // Remove selected domains older than 7 days
    $wpdb->query($wpdb->prepare(
        "DELETE FROM $table_name WHERE status IN ('selected', 'failed') AND created_at < %s",
        date('Y-m-d H:i:s', strtotime('-7 days'))
    ));
}

// Optional: Schedule cleanup (uncomment if needed)
// add_action('wp', 'ipt_schedule_domain_cleanup');
// function ipt_schedule_domain_cleanup() {
//     if (!wp_next_scheduled('ipt_domain_cleanup_hook')) {
//         wp_schedule_event(time(), 'daily', 'ipt_domain_cleanup_hook');
//     }
// }
// add_action('ipt_domain_cleanup_hook', 'ipt_cleanup_old_domains');
