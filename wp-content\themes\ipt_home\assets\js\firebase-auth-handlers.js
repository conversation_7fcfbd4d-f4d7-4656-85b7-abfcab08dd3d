/**
 * Firebase Authentication Handlers
 * Shared JavaScript for login and register pages
 */

// Handle Google Login/Register Button Click
function handleGoogleLogin() {
    
    // Check if Firebase is loaded
    if (typeof window.firebaseAuth === 'undefined') {
        // alert('Please wait for the page to fully load and try again.');
        return;
    }
    
    // Show loading state
    const button = event.target.closest('button');
    const originalText = button.innerHTML;
    button.originalText = originalText; // Store original text for Firebase function
    button.disabled = true;
    button.innerHTML = '<div class="flex items-center justify-center w-full"><div class="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-900 mr-2"></div><span>Signing in...</span></div>';

    // Call Firebase Google Sign In with button reference
    window.firebaseAuth.signInWithGoogle(button).catch(error => {
        // Restore button state on error
        button.disabled = false;
        button.innerHTML = originalText;
    });
}

// Handle Facebook Login/Register Button Click
function handleFacebookLogin() {
    
    // Check if Firebase is loaded
    if (typeof window.firebaseAuth === 'undefined') {
        // alert('Please wait for the page to fully load and try again.');
        return;
    }
    
    // Show loading state
    const button = event.target.closest('button');
    const originalText = button.innerHTML;
    button.originalText = originalText; // Store original text for Firebase function
    button.disabled = true;
    button.innerHTML = '<div class="flex items-center justify-center w-full"><div class="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-900 mr-2"></div><span>Processing...</span></div>';

    // Call Firebase Facebook Sign In with button reference
    window.firebaseAuth.signInWithFacebook(button).catch(error => {
        // Restore button state on error
        button.disabled = false;
        button.innerHTML = originalText;
    });
}

// Handle Apple Login/Register Button Click
function handleAppleLogin() {
    
    // Check if Firebase is loaded
    if (typeof window.firebaseAuth === 'undefined') {
        // alert('Please wait for the page to fully load and try again.');
        return;
    }
    
    // Show loading state
    const button = event.target.closest('button');
    const originalText = button.innerHTML;
    button.originalText = originalText; // Store original text for Firebase function
    button.disabled = true;
    button.innerHTML = '<div class="flex items-center justify-center w-full"><div class="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-900 mr-2"></div><span>Signing in...</span></div>';

    // Call Firebase Apple Sign In with button reference
    window.firebaseAuth.signInWithApple(button).catch(error => {
        // Restore button state on error
        button.disabled = false;
        button.innerHTML = originalText;
    });
}

// Register page handlers (same functions with different text)
function handleGoogleRegister() {
    return handleSocialAuth('google', 'Signing up...');
}

function handleFacebookRegister() {
    return handleSocialAuth('facebook', 'Signing up...');
}

function handleAppleRegister() {
    return handleSocialAuth('apple', 'Signing up...');
}

// Generic social auth handler
function handleSocialAuth(provider, loadingText = 'Processing...') {
    // Check if Firebase is loaded
    if (typeof window.firebaseAuth === 'undefined') {
        // alert('Please wait for the page to fully load and try again.');
        return;
    }
    
    // Show loading state
    const button = event.target.closest('button');
    const originalText = button.innerHTML;
    button.originalText = originalText; // Store original text for Firebase function
    button.disabled = true;
    button.innerHTML = `<div class="flex items-center justify-center w-full"><div class="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-900 mr-2"></div><span>${loadingText}</span></div>`;

    // Call appropriate Firebase function with button reference
    let authFunction;
    switch(provider) {
        case 'google':
            authFunction = () => window.firebaseAuth.signInWithGoogle(button);
            break;
        case 'facebook':
            authFunction = () => window.firebaseAuth.signInWithFacebook(button);
            break;
        case 'apple':
            authFunction = () => window.firebaseAuth.signInWithApple(button);
            break;
        default:
            button.disabled = false;
            button.innerHTML = originalText;
            return;
    }

    authFunction().catch(error => {
        // Restore button state on error
        button.disabled = false;
        button.innerHTML = originalText;
    });
}

// Wait for Firebase to load before enabling buttons
function initializeFirebaseButtons() {
    // Check if Firebase is loaded every 100ms for up to 10 seconds
    let checkCount = 0;
    const maxChecks = 100; // 10 seconds
    
    const checkFirebase = setInterval(() => {
        checkCount++;
        
        if (typeof window.firebaseAuth !== 'undefined') {
            clearInterval(checkFirebase);
            
            // Enable all social auth buttons
            const buttons = [
                // Login page buttons
                'button[onclick="handleGoogleLogin()"]',
                'button[onclick="handleFacebookLogin()"]',
                'button[onclick="handleAppleLogin()"]',
                // Register page buttons
                'button[onclick="handleGoogleRegister()"]',
                'button[onclick="handleFacebookRegister()"]',
                'button[onclick="handleAppleRegister()"]'
            ];
            
            buttons.forEach(selector => {
                const btn = document.querySelector(selector);
                if (btn) {
                    btn.disabled = false;
                }
            });
            
        } else if (checkCount >= maxChecks) {
            clearInterval(checkFirebase);

            // Show error message
            // alert('Authentication service failed to load. Please refresh the page.');
        }
    }, 100);
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    initializeFirebaseButtons();
});

// Password toggle function (shared utility)
function togglePassword(inputId, iconElement) {
    const input = document.getElementById(inputId);
    const showIcon = iconElement.querySelector('.show-password');
    const hideIcon = iconElement.querySelector('.hide-password');
    
    if (input.type === 'password') {
        input.type = 'text';
        showIcon.classList.add('hidden');
        hideIcon.classList.remove('hidden');
    } else {
        input.type = 'password';
        showIcon.classList.remove('hidden');
        hideIcon.classList.add('hidden');
    }
}
