<?php
// File xử lý sau khi thanh toán thành công
// Đặt file này tại wp-content/themes/ipt_home/customer/payment_success.php

// Kiểm tra nếu có thanh toán thành công
if (isset($_GET['payment_success']) && $_GET['payment_success'] === '1' && 
    isset($_GET['redirect_status']) && $_GET['redirect_status'] === 'succeeded' &&
    isset($_GET['payment_intent'])) {
    
    // Lấy payment intent ID
    $payment_intent_id = sanitize_text_field($_GET['payment_intent']);
    
    // Kiểm tra xem đơn hàng đã được tạo chưa (tránh tạo đơn hàng trùng lặp)
    $existing_orders = wc_get_orders(array(
        'meta_key' => '_stripe_payment_intent',
        'meta_value' => $payment_intent_id,
        'limit' => 1
    ));
    
    if (empty($existing_orders) && !empty($payment_intent_id)) {
        // Tạo đơn hàng WooCommerce từ giỏ hàng
        if (!WC()->cart->is_empty()) {
            // Tạo đơn hàng mới
            $order = wc_create_order();
            
            // Thêm sản phẩm từ giỏ hàng vào đơn hàng
            foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
                $product_id = $cart_item['product_id'];
                $quantity = $cart_item['quantity'];
                $variation_id = isset($cart_item['variation_id']) ? $cart_item['variation_id'] : 0;
                
                $order->add_product(wc_get_product($product_id), $quantity, array(
                    'variation_id' => $variation_id,
                    'variation' => $cart_item['variation'] ?? array(),
                ));
            }
            
            // Cập nhật thông tin khách hàng
            $current_user = wp_get_current_user();
            if ($current_user->ID) {
                $order->set_customer_id($current_user->ID);
                $order->set_billing_email($current_user->user_email);
                $order->set_billing_first_name($current_user->first_name);
                $order->set_billing_last_name($current_user->last_name);
                
                // Thêm thông tin thanh toán khác nếu có
                $billing_phone = get_user_meta($current_user->ID, 'billing_phone', true);
                if (!empty($billing_phone)) {
                    $order->set_billing_phone($billing_phone);
                }
                
                $billing_address_1 = get_user_meta($current_user->ID, 'billing_address_1', true);
                if (!empty($billing_address_1)) {
                    $order->set_billing_address_1($billing_address_1);
                }
            }
            
            // Cập nhật phương thức thanh toán
            $order->set_payment_method('stripe');
            $order->set_payment_method_title('Credit Card (Stripe)');
            
            // Lưu payment intent ID vào đơn hàng
            $order->update_meta_data('_stripe_payment_intent', $payment_intent_id);

            // Thử lấy thông tin chi tiết về payment method từ Stripe API
            if (function_exists('WC_Stripe_API') && class_exists('WC_Stripe_API')) {
                try {
                    // Lấy thông tin payment intent từ Stripe
                    $payment_intent = WC_Stripe_API::retrieve('payment_intents/' . $payment_intent_id);
                    
                    if (!empty($payment_intent) && isset($payment_intent->payment_method)) {
                        $payment_method_id = $payment_intent->payment_method;
                        $order->update_meta_data('_stripe_source_id', $payment_method_id);
                        
                        // Lấy thông tin chi tiết về payment method
                        $payment_method_object = WC_Stripe_API::retrieve('payment_methods/' . $payment_method_id);
                        
                        if (!empty($payment_method_object) && isset($payment_method_object->card) && isset($payment_method_object->card->last4)) {
                            $last4 = $payment_method_object->card->last4;
                            $order->update_meta_data('_stripe_card_last4', $last4);
                            
                            // Cập nhật tiêu đề phương thức thanh toán để hiển thị 4 số cuối
                            $order->set_payment_method_title('Credit Card (Stripe) ending in ' . $last4);
                        }
                    }
                } catch (Exception $e) {
                    error_log('Error fetching payment method from Stripe: ' . $e->getMessage());
                }
            }

            // Cập nhật tổng đơn hàng
            $order->calculate_totals();
            
            // Đánh dấu đơn hàng đã thanh toán
            $order->payment_complete($payment_intent_id);
            $order->update_status('completed');
            
            // Thêm ghi chú
            $order->add_order_note('Payment completed via Stripe. Payment Intent ID: ' . $payment_intent_id);
            
            // Lưu đơn hàng
            $order->save();
            
            // Xóa giỏ hàng
            WC()->cart->empty_cart();
            
            // Lưu ID đơn hàng vào session để hiển thị thông báo
            WC()->session->set('recent_order_id', $order->get_id());
            
            // Log thông tin
            error_log('Order created successfully. Order ID: ' . $order->get_id() . ', Payment Intent: ' . $payment_intent_id);
        } else {
            error_log('Cart is empty, cannot create order for Payment Intent: ' . $payment_intent_id);
        }
    } else {
        error_log('Order already exists for Payment Intent: ' . $payment_intent_id);
    }
    
    // Chuyển hướng đến trang subscription
    wp_safe_redirect(site_url('/?customer_page=subscription'));
    exit;
}
