<?php
/**
 * Template Name: Forgot Password page template
 *
 * @package ipt_home
 */

get_header();
// Ensure toast.js is loaded
wp_enqueue_script('ipt-toast');
?>
<main class="w-full bg-white min-h-[700px]">
    <div class="max-sm:px-[16px] mt-[48px] md:mt-[100px] mx-auto w-full md:w-[630px] mb-[168px]" id="contentForgotPasswordForm">
        <h1 class="text-left text-24 md:text-32 font-bold text-black mb-[44px] font-semibold forgot-pass-form">Forgot Password</h1>
        <div class="shadow-none forgot-pass-form">
            <!-- Forgot pass form -->
            <form class="space-y-6" id="forgotPasswordForm">
                <div>
                    <label for="email" class="block text-16 font-medium text-gray-700">Enter your login email and we'll send you an OTP to reset your password.</label>
                </div>
                <div>
                    <label for="email" class="block text-16 font-medium text-gray-700">Email</label>
                    <div class="mt-1">
                        <input id="email" name="email" type="email" autocomplete="email" required 
                            class="!bg-ipt-bg-1 !h-[48px] appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none !text-neutral-strong !bg-neutral-subtle"
                            placeholder="<EMAIL>">
                        <p id="email-error" class="mt-1 text-red-500 text-sm hidden">Please enter a valid email address.</p>
                    </div>
                </div>
               
                <div>
                    <button type="button" id="sendResetLinkBtn" class="w-full mx-auto flex justify-center py-2 px-4 text-primary-main hover:text-primary-main/80 rounded-md shadow-sm text-16 font-medium bg-brand-main hover:bg-brand-main/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-400">
                        Send
                    </button>   
                </div>

                <div>
                    <button type="button" onclick="window.location.href='<?php echo get_permalink(41); ?>'" class="w-full mx-auto flex justify-center py-2 px-4 hover:text-white rounded-md shadow-sm text-16 font-medium text-white bg-neutral-strong hover:bg-neutral-strong/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-400">
                        Back
                    </button>
                </div>

            </form>
        </div>
    </div>

    <!-- Reset password form -->
    <div class="max-sm:px-[16px] mt-[48px] md:mt-[60px] mx-auto w-full md:w-[630px] mb-[168px]" id="contentResetPasswordForm" style="display:none;">
        <h1 class="text-left text-24 md:text-32 font-bold text-black mb-[44px] font-semibold">Reset Password</h1>
        <div class="shadow-none">
            <!-- Reset password form -->
            <form class="space-y-6" id="resetPasswordForm">
                <div>
                    <label for="otp" class="block text-16 font-medium text-gray-700">OTP</label>
                    <div class="mt-1">
                        <input id="otp" name="otp" type="number" required 
                            class="!bg-ipt-bg-1 !h-[48px] appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none !text-neutral-strong !bg-neutral-subtle"
                            placeholder="Enter OTP">
                        <p id="otp-error" class="mt-1 text-red-500 text-sm hidden">Please enter a valid OTP.</p>
                    </div>

                    <label for="password" class="block text-16 font-medium text-gray-700 mt-4">New Password</label>
                    <div class="mt-1 relative">
                        <input id="password" 
                            name="password" 
                            type="password" 
                            required 
                            class="appearance-none block w-full px-4 py-3 border border-[#E6E9F5] rounded-lg shadow-sm 
                                    placeholder-gray-400 focus:outline-none focus:border-brand-main focus:ring-1 
                                    focus:ring-brand-main !text-neutral-strong !bg-ipt-bg-1 !h-[48px] pr-10"
                            placeholder="*****">

                        <!-- Password Requirements Hint -->
                        <div class="mt-2 text-xs text-gray-600 hidden" id="password-requirements">
                            <p class="font-medium mb-1">Password must contain:</p>
                            <ul class="space-y-1">
                                <li class="flex items-center" id="req-length">
                                    <span class="w-4 h-4 mr-2 flex items-center justify-center">
                                        <span class="w-2 h-2 bg-gray-300 rounded-full requirement-dot"></span>
                                        <svg class="w-4 h-4 text-green-500 hidden requirement-check" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                    </span>
                                    <span class="requirement-text">12 characters or more</span>
                                </li>
                                <li class="flex items-center" id="req-lowercase">
                                    <span class="w-4 h-4 mr-2 flex items-center justify-center">
                                        <span class="w-2 h-2 bg-gray-300 rounded-full requirement-dot"></span>
                                        <svg class="w-4 h-4 text-green-500 hidden requirement-check" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                    </span>
                                    <span class="requirement-text">At least 1 lowercase letter</span>
                                </li>
                                <li class="flex items-center" id="req-uppercase">
                                    <span class="w-4 h-4 mr-2 flex items-center justify-center">
                                        <span class="w-2 h-2 bg-gray-300 rounded-full requirement-dot"></span>
                                        <svg class="w-4 h-4 text-green-500 hidden requirement-check" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                    </span>
                                    <span class="requirement-text">At least 1 uppercase letter</span>
                                </li>
                                <li class="flex items-center" id="req-special">
                                    <span class="w-4 h-4 mr-2 flex items-center justify-center">
                                        <span class="w-2 h-2 bg-gray-300 rounded-full requirement-dot"></span>
                                        <svg class="w-4 h-4 text-green-500 hidden requirement-check" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                    </span>
                                    <span class="requirement-text">At least 1 special character</span>
                                </li>
                            </ul>
                        </div>

                        <a class="absolute top-2 right-0 flex items-center pr-3 cursor-pointer" onclick="togglePassword('password', this)">
                            <!-- Eye icon (shows when password is visible) - initially hidden -->
                            <svg class="h-5 w-5 text-gray-400 show-password hidden" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M15 12C15 13.6569 13.6569 15 12 15C10.3431 15 9 13.6569 9 12C9 10.3431 10.3431 9 12 9C13.6569 9 15 10.3431 15 12Z" stroke="#21272A" stroke-width="2"/>
                                <path d="M6.34315 7.34315C4.84285 8.84344 4 11 4 12C4 13 4.84285 15.1566 6.34315 16.6569C7.84344 18.1571 9.87827 19 12 19C14.1217 19 16.1566 18.1571 17.6569 16.6569C19.1571 15.1566 20 13 20 12C20 11 19.1571 8.84344 17.6569 7.34315C16.1566 5.84285 14.1217 5 12 5C9.87827 5 7.84344 5.84285 6.34315 7.34315Z" stroke="#21272A" stroke-width="2"/>
                            </svg>
                            <!-- Eye with slash icon (shows when password is hidden) - initially visible -->
                            <svg class="h-5 w-5 text-gray-400 hide-password" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M3.99989 3L19.9999 21" stroke="#21272A" stroke-width="2" stroke-linecap="round"/>
                                <path d="M14.8345 16.3775C13.9979 16.7803 13.0351 17 12 17C9.87827 17 7.84344 16.1571 6.34315 14.6569C4.84285 13.1566 4 11 4 10C4 9.29687 4.40519 8.10374 5.19371 7.00396" stroke="#21272A" stroke-width="2" stroke-linecap="round"/>
                                <path d="M8.53113 6.04435C9.5888 5.36033 10.7843 5 12 5C14.1217 5 16.1566 5.84285 17.6569 7.34315C19.1571 8.84344 20 11 20 12C20 12.7031 19.5948 13.8963 18.8063 14.996" stroke="#21272A" stroke-width="2" stroke-linecap="round"/>
                                <path d="M15 12C15 13.6569 13.6569 15 12 15C10.3431 15 9 13.6569 9 12C9 10.3431 10.3431 9 12 9C13.6569 9 15 10.3431 15 12Z" stroke="#21272A" stroke-width="2"/>
                            </svg>
                        </a>
                        <p id="password-error" class="mt-1 text-red-500 text-sm hidden">Please enter a valid password.</p>
                    </div>

                    <label for="confirm_password" class="block text-16 font-medium text-gray-700 mt-4">Confirm Password</label>
                    <div class="mt-1 relative">
                        <input id="confirm_password" 
                            name="confirm_password" 
                            type="password" 
                            required 
                            class="appearance-none block w-full px-4 py-3 border border-[#E6E9F5] rounded-lg shadow-sm 
                                    placeholder-gray-400 focus:outline-none focus:border-brand-main focus:ring-1 
                                    focus:ring-brand-main !text-neutral-strong !bg-ipt-bg-1 !h-[48px] pr-10"
                            placeholder="*****">
                        <a class="absolute top-2 right-0 flex items-center pr-3 cursor-pointer" onclick="togglePassword('confirm_password', this)">
                            <!-- Eye icon (shows when password is visible) - initially hidden -->
                            <svg class="h-5 w-5 text-gray-400 show-password hidden" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M15 12C15 13.6569 13.6569 15 12 15C10.3431 15 9 13.6569 9 12C9 10.3431 10.3431 9 12 9C13.6569 9 15 10.3431 15 12Z" stroke="#21272A" stroke-width="2"/>
                                <path d="M6.34315 7.34315C4.84285 8.84344 4 11 4 12C4 13 4.84285 15.1566 6.34315 16.6569C7.84344 18.1571 9.87827 19 12 19C14.1217 19 16.1566 18.1571 17.6569 16.6569C19.1571 15.1566 20 13 20 12C20 11 19.1571 8.84344 17.6569 7.34315C16.1566 5.84285 14.1217 5 12 5C9.87827 5 7.84344 5.84285 6.34315 7.34315Z" stroke="#21272A" stroke-width="2"/>
                            </svg>
                            <!-- Eye with slash icon (shows when password is hidden) - initially visible -->
                            <svg class="h-5 w-5 text-gray-400 hide-password" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M3.99989 3L19.9999 21" stroke="#21272A" stroke-width="2" stroke-linecap="round"/>
                                <path d="M14.8345 16.3775C13.9979 16.7803 13.0351 17 12 17C9.87827 17 7.84344 16.1571 6.34315 14.6569C4.84285 13.1566 4 11 4 10C4 9.29687 4.40519 8.10374 5.19371 7.00396" stroke="#21272A" stroke-width="2" stroke-linecap="round"/>
                                <path d="M8.53113 6.04435C9.5888 5.36033 10.7843 5 12 5C14.1217 5 16.1566 5.84285 17.6569 7.34315C19.1571 8.84344 20 11 20 12C20 12.7031 19.5948 13.8963 18.8063 14.996" stroke="#21272A" stroke-width="2" stroke-linecap="round"/>
                                <path d="M15 12C15 13.6569 13.6569 15 12 15C10.3431 15 9 13.6569 9 12C9 10.3431 10.3431 9 12 9C13.6569 9 15 10.3431 15 12Z" stroke="#21272A" stroke-width="2"/>
                            </svg>
                        </a>
                        <p id="confirm-password-error" class="mt-1 text-red-500 text-sm hidden">Passwords do not match.</p>
                    </div>
                </div>
               
                <div>
                    <button type="button" id="submitResetPassword" class="w-full mx-auto flex justify-center py-2 px-4 text-primary-main hover:text-primary-main/80 rounded-md shadow-sm text-16 font-medium bg-brand-main hover:bg-brand-main/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-400">
                        Reset Password
                    </button>   
                </div>
            </form>
        </div>
    </div>
    <!-- /. Page content -->
</main><!-- #page -->
<script>
    jQuery(document).ready(function($) {
        // Password requirements validation
        $('#password').on('input', function() {
            const password = $(this).val();
            const requirementsDiv = $('#password-requirements');

            // Show/hide requirements based on password field content
            if (password.length > 0) {
                requirementsDiv.removeClass('hidden');

                // Check length (12+ characters)
                const hasLength = password.length >= 12;
                updateRequirement('req-length', hasLength);

                // Check lowercase
                const hasLowercase = /[a-z]/.test(password);
                updateRequirement('req-lowercase', hasLowercase);

                // Check uppercase
                const hasUppercase = /[A-Z]/.test(password);
                updateRequirement('req-uppercase', hasUppercase);

                // Check special character
                const hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);
                updateRequirement('req-special', hasSpecial);
            } else {
                requirementsDiv.addClass('hidden');
            }
        });

        function updateRequirement(reqId, isValid) {
            const reqElement = $('#' + reqId);
            const dot = reqElement.find('.requirement-dot');
            const check = reqElement.find('.requirement-check');
            const text = reqElement.find('.requirement-text');

            if (isValid) {
                dot.addClass('hidden');
                check.removeClass('hidden');
                text.addClass('text-green-600');
                text.removeClass('text-gray-600');
            } else {
                dot.removeClass('hidden');
                check.addClass('hidden');
                text.removeClass('text-green-600');
                text.addClass('text-gray-600');
            }
        }

        // Biến cục bộ để lưu token reset password
        let resetPasswordToken = '';

        // Hàm kiểm tra email hợp lệ
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
        
        // Hàm hiển thị lỗi cho trường input
        function showError(fieldId, show, message = null) {
            const errorElement = document.getElementById(fieldId + '-error');
            if (errorElement) {
                if (show) {
                    errorElement.classList.remove('hidden');
                    if (message) {
                        errorElement.textContent = message;
                    }
                } else {
                    errorElement.classList.add('hidden');
                }
            }
        }
        
        // Hàm hiển thị thành công
        function showSuccess() {
            const forgotPassForms = document.querySelectorAll('.forgot-pass-form');
            const forgotPassSuccesses = document.querySelectorAll('.forgot-pass-success');

            // Ẩn tất cả các form
            forgotPassForms.forEach(form => {
                form.classList.add('hidden');
            });

            // Hiện tất cả các message success
            forgotPassSuccesses.forEach(success => {
                success.classList.remove('hidden');
            });
        }
        
        // Xử lý khi nhấn nút Send
        $('#sendResetLinkBtn').on('click', function(e) {
            // Lấy email từ form
            const email = $('#email').val().trim();
            
            // Kiểm tra email
            if (!email) {
                showError('email', true, 'Please enter your email address.');
                return;
            }
            
            if (!isValidEmail(email)) {
                showError('email', true, 'Please enter a valid email address.');
                return;
            }
            
            // Ẩn thông báo lỗi
            showError('email', false);
            
            // Hiển thị trạng thái đang xử lý
            const originalBtnText = $(this).text();
            $(this).text('Processing...').prop('disabled', true);
            
            // Gọi API GraphQL - Sử dụng biến thay vì nhúng trực tiếp
            const queryForgotPass = `
                mutation Auth_forgot_pass($email: String!) {
                    auth_forgot_pass(body: { email: $email, role_id: 2 })
                }
            `;
            
            $.ajax({
                url: iptHomeAjax.ajax_url,
                type: 'POST',
                dataType: 'json',
                data: {
                    action: 'ipt_home_graphql',
                    query: queryForgotPass,
                    variables: JSON.stringify({ email: email })
                },
                success: function(response) {
                    // Khôi phục trạng thái nút
                    $('#sendResetLinkBtn').text(originalBtnText).prop('disabled', false);
                    
                    // Kiểm tra response
                    if (response.data && response.data.auth_forgot_pass) {
                        // Lưu token
                        resetPasswordToken = response.data.auth_forgot_pass;
                        
                        // Lưu token và email vào localStorage để sử dụng sau này
                        localStorage.setItem('resetPasswordToken', resetPasswordToken);
                        localStorage.setItem('resetPasswordEmail', email);
                        
                        // Hiển thị thông báo thành công
                        showSuccess();
                        
                        // Thông báo thành công bằng toast nếu cần
                        if (typeof showSuccessToast === 'function') {
                            showSuccessToast('Password reset OTP has been sent to your email.');
                        }

                        // Hiển thị form với các input: OTP, password, reset password
                        $("#contentResetPasswordForm").show();
                        $("#contentForgotPasswordForm").hide();

                    } else if (response.errors && response.errors.length > 0) {
                        // Check if error is "Not Found" (404) - show generic success message for security
                        const error = response.errors[0];
                        const isNotFoundError = error.code === 404 || error.message === "Not Found";

                        if (isNotFoundError) {
                            // Show generic success message to prevent email enumeration
                            const genericMessage = `If an account matching ${email} exists, you will receive an email.`;
                            showSuccess();

                            // Show generic message in toast
                            if (typeof showSuccessToast === 'function') {
                                showSuccessToast(genericMessage);
                            }

                            // Hide the form and show a generic success state
                            $("#contentResetPasswordForm").show();
                            $("#contentForgotPasswordForm").hide();

                            // Show generic success message on page
                            const successDiv = document.createElement('div');
                            successDiv.className = 'text-center p-6 bg-green-50 border border-green-200 rounded-lg';
                            successDiv.innerHTML = `
                                <div class="text-green-600 mb-4">
                                    <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">Check Your Email</h3>
                                <p class="text-gray-600">${genericMessage}</p>
                                <p class="text-sm text-gray-500 mt-2">Please check your spam folder if you don't see the email.</p>
                            `;

                            // Replace the form content with success message
                            const mainContainer = document.querySelector('main .max-sm\\:px-\\[16px\\]');
                            if (mainContainer) {
                                mainContainer.innerHTML = '';
                                mainContainer.appendChild(successDiv);
                            }
                        } else {
                            // Show actual error for other types of errors
                            const errorMessage = error.message || 'Failed to send reset link. Please try again.';
                            showError('email', true, errorMessage);

                            // Thông báo lỗi bằng toast nếu cần
                            if (typeof showErrorToast === 'function') {
                                showErrorToast(errorMessage);
                            }
                        }
                    } else {
                        // Hiển thị lỗi chung
                        showError('email', true, 'Failed to send reset OTP. Please try again.');

                        // Thông báo lỗi bằng toast nếu cần
                        if (typeof showErrorToast === 'function') {
                            showErrorToast('Failed to send reset OTP. Please try again.');
                        }
                    }
                },
                error: function(xhr, status, error) {
                    // Khôi phục trạng thái nút
                    $('#sendResetLinkBtn').text(originalBtnText).prop('disabled', false);
                    
                    // Hiển thị lỗi
                    showError('email', true, 'An error occurred. Please try again later.');
                    
                    // Thông báo lỗi bằng toast nếu cần
                    if (typeof showErrorToast === 'function') {
                        showErrorToast('An error occurred. Please try again later.');
                    }
                    
                }
            });
        });
        
        // Xử lý khi nhấn Enter trong form
        $('#forgotPasswordForm').on('keypress', function(e) {
            if (e.which === 13) { // Enter key
                e.preventDefault();
                $('#sendResetLinkBtn').click();
            }
        });

        // Xử lý khi nhấn nút Submit Reset Password
        $('#submitResetPassword').on('click', function(e) {
            // Lấy dữ liệu từ form
            const otp = $('#otp').val().trim();
            const password = $('#password').val().trim();
            const confirmPassword = $('#confirm_password').val().trim();
            
            // Kiểm tra dữ liệu
            let hasError = false;
            
            if (!otp) {
                if (typeof showErrorToast === 'function') {
                    showErrorToast('Please enter the OTP sent to your email.');
                }
                hasError = true;
            }
            
            if (!password) {
                if (typeof showErrorToast === 'function') {
                    showErrorToast('Please enter a new password.');
                }
                hasError = true;
            }
            
            if (!confirmPassword) {
                if (typeof showErrorToast === 'function') {
                    showErrorToast('Please confirm your new password.');
                }
                hasError = true;
            }
            
            if (password !== confirmPassword) {
                if (typeof showErrorToast === 'function') {
                    showErrorToast('Passwords do not match.');
                }
                hasError = true;
            }
            
            if (hasError) {
                return;
            }
            
            // Lấy token và email từ biến cục bộ hoặc localStorage
            const token = resetPasswordToken || localStorage.getItem('resetPasswordToken');
            const email = localStorage.getItem('resetPasswordEmail');
            
            if (!token) {
                if (typeof showErrorToast === 'function') {
                    showErrorToast('Reset token not found. Please try again.');
                }
                return;
            }
            
            if (!email) {
                if (typeof showErrorToast === 'function') {
                    showErrorToast('Email not found. Please try again.');
                }
                return;
            }
            
            // Hiển thị trạng thái đang xử lý
            const originalBtnText = $(this).text();
            $(this).text('Processing...').prop('disabled', true);
            
            // Gọi API GraphQL để reset password
            const queryResetPass = `
                mutation Auth_reset_pass($token: String!, $otp: String!, $password: String!, $confirm_password: String!) {
                    auth_reset_pass(
                        body: {
                            token: $token
                            otp: $otp
                            password: $password
                            confirm_password: $confirm_password
                        }
                    )
                }
            `;
            
            const variables = {
                token: token,
                otp: otp,
                password: password,
                confirm_password: confirmPassword
            };
            
            $.ajax({
                url: iptHomeAjax.ajax_url,
                type: 'POST',
                dataType: 'json',
                data: {
                    action: 'ipt_home_graphql',
                    query: queryResetPass,
                    variables: JSON.stringify(variables)
                },
                success: function(response) {
                    // Kiểm tra response
                    if (response.data && response.data.auth_reset_pass === true) {
                        // Cập nhật mật khẩu WordPress
                        $.ajax({
                            url: iptHomeAjax.ajax_url,
                            type: 'POST',
                            dataType: 'json',
                            data: {
                                action: 'update_wp_user_password',
                                email: email,
                                password: password
                            },
                            success: function(wpResponse) {
                                // Khôi phục trạng thái nút
                                $('#submitResetPassword').text(originalBtnText).prop('disabled', false);
                                
                                if (wpResponse.success) {
                                    // Xóa token và email từ localStorage vì đã sử dụng xong
                                    localStorage.removeItem('resetPasswordToken');
                                    localStorage.removeItem('resetPasswordEmail');
                                    
                                    // Thông báo thành công
                                    if (typeof showSuccessToast === 'function') {
                                        showSuccessToast('Password has been reset successfully.');
                                    }
                                    
                                    // Chuyển hướng đến trang đăng nhập sau 1.5 giây
                                    setTimeout(function() {
                                        window.location.href = '<?php echo get_permalink(41); ?>';
                                    }, 1500);
                                } else {
                                    // Hiển thị lỗi từ WordPress
                                    if (typeof showErrorToast === 'function') {
                                        showErrorToast(wpResponse.data.message || 'Failed to update WordPress password.');
                                    }
                                }
                            },
                            error: function(xhr, status, error) {
                                // Khôi phục trạng thái nút
                                $('#submitResetPassword').text(originalBtnText).prop('disabled', false);
                                
                                // Hiển thị lỗi
                                if (typeof showErrorToast === 'function') {
                                    showErrorToast('Failed to update WordPress password.');
                                }
                                
                            }
                        });
                    } else if (response.errors && response.errors.length > 0) {
                        // Khôi phục trạng thái nút
                        $('#submitResetPassword').text(originalBtnText).prop('disabled', false);
                        
                        // Hiển thị lỗi từ GraphQL
                        let errorMessage = response.errors[0].message || 'Failed to reset password. Please try again.';

                        if( response.errors[0].message === 'Bad Request') {
                            errorMessage = 'Incorrect OTP.';
                        }
                        
                        if (typeof showErrorToast === 'function') {
                            showErrorToast(errorMessage);
                        }
                    } else {
                        // Khôi phục trạng thái nút
                        $('#submitResetPassword').text(originalBtnText).prop('disabled', false);
                        
                        // Hiển thị lỗi chung
                        if (typeof showErrorToast === 'function') {
                            showErrorToast('Failed to reset password. Please try again.');
                        }
                    }
                },
                error: function(xhr, status, error) {
                    // Khôi phục trạng thái nút
                    $('#submitResetPassword').text(originalBtnText).prop('disabled', false);
                    
                    // Hiển thị lỗi
                    if (typeof showErrorToast === 'function') {
                        showErrorToast('An error occurred. Please try again later.');
                    }
                    
                }
            });
        });
    });
    // Hàm hiển thị/ẩn mật khẩu
    function togglePassword(inputId, iconElement) {
        const input = document.getElementById(inputId);
        const showIcon = iconElement.querySelector('.show-password');
        const hideIcon = iconElement.querySelector('.hide-password');

        if (input.type === 'password') {
            // Password is currently hidden, so show it
            input.type = 'text';
            // Show the "visible" icon (eye) since password is now visible
            showIcon.classList.remove('hidden');
            hideIcon.classList.add('hidden');
        } else {
            // Password is currently visible, so hide it
            input.type = 'password';
            // Show the "hidden" icon (eye with slash) since password is now hidden
            showIcon.classList.add('hidden');
            hideIcon.classList.remove('hidden');
        }
    }
</script>
<?php
get_footer(); 
