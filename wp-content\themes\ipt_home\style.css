/*
Theme Name: IPT Home
Theme URI: http://ipt-home.savvycom.click/
Description: IPT Home
Author: Savvycom
Author URI: https://savvycom.vn/
Template: astra
Version: 1.0
Text Domain: ipt-home
*/

/* Custom CSS for SSO Widget Container */
.sso-widget-container {
    padding: 0;
    margin: 0;
    background: none;
    border: none;
}

.sso-widget-container .widget {
    margin: 0;
    padding: 0;
    background: none;
    border: none;
}

.sso-widget-container .sso-widget {
    text-align: center;
}

/* Hide the script tag and font elements */
.sso-widget-container script,
.sso-widget-container font,
.sso-widget-container input[type="hidden"] {
    display: none;
}

/* Style the SSO login link */
.sso-widget-container a {
    color: #dc3545 !important; /* Red color */
    text-decoration: none;
    font-size: 0; /* Hide original text */
    font-weight: 500;
    display: inline-block;
    padding: 10px 20px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.sso-widget-container a:before {
    content: "Continue with SSO";
    font-size: 16px;
    color: #dc3545;
}

.sso-widget-container a:hover {
    color: #c82333 !important; /* Darker red on hover */
    text-decoration: none;
}

.sso-widget-container a:focus {
    outline: none;
    color: #c82333 !important;
}

/* Social Login Widget Styles */
.social-login-widget {
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-width: 300px;
    margin: 0 auto;
}

.social-login-widget .btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: white;
    color: #333;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.social-login-widget .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    text-decoration: none;
}

.social-login-widget .btn-google {
    border-color: #4285F4;
}

.social-login-widget .btn-google:hover {
    background-color: #f8f9ff;
    border-color: #3367d6;
}

.social-login-widget .btn-facebook {
    border-color: #1877F2;
}

.social-login-widget .btn-facebook:hover {
    background-color: #f0f4ff;
    border-color: #166fe5;
}

.social-login-widget .btn-logout {
    background-color: #dc3545;
    color: white;
    border-color: #dc3545;
}

.social-login-widget .btn-logout:hover {
    background-color: #c82333;
    border-color: #bd2130;
    color: white;
}

.social-login-widget.logged-in {
    text-align: center;
}

.social-login-widget.logged-in p {
    margin-bottom: 15px;
    color: #333;
    font-weight: 500;
}

/* Checkout form */

/* Checkout Page Styling */
.woocommerce-checkout-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 30px 20px;
}

.woocommerce-checkout .col2-set {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 30px;
}

.woocommerce-checkout .col-1,
.woocommerce-checkout .col-2 {
    flex: 1;
    min-width: 48%;
    padding: 20px;
    background: #f8f8f8;
    border-radius: 5px;
    margin: 0 10px 20px 0;
}

#order_review_heading {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
    font-size: 24px;
}

#order_review {
    background: #f8f8f8;
    padding: 20px;
    border-radius: 5px;
}

.woocommerce-checkout .form-row label {
    font-weight: 500;
    margin-bottom: 8px;
    display: block;
}

.woocommerce-checkout .form-row input,
.woocommerce-checkout .form-row select,
.woocommerce-checkout .form-row textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.woocommerce-checkout button[type="submit"] {
    background-color: var(--brand-main, #4CAF50);
    color: white;
    padding: 12px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    width: 100%;
    margin-top: 20px;
}

/* Responsive */
@media (max-width: 768px) {
    .woocommerce-checkout .col2-set {
        flex-direction: column;
    }
    
    .woocommerce-checkout .col-1,
    .woocommerce-checkout .col-2 {
        width: 100%;
        margin-right: 0;
    }
}
