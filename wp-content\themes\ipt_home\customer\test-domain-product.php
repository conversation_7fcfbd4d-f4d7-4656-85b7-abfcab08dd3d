<?php
/**
 * Test page for dynamic domain product creation
 * This demonstrates how the new domain product system works
 */

// Ensure user is logged in
if (!is_user_logged_in()) {
    wp_redirect(wp_login_url());
    exit;
}

// Handle form submission
if ($_POST && isset($_POST['create_domain_product'])) {
    $domain = sanitize_text_field($_POST['domain']);
    $price = floatval($_POST['price']);
    
    if (!empty($domain) && $price > 0) {
        $product_id = create_or_update_domain_product($domain, $price);
        $success_message = "Domain product created/updated successfully! Product ID: $product_id";
    } else {
        $error_message = "Please enter valid domain and price.";
    }
}

// Get current domain product if exists
$existing_products = get_posts([
    'post_type' => 'product',
    'meta_query' => [
        [
            'key' => '_domain_product',
            'value' => 'yes',
            'compare' => '='
        ]
    ],
    'posts_per_page' => 1
]);

$current_product = null;
if (!empty($existing_products)) {
    $current_product = wc_get_product($existing_products[0]->ID);
}

?>

<?php include get_stylesheet_directory() . '/customer/header-customer.php'; ?>
<?php include get_stylesheet_directory() . '/customer/header-dashboard.php'; ?>

<div class="flex min-h-screen bg-neutral-light">
   <?php include get_stylesheet_directory() . '/customer/sidebar.php'; ?>
   <div class="flex-1 flex flex-col min-h-screen">
      
      <!-- Breadcrumb -->
      <div class="bg-white border-b border-gray-200 px-6 py-4">
         <nav aria-label="Breadcrumb">
            <ol class="flex items-center space-x-2 text-sm list-none ml-0 mb-1">
               <li><a href="<?php echo site_url('/customer/'); ?>" class="text-gray-500 hover:text-gray-700">Dashboard</a></li>
               <li class="text-gray-400">/</li>
               <li class="text-gray-900">Test Domain Product</li>
            </ol>
         </nav>
      </div>

      <!-- Main Content -->
      <main class="flex-1 p-4 bg-db-dark-2">
         <div class="bg-white rounded-lg shadow p-6">
            <h1 class="text-2xl font-bold mb-6">Test Dynamic Domain Product</h1>
            
            <?php if (isset($success_message)): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    <?php echo esc_html($success_message); ?>
                </div>
            <?php endif; ?>
            
            <?php if (isset($error_message)): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    <?php echo esc_html($error_message); ?>
                </div>
            <?php endif; ?>

            <!-- Current Product Info -->
            <?php if ($current_product): ?>
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <h3 class="text-lg font-semibold text-blue-800 mb-2">Current Domain Product</h3>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div><strong>Product ID:</strong> <?php echo $current_product->get_id(); ?></div>
                        <div><strong>Name:</strong> <?php echo esc_html($current_product->get_name()); ?></div>
                        <div><strong>Price:</strong> $<?php echo number_format($current_product->get_price(), 2); ?></div>
                        <div><strong>Current Domain:</strong> <?php echo esc_html($current_product->get_meta('_current_domain')); ?></div>
                        <div><strong>Last Updated:</strong> <?php echo esc_html($current_product->get_meta('_last_updated')); ?></div>
                        <div><strong>Status:</strong> <?php echo esc_html($current_product->get_status()); ?></div>
                    </div>
                </div>
            <?php else: ?>
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <p class="text-yellow-800">No domain product exists yet. Create one below.</p>
                </div>
            <?php endif; ?>

            <!-- Create/Update Form -->
            <div class="border border-gray-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold mb-4">Create/Update Domain Product</h3>
                
                <form method="POST" class="space-y-4">
                    <div>
                        <label for="domain" class="block text-sm font-medium text-gray-700 mb-2">Domain Name</label>
                        <input type="text" id="domain" name="domain" 
                               placeholder="example.com" 
                               value="<?php echo $current_product ? esc_attr($current_product->get_meta('_current_domain')) : ''; ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" 
                               required>
                    </div>
                    
                    <div>
                        <label for="price" class="block text-sm font-medium text-gray-700 mb-2">Price (USD)</label>
                        <input type="number" id="price" name="price" 
                               step="0.01" min="0.01" 
                               placeholder="12.99"
                               value="<?php echo $current_product ? esc_attr($current_product->get_price()) : ''; ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" 
                               required>
                    </div>
                    
                    <button type="submit" name="create_domain_product" value="1"
                            class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors">
                        <?php echo $current_product ? 'Update' : 'Create'; ?> Domain Product
                    </button>
                </form>
            </div>

            <!-- How It Works -->
            <div class="mt-8 border-t pt-6">
                <h3 class="text-lg font-semibold mb-4">How the Dynamic Domain Product System Works</h3>
                <div class="space-y-3 text-sm text-gray-600">
                    <p><strong>1. Single Product:</strong> Creates one "Purchase Domain" product that changes dynamically</p>
                    <p><strong>2. Price Updates:</strong> When customer selects a domain, the product price updates to match</p>
                    <p><strong>3. No WooCommerce Orders:</strong> Uses direct Stripe API to avoid creating WooCommerce orders</p>
                    <p><strong>4. Domain Registration:</strong> After payment, calls Namecheap API to register the domain</p>
                    <p><strong>5. Status Tracking:</strong> Tracks domain status in custom database table</p>
                </div>
            </div>

            <!-- Test Examples -->
            <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="border border-gray-200 rounded p-4">
                    <h4 class="font-medium mb-2">Test .com Domain</h4>
                    <p class="text-sm text-gray-600 mb-3">example.com - $12.99</p>
                    <button onclick="testDomain('example.com', 12.99)" 
                            class="text-blue-600 hover:text-blue-800 text-sm">Test This</button>
                </div>
                
                <div class="border border-gray-200 rounded p-4">
                    <h4 class="font-medium mb-2">Test .net Domain</h4>
                    <p class="text-sm text-gray-600 mb-3">mysite.net - $15.99</p>
                    <button onclick="testDomain('mysite.net', 15.99)" 
                            class="text-blue-600 hover:text-blue-800 text-sm">Test This</button>
                </div>
                
                <div class="border border-gray-200 rounded p-4">
                    <h4 class="font-medium mb-2">Test .org Domain</h4>
                    <p class="text-sm text-gray-600 mb-3">nonprofit.org - $14.99</p>
                    <button onclick="testDomain('nonprofit.org', 14.99)" 
                            class="text-blue-600 hover:text-blue-800 text-sm">Test This</button>
                </div>
            </div>
         </div>
      </main>
   </div>
</div>

<script>
function testDomain(domain, price) {
    document.getElementById('domain').value = domain;
    document.getElementById('price').value = price;
}
</script>

<?php include get_stylesheet_directory() . '/customer/footer-customer.php'; ?>
