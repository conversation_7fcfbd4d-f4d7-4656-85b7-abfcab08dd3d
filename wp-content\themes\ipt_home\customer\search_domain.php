<?php
/**
 * Domain Search Page with Local Database Pricing
 *
 * Features:
 * - Comprehensive TLD support (200+ domains including Singapore and worldwide)
 * - Real-time domain availability checking via Namecheap API
 * - Fast local database pricing lookups (updated every 6 hours)
 * - Background pricing updates via WordPress Cron
 * - Support for premium domains
 *
 * TLD Coverage:
 * - Generic TLDs: .com, .net, .org, .info, .biz, etc.
 * - New gTLDs: .app, .dev, .tech, .online, .store, .blog, etc.
 * - Country codes: .sg (Singapore), .us, .uk, .ca, .au, .de, .fr, .jp, .cn, .in, etc.
 * - Specialty TLDs: .ai, .crypto, .nft, .finance, .health, .education, etc.
 *
 * Pricing System:
 * - Local database pricing for fast searches
 * - Background API updates every 6 hours
 * - Manual pricing update available for admins
 */

// Include the domain pricing system
require_once get_stylesheet_directory() . '/includes/domain-pricing-system.php';

// Ensure domain tables exist before loading page
ipt_ensure_domain_tables_exist();

// Get current pricing data from database for JavaScript
$current_pricing = get_domain_pricing_from_db();
$pricing_stats = get_domain_pricing_stats();

// AJAX endpoint to get fresh pricing data
add_action('wp_ajax_get_domain_pricing', 'ajax_get_domain_pricing');
add_action('wp_ajax_nopriv_get_domain_pricing', 'ajax_get_domain_pricing');

function ajax_get_domain_pricing() {
    $pricing = get_domain_pricing_from_db();
    $stats = get_domain_pricing_stats();

    wp_send_json_success(array(
        'pricing' => $pricing,
        'stats' => $stats,
        'count' => count($pricing)
    ));
}

include get_stylesheet_directory() . '/customer/header-customer.php';
include get_stylesheet_directory() . '/customer/header-dashboard.php';
?>

<div class="flex min-h-screen bg-neutral-light">
   <?php include get_stylesheet_directory() . '/customer/sidebar.php'; ?>
   <div class="flex-1 flex flex-col min-h-screen">

      <!-- Search Domain -->
      <main class="flex-1 p-4 bg-db-dark-2">
         <div class="bg-white rounded-lg shadow p-6">
            <!-- Header with navigation -->
            <!-- <div class="flex items-center mb-6">
               <div class="flex items-center text-sm text-gray-500">
                  <span>Settings</span>
                  <svg class="mx-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                     <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                  <span>Domain management</span>
                  <svg class="mx-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                     <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                  <span>Purchase a New Domain</span>
               </div>
            </div> -->
            <nav class="" aria-label="Breadcrumb">
               <ol class="flex items-center space-x text-sm list-none ml-0 mb-1">
                  <li>
                     <a href="<?php echo site_url('/customer/subscription'); ?>" class="font-normal hover:text-db-text text-db-text  text-14 ">Settings</a>
                  </li>
                  <li>
                     <span class="text-neutral-400 text-12">
                        <svg width="22" height="30" viewBox="0 0 22 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                           <path fill-rule="evenodd" clip-rule="evenodd" d="M8 8.7L12.6 15L8 21.3L8.8 22L14 15L8.8 8L8 8.7Z" fill="#000624" fill-opacity="0.3"/>
                        </svg>
                     </span>
                  </li>
                  <li>
                     <a href="<?php echo site_url('/customer/domain_management'); ?>" class="font-normal hover:text-db-text text-db-text text-14">Domain management</a>
                  </li>
                  <li>
                     <span class="text-neutral-400 text-12">
                        <svg width="22" height="30" viewBox="0 0 22 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                           <path fill-rule="evenodd" clip-rule="evenodd" d="M8 8.7L12.6 15L8 21.3L8.8 22L14 15L8.8 8L8 8.7Z" fill="#000624" fill-opacity="0.3"/>
                        </svg>
                     </span>
                  </li>
                  <li>
                     <a href="#" class="font-normal hover:text-db-text text-db-text text-14">Purchase a New Domain</a>
                  </li>
               </ol>
            </nav>

            <!-- Page Title -->
            <h1 class="text-2xl font-semibold text-gray-900 mb-8">Purchase a New Domain</h1>

            <?php if (current_user_can('manage_options') && $pricing_stats): ?>
            <!-- Admin Pricing Info -->
            <div class="mb-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-blue-800">
                            <strong>Admin Info:</strong> Pricing database contains <?php echo $pricing_stats['total_tlds']; ?> TLDs.
                            Last updated: <?php echo $pricing_stats['newest_update'] ? date('M j, Y g:i A', strtotime($pricing_stats['newest_update'])) : 'Never'; ?>
                        </p>
                    </div>
                    <a href="<?php echo admin_url('tools.php?page=domain-pricing'); ?>" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        Manage Pricing →
                    </a>
                </div>
            </div>
            <?php endif; ?>

            <!-- Debug IP Information -->
            <?php if (current_user_can('administrator')): ?>
            <div class="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
               <div class="flex items-center">
                  <svg class="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                     <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <span class="text-yellow-800 font-medium">Debug Info (Admin Only)</span>
               </div>
               <div class="mt-2 text-sm text-yellow-700">
                  <p><strong>Server IP (HTTP_X_FORWARDED_FOR):</strong> <?php echo isset($_SERVER['HTTP_X_FORWARDED_FOR']) ? $_SERVER['HTTP_X_FORWARDED_FOR'] : 'Not set'; ?></p>
                  <p><strong>Server IP (REMOTE_ADDR):</strong> <?php echo isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'Not set'; ?></p>
                  <p><strong>Server IP (SERVER_ADDR):</strong> <?php echo isset($_SERVER['SERVER_ADDR']) ? $_SERVER['SERVER_ADDR'] : 'Not set'; ?></p>
                  <p><strong>Outbound IP Check:</strong> <span id="outbound-ip">Checking...</span></p>
               </div>
            </div>
            <?php endif; ?>



            <!-- Domain Search Section -->
            <div class="max-w-4xl mx-auto text-center">
               <!-- Main Heading -->
               <h2 class="text-3xl font-semibold text-gray-900 mb-12">Get a Domain Name for Your Site</h2>

               <!-- Search Form -->
               <div class="max-w-4xl mx-auto">
                  <form id="domain-search-form" class="flex items-center bg-gray-100 rounded-xl p-2">
                     <div class="flex items-center flex-1 relative">
                        <div class="absolute left-4 flex items-center pointer-events-none">
                           <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                           </svg>
                        </div>
                        <input
                           type="text"
                           id="domain_search"
                           name="domain_search"
                           placeholder="Enter your domain name"
                           style="padding-left: 40px;"
                           class="w-full px-12 pr-4 py-3 bg-transparent border-0 text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-0 text-base"
                        />
                     </div>
                     <button
                        type="submit"
                        id="search-btn"
                        class="ml-2 px-6 py-3 bg-blue-400 text-white font-medium text-base rounded-lg hover:bg-green-500 focus:outline-none transition-colors"
                     >
                        Search
                     </button>
                  </form>

                  <!-- Custom Domain Connection Link -->
                  <div class="text-center mt-4">
                     <p class="text-gray-600 text-sm">
                        Already have a domain?
                        <a href="<?php echo site_url('/customer/connect_custom_domain/?website_id=' . (isset($_GET['website_id']) ? intval($_GET['website_id']) : '')); ?>" class="text-blue-600 hover:text-blue-800 underline font-medium">
                           Connect your existing domain
                        </a>
                     </p>
                  </div>
               </div>

               <!-- Search Results Section -->
               <div id="search-results" class="mt-12 hidden">
                  <h3 id="results-title" class="text-xl font-semibold text-gray-900 mb-6">Available Domains</h3>
                  <div id="search-info" class="mb-4 text-sm text-gray-600 hidden">
                     <!-- Search information will be shown here -->
                  </div>
                  <div id="results-container" class="space-y-4">
                     <!-- Results will be populated here -->
                  </div>
               </div>

               <!-- Loading State -->
               <div id="loading-state" class="mt-12 hidden">
                  <div class="flex items-center justify-center">
                     <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
                     <span class="ml-3 text-gray-600">Searching for available domains...</span>
                  </div>
               </div>

               <!-- No Results State -->
               <div id="no-results" class="mt-12 hidden">
                  <div class="text-center text-gray-500">
                     <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                     </svg>
                     <h3 class="text-lg font-medium text-gray-900 mb-2">No domains found</h3>
                     <p class="text-gray-600 mb-4">We couldn't find any available domains for your search.</p>
                     <div id="suggestions-container" class="text-sm text-gray-500">
                        <p class="mb-2">Try:</p>
                        <ul class="list-disc list-inside space-y-1">
                           <li>Using a different domain name</li>
                           <li>Checking your spelling</li>
                           <li>Trying a shorter or longer name</li>
                           <li>Using different keywords</li>
                        </ul>
                     </div>
                     <div id="alternative-suggestions" class="mt-6 hidden">
                        <p class="text-sm text-gray-600 mb-3">Or try these alternatives:</p>
                        <div id="alternative-domains" class="flex flex-wrap justify-center gap-2">
                           <!-- Alternative domain suggestions will be added here -->
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </main>
   </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('domain-search-form');
    const searchInput = document.getElementById('domain_search');
    const searchBtn = document.getElementById('search-btn');
    const loadingState = document.getElementById('loading-state');
    const searchResults = document.getElementById('search-results');
    const resultsContainer = document.getElementById('results-container');
    const noResults = document.getElementById('no-results');

    // Load pricing data from database
    window.currentPricing = <?php echo json_encode($current_pricing); ?>;
    window.pricingStats = <?php echo json_encode($pricing_stats); ?>;

    console.log('Loaded pricing for', Object.keys(window.currentPricing).length, 'TLDs from database');
    console.log('Pricing last updated:', window.pricingStats?.newest_update || 'Unknown');


    // Handle form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const searchTerm = searchInput.value.trim();
        if (!searchTerm) {
            alert('Please enter a domain name to search');
            return;
        }

        // Show loading state
        showLoadingState();

        // Start domain search immediately (no artificial delay)
        searchDomains(searchTerm);
    });

    function showLoadingState() {
        hideAllStates();
        loadingState.classList.remove('hidden');
        searchBtn.disabled = true;
        searchBtn.textContent = 'Searching...';

        // Update loading message to be more informative
        const loadingMessage = loadingState.querySelector('span');
        if (loadingMessage) {
            loadingMessage.textContent = 'Checking domain availability...';
        }
    }

    function hideAllStates() {
        loadingState.classList.add('hidden');
        searchResults.classList.add('hidden');
        noResults.classList.add('hidden');

        // Also hide search info
        const searchInfo = document.getElementById('search-info');
        if (searchInfo) {
            searchInfo.classList.add('hidden');
        }
    }

    function searchDomains(searchTerm) {
        console.log('🔍 STEP 1: Domain Search Started');
        console.log('📝 Search Term:', searchTerm);
        console.log('💰 Using local database pricing for', Object.keys(window.currentPricing).length, 'TLDs');
        console.log('📊 Available TLDs:', Object.keys(window.currentPricing));

        // Directly proceed with domain availability check
        performDomainAvailabilityCheck(searchTerm);
    }

    /**
     * Parse domain input to detect if user entered domain with extension
     * @param {string} input - User input (e.g., "devthienxm" or "devthienxm.sg")
     * @returns {object} - Parsed domain information
     */
    function parseDomainInput(input) {
        // Clean the input
        const cleanInput = input.trim().toLowerCase();

        // List of known TLDs from our pricing database
        const knownTlds = Object.keys(window.currentPricing || {});

        // Add common TLDs that might not be in pricing database
        const commonTlds = ['com', 'net', 'org', 'info', 'biz', 'tech', 'club', 'top', 'site', 'online', 'sg', 'us', 'uk', 'ca', 'au', 'de', 'fr', 'jp', 'cn', 'in'];
        const allTlds = [...new Set([...knownTlds, ...commonTlds])];

        // Check if input contains a dot and ends with a known TLD
        if (cleanInput.includes('.')) {
            const parts = cleanInput.split('.');
            if (parts.length >= 2) {
                const possibleTld = parts[parts.length - 1];

                // Check if the last part is a known TLD
                if (allTlds.includes(possibleTld)) {
                    const domainName = parts.slice(0, -1).join('.');

                    return {
                        hasExtension: true,
                        domainName: domainName,
                        tld: possibleTld,
                        fullDomain: cleanInput,
                        originalInput: input
                    };
                }
            }
        }

        // Input doesn't contain a valid TLD extension
        return {
            hasExtension: false,
            domainName: cleanInput,
            tld: null,
            fullDomain: null,
            originalInput: input
        };
    }

    function performDomainAvailabilityCheck(searchTerm) {
        // Check if user entered a domain with extension (e.g., "devthienxm.sg")
        const domainParts = parseDomainInput(searchTerm);
        let domainList;

        if (domainParts.hasExtension) {
            // User entered domain with extension - search only for that specific domain
            console.log('🎯 STEP 2A: Specific Domain Search');
            console.log('📝 Full Domain:', domainParts.fullDomain);
            domainList = domainParts.fullDomain;
        } else {
            // User entered domain name only - search multiple TLDs
            console.log('🎯 STEP 2B: Multi-TLD Search');
            console.log('📝 Domain Name:', domainParts.domainName);
            const tlds = [
                // Generic top-level domains (gTLDs)
                'com', 'net', 'org', 'info', 'biz', 'tech',
                'club', 'top', 'site', 'online',
                // Country code top-level domains (ccTLDs) - Major countries
                'sg'
            ];
            domainList = tlds.map(tld => `${domainParts.domainName}.${tld}`).join(',');
            console.log('🔍 Searching TLDs:', tlds);
            console.log('📋 Domain List:', domainList);
        }

        // Build Namecheap API query string for domain availability check
        const queryString = `Command=namecheap.domains.check&DomainList=${domainList}`;

        // GraphQL query for domain availability
        const query = `
            query Name_cheap_index($queryString: String!) {
                name_cheap_index(query_string: $queryString)
            }
        `;

        // Also prepare pricing query for accurate pricing (optional enhancement)
        const pricingQueryString = `Command=namecheap.users.getPricing&ProductType=DOMAIN&ProductCategory=REGISTER`;
        const pricingQuery = `
            query Name_cheap_index($queryString: String!) {
                name_cheap_index(query_string: $queryString)
            }
        `;

        // Make API call for domain availability
        console.log('🌐 STEP 3: API Call to Namecheap');
        console.log('📡 API Endpoint:', 'https://api-weaveform.ip-tribe.com/graphql');
        console.log('📝 Query String:', queryString);
        console.log('🔍 GraphQL Query:', query);

        const apiStartTime = Date.now();

        fetch('https://api-weaveform.ip-tribe.com/graphql', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                query: query,
                variables: {
                    queryString: queryString
                }
            })
        })
        .then(response => {
            const apiEndTime = Date.now();
            console.log('⏱️ API Response Time:', (apiEndTime - apiStartTime) + 'ms');
            console.log('📊 Response Status:', response.status, response.statusText);
            return response.json();
        })
        .then(data => {
            console.log('📦 STEP 4: API Response Received');
            console.log('📄 Full Response:', data);

            // Reset button state
            searchBtn.disabled = false;
            searchBtn.textContent = 'Search';

            if (data.errors) {
                console.log('❌ API Errors:', data.errors);
                showNoResults();
                return;
            }

            if (data.data && data.data.name_cheap_index) {
                console.log('✅ Valid API Response - Processing Results');
                console.log('📋 Raw Namecheap Data:', data.data.name_cheap_index);
                parseNamecheapResponse(data.data.name_cheap_index);
            } else {
                console.log('❌ No valid data in response');
                showNoResults();
            }
        })
        .catch(error => {
            console.log('❌ STEP 4: API Error');
            console.error('🚨 Error searching domains:', error);
            console.log('📊 Error Details:', {
                message: error.message,
                stack: error.stack,
                timestamp: new Date().toISOString()
            });

            // Reset button state
            searchBtn.disabled = false;
            searchBtn.textContent = 'Search';

            showNoResults();
        });
    }



    function parseNamecheapResponse(apiResponse) {
        try {

            // Handle the specific response format from your API
            if (typeof apiResponse === 'object' && apiResponse.ApiResponse) {
                const response = apiResponse.ApiResponse;

                // Check API status
                if (response.$ && response.$.Status === 'ERROR') {
                    // Log error details for debugging but show user-friendly message
                    let errorMsg = 'Unknown API error';

                    if (response.Errors && response.Errors.Error) {
                        if (response.Errors.Error._) {
                            errorMsg = response.Errors.Error._;
                        } else if (typeof response.Errors.Error === 'string') {
                            errorMsg = response.Errors.Error;
                        }
                    }

                    console.log('Domain search API error:', errorMsg);
                    showNoResults();
                    return;
                }

                // If successful, look for domain results
                if (response.CommandResponse && response.CommandResponse.DomainCheckResult) {
                    const domainResults = response.CommandResponse.DomainCheckResult;
                    parseDomainResults(domainResults);
                    return;
                }
            }

            console.log('Invalid response format from domain search service');
            showNoResults();

        } catch (error) {
            console.log('Error processing domain search results:', error);
            showNoResults();
        }
    }

    function parseDomainResults(domainResults) {
        const results = [];

        // Handle array or single result
        const domains = Array.isArray(domainResults) ? domainResults : [domainResults];

        domains.forEach(domain => {
            // Extract domain information from the response format
            const domainData = domain.$;
            const domainName = domainData.Domain;
            const isAvailable = domainData.Available === 'true';
            const isPremium = domainData.IsPremiumName === 'true';
            const premiumPrice = parseFloat(domainData.PremiumRegistrationPrice);

            // Get pricing
            let price = 'Contact for price';
            if (isAvailable) {
                if (isPremium && premiumPrice > 0) {
                    price = '$' + premiumPrice.toFixed(2);
                } else {
                    // Always use real-time pricing - no static fallback
                    const tld = domainName.split('.').pop();

                    // Check if we have real-time pricing for this TLD
                    if (window.currentPricing && window.currentPricing[tld]) {
                        price = window.currentPricing[tld];
                    } else {
                        // If real-time pricing is not available for this TLD, show contact message
                        price = 'Contact for price';
                    }
                }
            }

            results.push({
                domain: domainName,
                available: isAvailable,
                price: isAvailable ? price + '/year' : null,
                isPremium: isPremium,
                errorNo: domainData.ErrorNo,
                description: domainData.Description
            });
        });


        // Filter available domains
        const availableDomains = results.filter(result => result.available);

        if (availableDomains.length > 0) {
            showResults(availableDomains);
        } else {
            showNoResults();
        }
    }

    function showResults(domains) {
        hideAllStates();

        // Clear previous results
        resultsContainer.innerHTML = '';

        // Update results title and info based on search type
        const resultsTitle = document.getElementById('results-title');
        const searchInfo = document.getElementById('search-info');

        // Check if this was a specific domain search
        const searchTerm = searchInput.value.trim();
        const domainParts = parseDomainInput(searchTerm);

        if (domainParts.hasExtension) {
            // Specific domain search
            resultsTitle.textContent = `Search Results for "${domainParts.fullDomain}"`;
            searchInfo.innerHTML = `
                <div class="flex items-center">
                    <svg class="w-4 h-4 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>You searched for a specific domain with extension <strong>.${domainParts.tld}</strong></span>
                </div>
            `;
            searchInfo.classList.remove('hidden');
        } else {
            // Multiple TLD search
            resultsTitle.textContent = 'Available Domains';
            searchInfo.innerHTML = `
                <div class="flex items-center">
                    <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>Showing available domains for <strong>"${domainParts.domainName}"</strong> across multiple extensions</span>
                </div>
            `;
            searchInfo.classList.remove('hidden');
        }

        // Add domain results
        domains.forEach(domain => {
            const domainCard = createDomainCard(domain);
            resultsContainer.appendChild(domainCard);
        });

        searchResults.classList.remove('hidden');
    }

    function createDomainCard(domain) {
        const card = document.createElement('div');
        const isPremium = domain.isPremium;
        const borderColor = isPremium ? 'border-yellow-300' : 'border-gray-200';
        const hoverColor = isPremium ? 'hover:border-yellow-500' : 'hover:border-green-500';

        card.className = `flex items-center justify-between p-4 border ${borderColor} rounded-lg ${hoverColor} transition-colors`;

        const premiumBadge = isPremium ? '<span class="ml-2 px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded-full">Premium</span>' : '';
        const priceColor = isPremium ? 'text-yellow-600' : 'text-green-600';
        const buttonColor = isPremium ? 'bg-yellow-500 hover:bg-yellow-600 focus:ring-yellow-500' : 'bg-green-500 hover:bg-green-600 focus:ring-green-500';

        card.innerHTML = `
            <div class="flex items-center">
                <div class="w-3 h-3 ${isPremium ? 'bg-yellow-500' : 'bg-green-500'} rounded-full mr-3"></div>
                <div class="flex items-center">
                    <span class="text-lg font-medium text-gray-900">${domain.domain}</span>
                    ${premiumBadge}
                </div>
            </div>
            <div class="flex items-center gap-4">
                <span class="text-lg font-semibold ${priceColor}">${domain.price}</span>
                <button class="px-6 py-2 ${buttonColor} text-white rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors" onclick="selectDomain('${domain.domain}', '${domain.price}', ${isPremium})">
                    Select
                </button>
            </div>
        `;

        return card;
    }

    function showNoResults() {
        hideAllStates();

        // Update no results message based on search type
        const searchTerm = searchInput.value.trim();
        const domainParts = parseDomainInput(searchTerm);

        const noResultsTitle = noResults.querySelector('h3');
        const noResultsDescription = noResults.querySelector('p');
        const alternativeSuggestions = document.getElementById('alternative-suggestions');
        const alternativeDomains = document.getElementById('alternative-domains');

        if (domainParts.hasExtension) {
            // Specific domain search - show alternatives
            noResultsTitle.textContent = `"${domainParts.fullDomain}" is not available`;
            noResultsDescription.textContent = `The domain ${domainParts.fullDomain} is already taken or not available for registration.`;

            // Show alternative domain suggestions
            showAlternativeDomains(domainParts.domainName, domainParts.tld);
        } else {
            // Multiple TLD search
            noResultsTitle.textContent = 'No domains found';
            noResultsDescription.textContent = `We couldn't find any available domains for "${domainParts.domainName}".`;

            // Hide alternative suggestions for multiple TLD searches
            alternativeSuggestions.classList.add('hidden');
        }

        noResults.classList.remove('hidden');
    }

    /**
     * Show alternative domain suggestions when a specific domain is not available
     */
    function showAlternativeDomains(domainName, originalTld) {
        const alternativeSuggestions = document.getElementById('alternative-suggestions');
        const alternativeDomains = document.getElementById('alternative-domains');

        // Clear previous suggestions
        alternativeDomains.innerHTML = '';

        // Suggest popular alternative TLDs
        const alternativeTlds = ['com', 'net', 'org', 'info', 'biz', 'tech', 'online', 'site'];

        // Remove the original TLD from alternatives
        const filteredTlds = alternativeTlds.filter(tld => tld !== originalTld);

        // Create clickable alternative domain buttons
        filteredTlds.slice(0, 6).forEach(tld => {
            const alternativeDomain = `${domainName}.${tld}`;
            const button = document.createElement('button');
            button.className = 'px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm hover:bg-blue-200 transition-colors';
            button.textContent = alternativeDomain;
            button.onclick = () => {
                // Auto-search for the alternative domain
                searchInput.value = alternativeDomain;
                form.dispatchEvent(new Event('submit'));
            };
            alternativeDomains.appendChild(button);
        });

        // Show the suggestions section
        alternativeSuggestions.classList.remove('hidden');
    }

    // Global function for domain selection
    window.selectDomain = function(domain, price, isPremium = false) {
        const domainType = isPremium ? 'premium domain' : 'domain';
        const confirmMessage = `Do you want to purchase this ${domainType} ${domain} for ${price}?`;

        if (confirm(confirmMessage)) {
            console.log('🛒 STEP 5: Domain Selection Confirmed');
            console.log('📝 Selected Domain:', domain);
            console.log('💰 Price:', price);
            console.log('⭐ Is Premium:', isPremium);

            // First, save the selected domain to database
            console.log('💾 STEP 6: Saving Domain Selection to Database');
            saveDomainSelection(domain, price, isPremium).then(() => {
                console.log('✅ Domain saved successfully');
                console.log('🔄 STEP 7: Redirecting to Payment Page');

                // Then redirect to payment page
                const checkoutUrl = `<?php echo site_url('/customer/create_domain_payment/'); ?>?domain=${encodeURIComponent(domain)}&price=${encodeURIComponent(price)}&premium=${isPremium}`;
                console.log('🌐 Payment URL:', checkoutUrl);
                window.location.href = checkoutUrl;
            }).catch(error => {
                console.log('❌ Failed to save domain selection:', error);
                console.log('🔄 Redirecting anyway...');

                // Still redirect even if saving fails
                const checkoutUrl = `<?php echo site_url('/customer/create_domain_payment/'); ?>?domain=${encodeURIComponent(domain)}&price=${encodeURIComponent(price)}&premium=${isPremium}`;
                console.log('🌐 Payment URL (fallback):', checkoutUrl);
                window.location.href = checkoutUrl;
            });
        } else {
            console.log('❌ Domain selection cancelled by user');
        }
    };

    // Function to save domain selection to database
    async function saveDomainSelection(domain, price, isPremium) {
        console.log('💾 STEP 6A: Starting Domain Save Process');
        console.log('📝 Domain:', domain);
        console.log('💰 Price:', price);
        console.log('⭐ Premium:', isPremium);

        try {
            console.log('📡 Making AJAX request to save domain...');
            const requestData = {
                action: 'save_domain_selection',
                domain: domain,
                price: price,
                is_premium: isPremium,
                security: '<?php echo wp_create_nonce('save_domain_selection_nonce'); ?>'
            };
            console.log('📦 Request Data:', requestData);

            const response = await fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams(requestData)
            });

            console.log('📊 Response Status:', response.status, response.statusText);
            const data = await response.json();
            console.log('📄 Response Data:', data);

            if (data.success) {
                console.log('✅ Domain saved successfully to database');
                return data;
            } else {
                console.log('❌ Failed to save domain:', data.data?.message);
                throw new Error(data.data?.message || 'Failed to save domain selection');
            }
        } catch (error) {
            console.log('🚨 Error in saveDomainSelection:', error);
            throw error;
        }
    }



    // Pricing is now loaded from local database on page load
    // No need for real-time API calls during search

    // Pricing parsing is now handled server-side in the database system

    // Debug function to view current database pricing
    window.viewCurrentPricing = function() {
        if (window.currentPricing) {
            console.log('Current database pricing:', window.currentPricing);
            console.log('Available TLDs:', Object.keys(window.currentPricing).length);
            console.log('Pricing stats:', window.pricingStats);
            return window.currentPricing;
        } else {
            console.log('No pricing data available from database');
            return null;
        }
    };

    // Debug function to manually update pricing (admin only)
    window.updatePricing = function() {
        if (typeof wp !== 'undefined' && wp.ajax) {
            console.log('Updating domain pricing from API...');

            wp.ajax.post('update_domain_pricing', {
                nonce: '<?php echo wp_create_nonce('update_domain_pricing'); ?>'
            }).done(function(response) {
                console.log('Pricing update successful:', response);
                alert('Pricing updated successfully! Refresh the page to see new prices.');
            }).fail(function(error) {
                console.error('Pricing update failed:', error);
                alert('Failed to update pricing. Check console for details.');
            });
        } else {
            console.error('WordPress AJAX not available. Are you logged in as admin?');
        }
    };

    // Auto-focus search input
    searchInput.focus();

    // Check outbound IP for admin users
    if (document.getElementById('outbound-ip')) {
        fetch('https://api.ipify.org?format=json')
            .then(response => response.json())
            .then(data => {
                document.getElementById('outbound-ip').textContent = data.ip;
            })
            .catch(error => {
                document.getElementById('outbound-ip').textContent = 'Failed to check';
            });
    }
});
</script>

<?php include get_stylesheet_directory() . '/customer/footer-customer.php'; ?>
