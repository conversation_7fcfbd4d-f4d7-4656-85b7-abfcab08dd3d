<?php
/**
 * Domain Pricing Admin Page
 * 
 * Provides an admin interface to manage domain pricing updates
 * and view pricing statistics.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Add admin menu
add_action('admin_menu', 'add_domain_pricing_admin_menu');

function add_domain_pricing_admin_menu() {
    add_management_page(
        'Domain Pricing Management',
        'Domain Pricing',
        'manage_options',
        'domain-pricing',
        'domain_pricing_admin_page'
    );
}

function domain_pricing_admin_page() {
    // Include the pricing system
    require_once get_stylesheet_directory() . '/includes/domain-pricing-system.php';
    
    // Handle manual update
    if (isset($_POST['update_pricing']) && wp_verify_nonce($_POST['_wpnonce'], 'update_domain_pricing_manual')) {
        echo '<div class="notice notice-info"><p>Starting pricing update process...</p></div>';

        // Test GraphQL endpoint first
        $graphql_url = 'https://api-weaveform.ip-tribe.com/graphql';
        $test_response = wp_remote_post($graphql_url, array(
            'headers' => array(
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . IPT_API_KEY
            ),
            'body' => json_encode(array(
                'query' => '{ __typename }'
            )),
            'timeout' => 10
        ));

        if (is_wp_error($test_response)) {
            echo '<div class="notice notice-error"><p>Cannot reach GraphQL endpoint: ' . $test_response->get_error_message() . '</p></div>';
        } else {
            $status_code = wp_remote_retrieve_response_code($test_response);
            echo '<div class="notice notice-info"><p>GraphQL endpoint responds with status: ' . $status_code . '</p></div>';
        }

        // Let's debug the API call step by step
        $pricing_query_string = 'Command=namecheap.users.getPricing&ProductType=DOMAIN&ProductCategory=REGISTER';

        $query = '
            query Name_cheap_index($queryString: String!) {
                name_cheap_index(query_string: $queryString)
            }
        ';

        $variables = array('queryString' => $pricing_query_string);

        echo '<div class="notice notice-info"><p>Query: ' . htmlspecialchars($query) . '</p></div>';
        echo '<div class="notice notice-info"><p>Variables: ' . htmlspecialchars(json_encode($variables)) . '</p></div>';

        $response = wp_remote_post($graphql_url, array(
            'headers' => array(
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . IPT_API_KEY
            ),
            'body' => json_encode(array(
                'query' => $query,
                'variables' => $variables
            )),
            'timeout' => 30
        ));

        if (is_wp_error($response)) {
            echo '<div class="notice notice-error"><p>API Request Error: ' . $response->get_error_message() . '</p></div>';
        } else {
            $status_code = wp_remote_retrieve_response_code($response);
            $body = wp_remote_retrieve_body($response);

            echo '<div class="notice notice-info"><p>Response Status: ' . $status_code . '</p></div>';
            echo '<div class="notice notice-info"><p>Response Body (first 1000 chars): <pre>' . htmlspecialchars(substr($body, 0, 1000)) . '</pre></p></div>';

            $data = json_decode($body, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                echo '<div class="notice notice-error"><p>JSON decode error: ' . json_last_error_msg() . '</p></div>';
            } else {
                if (isset($data['errors'])) {
                    echo '<div class="notice notice-error"><p>GraphQL errors: <pre>' . htmlspecialchars(print_r($data['errors'], true)) . '</pre></p></div>';
                }

                if (isset($data['data']['name_cheap_index'])) {
                    echo '<div class="notice notice-success"><p>Found name_cheap_index data! Processing...</p></div>';
                    $pricing_data = fetch_pricing_from_namecheap_api();

                    if ($pricing_data && !empty($pricing_data)) {
                        $updated_count = update_domain_pricing_in_db($pricing_data);
                        echo '<div class="notice notice-success"><p>Successfully updated pricing for ' . $updated_count . ' TLDs!</p></div>';
                    } else {
                        echo '<div class="notice notice-error"><p>Failed to parse pricing data from API response.</p></div>';
                    }
                } else {
                    echo '<div class="notice notice-error"><p>No name_cheap_index found in response. Full data structure: <pre>' . htmlspecialchars(print_r($data, true)) . '</pre></p></div>';
                }
            }
        }
    }
    
    // Get current stats
    $stats = get_domain_pricing_stats();
    $pricing_data = get_domain_pricing_from_db();
    
    ?>
    <div class="wrap">
        <h1>Domain Pricing Management</h1>
        
        <div class="card">
            <h2>Pricing Statistics</h2>
            <table class="widefat">
                <tr>
                    <td><strong>Total TLDs in Database:</strong></td>
                    <td><?php echo $stats['total_tlds'] ?? 0; ?></td>
                </tr>
                <tr>
                    <td><strong>Average Price:</strong></td>
                    <td>$<?php echo number_format($stats['average_price'] ?? 0, 2); ?></td>
                </tr>
                <tr>
                    <td><strong>Last Updated:</strong></td>
                    <td><?php echo $stats['newest_update'] ?? 'Never'; ?></td>
                </tr>
                <tr>
                    <td><strong>Oldest Update:</strong></td>
                    <td><?php echo $stats['oldest_update'] ?? 'Never'; ?></td>
                </tr>
            </table>
        </div>
        
        <div class="card">
            <h2>Manual Pricing Update</h2>
            <p>Click the button below to manually fetch the latest pricing from Namecheap API and update the database.</p>
            <p><strong>Note:</strong> Pricing is automatically updated every 6 hours via WordPress Cron.</p>
            
            <form method="post" action="">
                <?php wp_nonce_field('update_domain_pricing_manual'); ?>
                <input type="submit" name="update_pricing" class="button button-primary" value="Update Pricing Now" onclick="return confirm('This will fetch fresh pricing data from Namecheap API. Continue?');">
            </form>
        </div>
        
        <div class="card">
            <h2>Cron Job Status</h2>
            <?php
            $next_cron = wp_next_scheduled('update_domain_pricing_cron');
            if ($next_cron) {
                echo '<p><strong>Next automatic update:</strong> ' . date('Y-m-d H:i:s', $next_cron) . '</p>';
            } else {
                echo '<p><strong>Status:</strong> <span style="color: red;">Cron job not scheduled!</span></p>';
                echo '<p>The automatic pricing updates are not working. Please check your WordPress Cron configuration.</p>';
            }
            ?>
        </div>
        
        <div class="card">
            <h2>Current Pricing Data (Sample)</h2>
            <p>Showing first 20 TLDs from database:</p>
            <table class="widefat striped">
                <thead>
                    <tr>
                        <th>TLD</th>
                        <th>Price</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $count = 0;
                    foreach ($pricing_data as $tld => $price) {
                        if ($count >= 20) break;
                        echo '<tr><td>.' . esc_html($tld) . '</td><td>' . esc_html($price) . '</td></tr>';
                        $count++;
                    }
                    
                    if (empty($pricing_data)) {
                        echo '<tr><td colspan="2">No pricing data available. Click "Update Pricing Now" to fetch data.</td></tr>';
                    }
                    ?>
                </tbody>
            </table>
            
            <?php if (count($pricing_data) > 20): ?>
                <p><em>... and <?php echo count($pricing_data) - 20; ?> more TLDs</em></p>
            <?php endif; ?>
        </div>
        
        <div class="card">
            <h2>System Information</h2>
            <table class="widefat">
                <tr>
                    <td><strong>Database Table:</strong></td>
                    <td><?php global $wpdb; echo $wpdb->prefix . 'domain_pricing'; ?></td>
                </tr>
                <tr>
                    <td><strong>GraphQL Endpoint:</strong></td>
                    <td><?php echo home_url('/graphql'); ?></td>
                </tr>
                <tr>
                    <td><strong>Update Frequency:</strong></td>
                    <td>Every 6 hours</td>
                </tr>
                <tr>
                    <td><strong>WordPress Cron Status:</strong></td>
                    <td><?php echo defined('DISABLE_WP_CRON') && DISABLE_WP_CRON ? '<span style="color: red;">Disabled</span>' : '<span style="color: green;">Enabled</span>'; ?></td>
                </tr>
            </table>
        </div>

        <div class="card">
            <h2>API Information</h2>
            <p>REST API endpoints are available for server-to-server integration:</p>

            <table class="widefat">
                <tr>
                    <td><strong>Base URL:</strong></td>
                    <td><code><?php echo home_url('/wp-json/domain-pricing/v1/'); ?></code></td>
                </tr>
                <tr>
                    <td><strong>Authentication:</strong></td>
                    <td>Uses system IPT_API_KEY constant</td>
                </tr>
                <tr>
                    <td><strong>Available Endpoints:</strong></td>
                    <td>
                        <ul style="margin: 5px 0;">
                            <li><code>/import-from-file</code> - Import pricing from file</li>
                            <li><code>/update-from-api</code> - Update from Namecheap API</li>
                            <li><code>/stats</code> - Get pricing statistics</li>
                            <li><code>/pricing-data</code> - Get all pricing data</li>
                            <li><code>/production-setup</code> - Complete setup</li>
                        </ul>
                    </td>
                </tr>
            </table>

            <div style="margin-top: 15px; padding: 10px; background: #f0f8ff; border-left: 4px solid #0073aa;">
                <h4>Authentication Methods:</h4>
                <ul>
                    <li><strong>API Key Header:</strong> <code>X-API-Key: [IPT_API_KEY]</code></li>
                    <li><strong>Bearer Token:</strong> <code>Authorization: Bearer [IPT_API_KEY]</code></li>
                    <li><strong>Secret Parameter:</strong> <code>?secret=[IPT_API_KEY]</code></li>
                </ul>
                <p><em>Uses the same IPT_API_KEY constant defined in your system for consistency.</em></p>
            </div>
        </div>
        
        <div class="card">
            <h2>Troubleshooting</h2>
            <h3>If pricing is not updating automatically:</h3>
            <ol>
                <li>Check that WordPress Cron is enabled (not disabled via DISABLE_WP_CRON)</li>
                <li>Verify that your GraphQL endpoint is accessible</li>
                <li>Check the WordPress error logs for any API errors</li>
                <li>Use the "Update Pricing Now" button to test manual updates</li>
            </ol>
            
            <h3>JavaScript Console Commands:</h3>
            <ul>
                <li><code>viewCurrentPricing()</code> - View current pricing data loaded on domain search page</li>
                <li><code>updatePricing()</code> - Manually trigger pricing update (admin only)</li>
            </ul>
        </div>

        <div class="card">
            <h2>REST API Methods</h2>
            <p>Use these REST API endpoints for programmatic access:</p>

            <div style="margin: 20px 0;">
                <h3>Import from File</h3>
                <p>Import pricing data from the namecheap_price.md file:</p>
                <button type="button" class="button button-primary" onclick="callRestAPI('import-from-file', 'POST')">Import from File</button>
                <p><strong>Endpoint:</strong> <code><?php echo home_url('/wp-json/domain-pricing/v1/import-from-file'); ?></code></p>
            </div>

            <div style="margin: 20px 0;">
                <h3>Update from API</h3>
                <p>Fetch fresh pricing data from Namecheap API:</p>
                <button type="button" class="button button-secondary" onclick="callRestAPI('update-from-api', 'POST')">Update from API</button>
                <p><strong>Endpoint:</strong> <code><?php echo home_url('/wp-json/domain-pricing/v1/update-from-api'); ?></code></p>
            </div>

            <div style="margin: 20px 0;">
                <h3>Get Statistics</h3>
                <p>Get current pricing statistics:</p>
                <button type="button" class="button" onclick="callRestAPI('stats', 'GET')">Get Stats</button>
                <p><strong>Endpoint:</strong> <code><?php echo home_url('/wp-json/domain-pricing/v1/stats'); ?></code></p>
            </div>

            <div style="margin: 20px 0;">
                <h3>Production Setup</h3>
                <p>Complete production setup (database + import + cron):</p>
                <button type="button" class="button button-primary" onclick="callRestAPI('production-setup', 'POST')">Production Setup</button>
                <p><strong>Endpoint:</strong> <code><?php echo home_url('/wp-json/domain-pricing/v1/production-setup'); ?></code></p>
            </div>

            <div id="api-response" style="margin-top: 20px; padding: 15px; background: #f9f9f9; border-radius: 4px; display: none;">
                <h4>API Response:</h4>
                <pre id="api-response-content" style="white-space: pre-wrap; word-wrap: break-word;"></pre>
            </div>
        </div>
    </div>
    
    <style>
    .card {
        background: #fff;
        border: 1px solid #ccd0d4;
        border-radius: 4px;
        margin: 20px 0;
        padding: 20px;
    }
    .card h2 {
        margin-top: 0;
    }
    .widefat td, .widefat th {
        padding: 8px 10px;
    }
    #api-response {
        max-height: 400px;
        overflow-y: auto;
    }
    .loading {
        opacity: 0.6;
        pointer-events: none;
    }
    </style>

    <script>
    function callRestAPI(endpoint, method, params = {}) {
        const responseDiv = document.getElementById('api-response');
        const responseContent = document.getElementById('api-response-content');
        const button = event.target;

        // Show loading state
        button.classList.add('loading');
        button.disabled = true;
        button.textContent = 'Loading...';

        responseDiv.style.display = 'block';
        responseContent.textContent = 'Making API request...';

        const url = '<?php echo home_url('/wp-json/domain-pricing/v1/'); ?>' + endpoint;
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'X-WP-Nonce': '<?php echo wp_create_nonce('wp_rest'); ?>'
            }
        };

        if (method === 'POST' && Object.keys(params).length > 0) {
            options.body = JSON.stringify(params);
        }

        fetch(url, options)
            .then(response => response.json())
            .then(data => {
                responseContent.textContent = JSON.stringify(data, null, 2);

                if (data.success) {
                    responseDiv.style.background = '#d4edda';
                    responseDiv.style.border = '1px solid #c3e6cb';

                    // If this was a successful import/update, refresh the page after 2 seconds
                    if (endpoint === 'import-from-file' || endpoint === 'update-from-api' || endpoint === 'production-setup') {
                        setTimeout(() => {
                            location.reload();
                        }, 2000);
                    }
                } else {
                    responseDiv.style.background = '#f8d7da';
                    responseDiv.style.border = '1px solid #f5c6cb';
                }
            })
            .catch(error => {
                responseContent.textContent = 'Error: ' + error.message;
                responseDiv.style.background = '#f8d7da';
                responseDiv.style.border = '1px solid #f5c6cb';
            })
            .finally(() => {
                // Reset button state
                button.classList.remove('loading');
                button.disabled = false;

                // Reset button text based on endpoint
                const buttonTexts = {
                    'import-from-file': 'Import from File',
                    'update-from-api': 'Update from API',
                    'stats': 'Get Stats',
                    'production-setup': 'Production Setup'
                };
                button.textContent = buttonTexts[endpoint] || 'Action';
            });
    }


    </script>
    <?php
}

// Add admin notice if pricing data is old or missing
add_action('admin_notices', 'domain_pricing_admin_notices');

function domain_pricing_admin_notices() {
    if (get_current_screen()->id !== 'tools_page_domain-pricing') {
        return;
    }
    
    require_once get_stylesheet_directory() . '/includes/domain-pricing-system.php';
    $stats = get_domain_pricing_stats();
    
    if (!$stats || $stats['total_tlds'] == 0) {
        echo '<div class="notice notice-warning"><p><strong>No pricing data found!</strong> Click "Update Pricing Now" to fetch initial pricing data.</p></div>';
    } elseif ($stats['newest_update']) {
        $last_update = strtotime($stats['newest_update']);
        $hours_old = (time() - $last_update) / 3600;
        
        if ($hours_old > 12) {
            echo '<div class="notice notice-warning"><p><strong>Pricing data is ' . round($hours_old) . ' hours old.</strong> Consider updating pricing data.</p></div>';
        }
    }
}
