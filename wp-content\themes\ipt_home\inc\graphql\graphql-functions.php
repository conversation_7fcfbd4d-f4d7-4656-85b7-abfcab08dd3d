<?php

/**
 * <PERSON><PERSON><PERSON> request GraphQL và trả về dữ liệu
 *
 * @param string $query Câu truy vấn GraphQL
 * @param array $variables (<PERSON><PERSON><PERSON> chọn) Biến truyền vào query
 * @return array|WP_Error
 */
/**
 * <PERSON><PERSON><PERSON> request GraphQL và trả về dữ liệu
 *
 * @param string $query Câu truy vấn GraphQL
 * @param array $variables (Tù<PERSON> chọn) Biến truyền vào query
 * @return array|WP_Error
 */
function ipt_home_fetch_graphql_data($query, $variables = array()) {
    $start_time = microtime(true);
    $url = GRAPHQL_API_URL;

    $body = array(
        'query' => $query,
        'variables' => (object) $variables,
    );

    // Determine which token to use based on mutation type
    $headers = array(
        'Content-Type' => 'application/json',
    );

    // Check if this is a Webhooks_ mutation
    $is_webhooks_mutation = strpos($query, 'Webhooks_') !== false;

    if ($is_webhooks_mutation) {
        // Use IPT_API_KEY for Webhooks_ mutations
        if (defined('IPT_API_KEY') && IPT_API_KEY) {
            $headers['Authorization'] = 'Bearer ' . IPT_API_KEY;
            error_log('GraphQL: Using IPT_API_KEY for Webhooks_ mutation with token: ' . substr(IPT_API_KEY, 0, 10) . '...');
        } else {
            error_log('GraphQL: IPT_API_KEY not defined for Webhooks_ mutation');
        }
    } else {
        // Use GRAPHQL_TOKEN for regular mutations
        if (defined('GRAPHQL_TOKEN') && GRAPHQL_TOKEN) {
            $headers['Authorization'] = 'Bearer ' . GRAPHQL_TOKEN;
            error_log('GraphQL: Using GRAPHQL_TOKEN with token: ' . substr(GRAPHQL_TOKEN, 0, 10) . '...');
        } else {
            error_log('GraphQL: GRAPHQL_TOKEN not defined');
        }
    }

    $response = wp_remote_post($url, array(
        'headers' => $headers,
        'body' => json_encode($body),
        'timeout' => 15,
    ));

    $end_time = microtime(true);
    $execution_time = $end_time - $start_time;

    // Get response data
    $response_data = '';
    $status_code = '';

    if (is_wp_error($response)) {
        $response_data = $response->get_error_message();
        $status_code = 'ERROR';
        error_log('GraphQL Error: ' . $response_data);
    } else {
        $response_data = wp_remote_retrieve_body($response);
        $status_code = wp_remote_retrieve_response_code($response);

        // Log authentication errors
        if ($status_code == 401) {
            error_log('GraphQL 401 Unauthorized - Token may be invalid: ' . substr(GRAPHQL_TOKEN, 0, 10) . '...');
            error_log('GraphQL Response: ' . $response_data);
        }
    }

    // Log the GraphQL request and response with headers
    ipt_home_log_graphql_request($url, $body, $response_data, $status_code, $execution_time, $headers);

    if (is_wp_error($response)) {
        return $response;
    }

    $data = json_decode($response_data, true);

    return $data;
}

/**
 * Log GraphQL request and response to database
 */
function ipt_home_log_graphql_request($api_url, $request_data, $response_data, $status_code, $execution_time, $headers = array()) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'ipt_graphql_logs';

    // Get current user ID if logged in
    $user_id = is_user_logged_in() ? get_current_user_id() : null;

    // Prepare complete request data including headers
    $complete_request_data = array(
        'headers' => $headers,
        'body' => $request_data
    );

    // Insert log entry with headers included
    $wpdb->insert(
        $table_name,
        array(
            'api_url' => $api_url,
            'request_data' => json_encode($complete_request_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE),
            'response_data' => is_string($response_data) ? $response_data : json_encode($response_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE),
            'status_code' => $status_code,
            'execution_time' => $execution_time,
            'user_id' => $user_id,
            'created_at' => current_time('mysql')
        ),
        array(
            '%s', // api_url
            '%s', // request_data
            '%s', // response_data
            '%s', // status_code
            '%f', // execution_time
            '%d', // user_id
            '%s'  // created_at
        )
    );
}

add_action('wp_ajax_ipt_home_graphql', 'ipt_home_graphql_ajax_handler');
add_action('wp_ajax_nopriv_ipt_home_graphql', 'ipt_home_graphql_ajax_handler');

function ipt_home_graphql_ajax_handler() {
    $query = isset($_POST['query']) ? $_POST['query'] : '';
    $variables = isset($_POST['variables']) ? $_POST['variables'] : array();

    // Nếu là chuỗi rỗng hoặc không phải mảng/object thì gán lại là mảng rỗng
    if (empty($variables) || $variables === '' || $variables === 'null') {
        $variables = array();
    } elseif (is_string($variables)) {
        $variables = json_decode(stripslashes($variables), true);
        if (!is_array($variables)) {
            $variables = array();
        }
    }

    $result = ipt_home_fetch_graphql_data($query, $variables);

    wp_send_json($result);
}
