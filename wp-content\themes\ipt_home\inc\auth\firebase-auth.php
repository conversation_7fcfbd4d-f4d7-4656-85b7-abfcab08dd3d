<?php
/**
 * Firebase Authentication for Social Login
 */

// Add Firebase scripts and configuration
function ipt_home_firebase_auth() {
    // Only load Firebase on login page or pages that need social login
    if (!is_page_template('page-login.php') && !is_page_template('page-register.php')) {
        return;
    }
    ?>
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    <script>

        // Your web app's Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyAV8LpR9sQEjNDpB-USsxBlxi1OfglYOho",
            authDomain: "weaveform-dev.firebaseapp.com",
            projectId: "weaveform-dev",
            storageBucket: "weaveform-dev.firebasestorage.app",
            messagingSenderId: "951677862504",
            appId: "1:951677862504:web:37166d00887b05bc32b1dd",
            measurementId: "G-3HW8D9PGR5"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();

        // Google Provider
        const googleProvider = new firebase.auth.GoogleAuthProvider();
        googleProvider.addScope('email');
        googleProvider.addScope('profile');

        // Facebook Provider
        const facebookProvider = new firebase.auth.FacebookAuthProvider();
        facebookProvider.addScope('email');

        // Apple Provider
        const appleProvider = new firebase.auth.OAuthProvider('apple.com');
        appleProvider.addScope('email');
        appleProvider.addScope('name');

        // Make functions globally available
        window.firebaseAuth = {
            signInWithGoogle: (button = null) => signInWithGoogle(button),
            signInWithFacebook: (button = null) => signInWithFacebook(button),
            signInWithApple: (button = null) => signInWithApple(button),
            signOut: () => signOutUser()
        };

        // Debug: Log that Firebase is loaded

        // Google Sign In
        async function signInWithGoogle(button = null) {
            try {
                const result = await auth.signInWithPopup(googleProvider);
                const user = result.user;

                // Send user data to WordPress
                await sendUserDataToWordPress(user, 'google', button);

            } catch (error) {

                // Restore button state if button reference is provided
                if (button && button.originalText) {
                    button.disabled = false;
                    button.innerHTML = button.originalText;
                }
            }
        }

        // Facebook Sign In
        async function signInWithFacebook(button = null) {
            try {
                const result = await auth.signInWithPopup(facebookProvider);
                const user = result.user;

                // Send user data to WordPress
                await sendUserDataToWordPress(user, 'facebook', button);

            } catch (error) {

                // Restore button state if button reference is provided
                if (button && button.originalText) {
                    button.disabled = false;
                    button.innerHTML = button.originalText;
                }
            }
        }

        // Apple Sign In
        async function signInWithApple(button = null) {
            try {
                const result = await auth.signInWithPopup(appleProvider);
                const user = result.user;

                // Send user data to WordPress
                await sendUserDataToWordPress(user, 'apple', button);

            } catch (error) {

                // Restore button state if button reference is provided
                if (button && button.originalText) {
                    button.disabled = false;
                    button.innerHTML = button.originalText;
                }
            }
        }

        // Sign Out
        async function signOutUser() {
            try {
                await auth.signOut();

                // Redirect to home or login page
                window.location.href = '<?php echo home_url(); ?>';

            } catch (error) {
            }
        }

        // Send user data to WordPress backend
        async function sendUserDataToWordPress(user, provider, button = null) {
            const userData = {
                action: 'firebase_social_login',
                uid: user.uid,
                email: user.email,
                displayName: user.displayName,
                photoURL: user.photoURL,
                provider: provider,
                nonce: '<?php echo wp_create_nonce("firebase_social_login"); ?>'
            };

            try {
                const response = await fetch('<?php echo admin_url("admin-ajax.php"); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams(userData)
                });

                const result = await response.json();

                if (result.success) {
                    // Redirect to dashboard or intended page
                    window.location.href = result.data.redirect_url || '<?php echo home_url(); ?>';
                } else {

                    // Restore button state if button reference is provided
                    if (button && button.originalText) {
                        button.disabled = false;
                        button.innerHTML = button.originalText;
                    }
                }
            } catch (error) {

                // Restore button state if button reference is provided
                if (button && button.originalText) {
                    button.disabled = false;
                    button.innerHTML = button.originalText;
                }
            }
        }
    </script>
    <?php
}
add_action('wp_footer', 'ipt_home_firebase_auth');

/**
 * Handle Firebase Social Login AJAX request
 */
function handle_firebase_social_login() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'firebase_social_login')) {
        wp_die(json_encode([
            'success' => false,
            'data' => ['message' => 'Security check failed']
        ]));
    }

    $uid = sanitize_text_field($_POST['uid']);
    $email = sanitize_email($_POST['email']);
    $display_name = sanitize_text_field($_POST['displayName']);
    $photo_url = esc_url_raw($_POST['photoURL']);
    $provider = sanitize_text_field($_POST['provider']);

    // Check if user exists by email
    $user = get_user_by('email', $email);

    if (!$user) {
        // Create new user
        $username = sanitize_user($email);

        // Make sure username is unique
        $counter = 1;
        $original_username = $username;
        while (username_exists($username)) {
            $username = $original_username . $counter;
            $counter++;
        }

        $user_data = [
            'user_login' => $username,
            'user_email' => $email,
            'display_name' => $display_name,
            'first_name' => $display_name,
            'user_pass' => wp_generate_password(12, false),
            'role' => 'customer'
        ];

        $user_id = wp_insert_user($user_data);

        if (is_wp_error($user_id)) {
            wp_die(json_encode([
                'success' => false,
                'data' => ['message' => 'Failed to create user: ' . $user_id->get_error_message()]
            ]));
        }

        $user = get_user_by('id', $user_id);

        // Save Firebase UID and provider info
        update_user_meta($user_id, 'firebase_uid', $uid);
        update_user_meta($user_id, 'social_provider', $provider);
        update_user_meta($user_id, 'profile_photo_url', $photo_url);

        // Sync new user to GraphQL API (like register function does)
        $api_sync_result = sync_user_to_graphql_api($email, $display_name, $provider);
        if (!$api_sync_result['success']) {
            error_log('Firebase Social Login: Failed to sync user to GraphQL API: ' . $api_sync_result['message']);
            // Continue with login even if API sync fails
        }

        // Auto-assign trial to new social login user
        if (function_exists('ipt_home_auto_assign_trial_to_new_customer')) {
            ipt_home_auto_assign_trial_to_new_customer($user_id);
        }

    } else {
        // Update existing user's Firebase info
        update_user_meta($user->ID, 'firebase_uid', $uid);
        update_user_meta($user->ID, 'social_provider', $provider);
        update_user_meta($user->ID, 'profile_photo_url', $photo_url);
    }

    // Call GraphQL auth_login to get customer_id and store it (like regular login does)
    $customer_id = get_customer_id_from_graphql_login($email);
    if ($customer_id) {
        update_user_meta($user->ID, 'customer_id', $customer_id);
        error_log("Firebase Social Login: Saved customer_id {$customer_id} for user {$user->ID}");
    } else {
        error_log("Firebase Social Login: Failed to get customer_id for user {$email}");
    }

    // Log the user in
    wp_set_current_user($user->ID);
    wp_set_auth_cookie($user->ID, true);

    // Determine redirect URL
    $redirect_url = add_query_arg('customer_page', 'dashboard', home_url()); // Primary redirect URL

    // Always redirect to customer dashboard after successful login
    // If you want a fallback, uncomment the line below:
    // $redirect_url = home_url(); // Fallback to home page

    wp_die(json_encode([
        'success' => true,
        'data' => [
            'message' => 'Login successful',
            'user_id' => $user->ID,
            'redirect_url' => $redirect_url
        ]
    ]));
}

// Hook for both logged in and non-logged in users
add_action('wp_ajax_firebase_social_login', 'handle_firebase_social_login');
add_action('wp_ajax_nopriv_firebase_social_login', 'handle_firebase_social_login');

/**
 * Helper function to check if a page exists
 */
function page_exists($page_slug) {
    return get_page_by_path($page_slug) !== null;
}