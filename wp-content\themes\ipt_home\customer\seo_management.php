<?php include get_stylesheet_directory() . '/customer/header-customer.php'; ?>
<?php include get_stylesheet_directory() . '/customer/header-dashboard.php'; ?>

<div class="flex min-h-screen bg-neutral-light">
   <?php include get_stylesheet_directory() . '/customer/sidebar.php'; ?>
   <div class="flex-1 flex flex-col min-h-screen">

      <!-- SEO Management -->
      <main class="flex-1 p-4 bg-db-dark-2">
         <div class="bg-white rounded-lg shadow p-6">
            <!-- Header with navigation -->
            <div class="flex items-center mb-6">
               <div class="flex items-center text-sm text-gray-500">
                  <span>Settings</span>
                  <svg class="mx-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                     <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                  <span>SEO management</span>
               </div>
            </div>

            <!-- Page Title -->
            <h1 class="text-2xl font-semibold text-gray-900 mb-8">SEO management</h1>

            <!-- SEO Form -->
            <form id="seo-form" class="space-y-6">
               <!-- Meta Information Section -->
               <div>
                  <h2 class="text-lg font-medium text-gray-900 mb-6">Meta Information</h2>
                  
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                     <!-- Meta Title -->
                     <div>
                        <label for="meta_title" class="block text-sm font-medium text-gray-700 mb-2">
                           Meta Title
                        </label>
                        <input 
                           type="text" 
                           id="meta_title" 
                           name="meta_title" 
                           placeholder="Placeholder"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                     </div>

                     <!-- Meta Description -->
                     <div>
                        <label for="meta_description" class="block text-sm font-medium text-gray-700 mb-2">
                           Meta Description
                        </label>
                        <textarea 
                           id="meta_description" 
                           name="meta_description" 
                           rows="3"
                           placeholder="Placeholder"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                        ></textarea>
                     </div>
                  </div>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                     <!-- Keywords -->
                     <div class="mt-6">
                        <label for="keywords" class="block text-sm font-medium text-gray-700 mb-2">
                           Keywords
                        </label>
                        <input 
                           type="text" 
                           id="keywords" 
                           name="keywords" 
                           placeholder="Placeholder"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                     </div>
                  </div>
               </div>

               <!-- Action Buttons -->
               <div class="flex justify-end space-x-4 pt-6">
                  <button 
                     type="button" 
                     id="cancel-btn"
                     class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                  >
                     Cancel
                  </button>
                  <button 
                     type="submit" 
                     id="save-btn"
                     class="px-6 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors"
                  >
                     Save SEO Settings
                  </button>
               </div>
            </form>
         </div>
      </main>
   </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('seo-form');
    const cancelBtn = document.getElementById('cancel-btn');
    const saveBtn = document.getElementById('save-btn');

    // Handle form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(form);
        const seoData = {
            meta_title: formData.get('meta_title'),
            meta_description: formData.get('meta_description'),
            keywords: formData.get('keywords')
        };

        // Show loading state
        saveBtn.disabled = true;
        saveBtn.textContent = 'Saving...';

        // Here you can add AJAX call to save the data
        // For now, we'll just simulate a save
        setTimeout(() => {
            
            // Show success message
            showNotification('SEO settings saved successfully!', 'success');
            
            // Reset button state
            saveBtn.disabled = false;
            saveBtn.textContent = 'Save SEO Settings';
        }, 1000);
    });

    // Handle cancel button
    cancelBtn.addEventListener('click', function() {
        if (confirm('Are you sure you want to cancel? Any unsaved changes will be lost.')) {
            form.reset();
        }
    });

    // Function to show notifications
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 px-6 py-3 rounded-md text-white z-50 ${
            type === 'success' ? 'bg-green-500' : 
            type === 'error' ? 'bg-red-500' : 'bg-blue-500'
        }`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    // Load existing SEO data (if any)
    loadSEOData();

    function loadSEOData() {
        // Here you can add AJAX call to load existing SEO data
        // For now, we'll just log that we're loading
    }
});
</script>

<?php include get_stylesheet_directory() . '/customer/footer-customer.php'; ?>
