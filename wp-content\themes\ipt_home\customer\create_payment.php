<?php
// Thêm code debug ở đầu file
if (isset($_GET['debug']) && $_GET['debug'] === '1') {
    // Kiểm tra Stripe settings
    $stripe_settings = get_option('woocommerce_stripe_settings');
    echo '<pre style="background: #f5f5f5; padding: 10px; margin: 10px 0; border: 1px solid #ddd;">';
    echo 'Stripe Enabled: ' . (isset($stripe_settings['enabled']) && $stripe_settings['enabled'] === 'yes' ? 'Yes' : 'No') . "\n";
    echo 'Test Mode: ' . (isset($stripe_settings['testmode']) && $stripe_settings['testmode'] === 'yes' ? 'Yes' : 'No') . "\n";
    
    $test_mode = isset($stripe_settings['testmode']) && $stripe_settings['testmode'] === 'yes';
    $publishable_key = $test_mode ? 
        (isset($stripe_settings['test_publishable_key']) ? substr($stripe_settings['test_publishable_key'], 0, 10) . '...' : 'Not set') : 
        (isset($stripe_settings['publishable_key']) ? substr($stripe_settings['publishable_key'], 0, 10) . '...' : 'Not set');
    echo 'Publishable Key: ' . $publishable_key . "\n";
    
    $secret_key = $test_mode ? 
        (isset($stripe_settings['test_secret_key']) && !empty($stripe_settings['test_secret_key']) ? 'Set' : 'Not set') : 
        (isset($stripe_settings['secret_key']) && !empty($stripe_settings['secret_key']) ? 'Set' : 'Not set');
    echo 'Secret Key: ' . $secret_key . "\n";
    
    // Kiểm tra WooCommerce
    echo 'WooCommerce Active: ' . (class_exists('WooCommerce') ? 'Yes' : 'No') . "\n";
    echo 'Cart Total: ' . (function_exists('WC') && WC()->cart ? WC()->cart->get_total() : 'N/A') . "\n";
    echo 'Currency: ' . (function_exists('get_woocommerce_currency') ? get_woocommerce_currency() : 'N/A') . "\n";
    
    // Kiểm tra AJAX URL
    echo 'AJAX URL: ' . admin_url('admin-ajax.php') . "\n";
    echo 'Site URL: ' . site_url() . "\n";
    echo 'Home URL: ' . home_url() . "\n";
    
    // Kiểm tra nonce
    echo 'Nonce: ' . wp_create_nonce('ipt-payment-nonce') . "\n";
    
    echo '</pre>';
}

// Debug URL parameters
error_log('Create payment URL parameters: ' . print_r($_GET, true));

// Kiểm tra nếu có thanh toán thành công
if (isset($_GET['payment_intent']) && isset($_GET['redirect_status']) && $_GET['redirect_status'] === 'succeeded') {
    // Lấy payment intent ID
    $payment_intent_id = isset($_GET['payment_intent']) ? sanitize_text_field($_GET['payment_intent']) : '';
    
    if (!empty($payment_intent_id)) {
        error_log('Processing payment success for payment intent: ' . $payment_intent_id);

        // Tạo đơn hàng WooCommerce từ giỏ hàng
        if (!WC()->cart->is_empty()) {
            error_log('Cart has items, creating order...');

            // Tạo đơn hàng
            $order = wc_create_order();
            
            // Thêm sản phẩm từ giỏ hàng vào đơn hàng
            foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
                $product_id = $cart_item['product_id'];
                $quantity = $cart_item['quantity'];
                $variation_id = isset($cart_item['variation_id']) ? $cart_item['variation_id'] : 0;
                
                $order->add_product(wc_get_product($product_id), $quantity, [
                    'variation_id' => $variation_id,
                ]);
            }
            
            // Cập nhật thông tin khách hàng
            $current_user = wp_get_current_user();
            if ($current_user->ID) {
                $order->set_customer_id($current_user->ID);
                $order->set_billing_email($current_user->user_email);
                $order->set_billing_first_name($current_user->first_name);
                $order->set_billing_last_name($current_user->last_name);
                
                // Thêm thông tin thanh toán khác nếu có
                $billing_phone = get_user_meta($current_user->ID, 'billing_phone', true);
                if (!empty($billing_phone)) {
                    $order->set_billing_phone($billing_phone);
                }
                
                $billing_address_1 = get_user_meta($current_user->ID, 'billing_address_1', true);
                if (!empty($billing_address_1)) {
                    $order->set_billing_address_1($billing_address_1);
                }
            }
            
            // Cập nhật phương thức thanh toán
            $order->set_payment_method('stripe');
            $order->set_payment_method_title('Credit Card (Stripe)');
            
            // Lưu payment intent ID vào đơn hàng
            $order->update_meta_data('_stripe_payment_intent', $payment_intent_id);
            
            // Cập nhật tổng đơn hàng
            $order->calculate_totals();
            
            // Đánh dấu đơn hàng đã thanh toán
            $order->payment_complete($payment_intent_id);
            
            // Thêm ghi chú
            $order->add_order_note('Payment completed via Stripe. Payment Intent ID: ' . $payment_intent_id);
            
            // Lưu đơn hàng
            $order->save();

            error_log('Order created successfully: #' . $order->get_id() . ' with total: ' . $order->get_total());

            // Xóa giỏ hàng
            WC()->cart->empty_cart();

            // Lưu ID đơn hàng vào session để hiển thị thông báo
            WC()->session->set('recent_order_id', $order->get_id());

            // Chuyển hướng đến trang subscription thay vì dashboard (using user-friendly URL)
            wp_safe_redirect(site_url('/customer/subscription/'));
            exit;
        } else {
            error_log('Cart is empty, cannot create order for payment intent: ' . $payment_intent_id);
        }
    } else {
        error_log('Payment intent ID is empty');
    }
}
?>
<?php include get_stylesheet_directory() . '/customer/header-customer.php'; ?>
<?php include get_stylesheet_directory() . '/customer/header-dashboard.php'; ?>
<?php
// Đặt ở đầu file, trước khi có bất kỳ output nào
// Kiểm tra giỏ hàng có sản phẩm không
if (WC()->cart->is_empty()) {
    // Sử dụng wp_safe_redirect thay vì wp_redirect
    wp_safe_redirect(home_url().'?page_id=33');
    exit; // Luôn luôn cần exit sau redirect
}

// Lấy thông tin người dùng hiện tại nếu đã đăng nhập
$current_user = wp_get_current_user();
$user_name = $current_user->ID ? $current_user->display_name : '';
$user_email = $current_user->ID ? $current_user->user_email : '';
$user_phone = get_user_meta($current_user->ID, 'billing_phone', true);
$user_address = get_user_meta($current_user->ID, 'billing_address_1', true);

// Lấy thông tin đơn hàng
$cart_total = WC()->cart->get_total();
$amount = (int) (WC()->cart->get_total('edit') * 100); // Convert to cents for Stripe

// Debug Stripe settings
$stripe_settings = get_option('woocommerce_stripe_settings');
error_log('Stripe Settings: ' . print_r($stripe_settings, true));

// Kiểm tra chế độ test
$test_mode = isset($stripe_settings['testmode']) && 'yes' === $stripe_settings['testmode'];
error_log('Test Mode: ' . ($test_mode ? 'Yes' : 'No'));

// Kiểm tra publishable key
$publishable_key = $test_mode ? 
    (isset($stripe_settings['test_publishable_key']) ? $stripe_settings['test_publishable_key'] : 'Not set') : 
    (isset($stripe_settings['publishable_key']) ? $stripe_settings['publishable_key'] : 'Not set');
error_log('Publishable Key: ' . $publishable_key);

// Kiểm tra secret key (chỉ log có hay không, không log giá trị thực)
$secret_key = $test_mode ? 
    (isset($stripe_settings['test_secret_key']) && !empty($stripe_settings['test_secret_key']) ? 'Set' : 'Not set') : 
    (isset($stripe_settings['secret_key']) && !empty($stripe_settings['secret_key']) ? 'Set' : 'Not set');
error_log('Secret Key: ' . $secret_key);

// Kiểm tra webhook
$webhook_secret = $test_mode ? 
    (isset($stripe_settings['test_webhook_secret']) ? 'Set' : 'Not set') : 
    (isset($stripe_settings['webhook_secret']) ? 'Set' : 'Not set');
error_log('Webhook Secret: ' . $webhook_secret);

// Kiểm tra trạng thái kích hoạt
$enabled = isset($stripe_settings['enabled']) && 'yes' === $stripe_settings['enabled'];
error_log('Stripe Enabled: ' . ($enabled ? 'Yes' : 'No'));
?>

<div class="flex min-h-screen bg-neutral-light">
   <?php include get_stylesheet_directory() . '/customer/sidebar.php'; ?>
   <div class="flex-1 flex flex-col min-h-screen">
      <?php include get_stylesheet_directory() . '/customer/breadcrumb-create-payment.php'; ?>

      <!-- Website -->
      <main class="flex-1 p-4 bg-db-dark-2">
         <div class="bg-white rounded-lg shadow p-4">
            <div class="grid grid-cols-1 md:grid-cols-12 gap-6">
               <!-- Cột trái: Thông tin thanh toán -->
               <div class="md:col-span-6 col-span-12 relative overflow-hidden min-h-[220px] flex flex-col justify-start">
                  <div class="font-semibold text-lg mb-4">Payment Information</div>
                  <div class="payment-container">
                      <!-- Thông báo lỗi -->
                      <div id="error-message" class="alert alert-danger" style="display: none;"></div>
                      
                      <!-- Payment Method Selection -->
                      <div class="mb-6">
                          <h3 class="text-lg font-semibold mb-4">Choose Payment Method</h3>
                          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <!-- Card Payment Option -->
                              <div class="payment-method-option border-2 border-gray-200 rounded-lg p-4 cursor-pointer hover:border-blue-500 transition-colors" data-method="card">
                                  <div class="flex items-center mb-2">
                                      <input type="radio" id="payment-card" name="payment_method" value="card" class="mr-3" checked>
                                      <label for="payment-card" class="font-medium cursor-pointer">Credit/Debit Card</label>
                                  </div>
                                  <div class="flex items-center space-x-2 text-sm text-gray-600">
                                      <span>💳</span>
                                      <span>Visa, Mastercard, AMEX</span>
                                  </div>
                              </div>

                              <!-- PayNow Option -->
                              <div class="payment-method-option border-2 border-gray-200 rounded-lg p-4 cursor-pointer hover:border-blue-500 transition-colors" data-method="paynow">
                                  <div class="flex items-center mb-2">
                                      <input type="radio" id="payment-paynow" name="payment_method" value="paynow" class="mr-3">
                                      <label for="payment-paynow" class="font-medium cursor-pointer">PayNow QR</label>
                                  </div>
                                  <div class="flex items-center space-x-2 text-sm text-gray-600">
                                      <span>📱</span>
                                      <span>Scan QR with banking app</span>
                                  </div>
                              </div>
                          </div>
                      </div>

                      <!-- Card Payment Form -->
                      <div id="card-payment-form" class="payment-form">
                          <!-- Loading indicator -->
                          <div id="payment-loading" class="text-center p-4">
                              <div class="spinner-border text-primary" role="status">
                                  <span class="visually-hidden">Loading...</span>
                              </div>
                              <p class="mt-2">Initializing payment...</p>
                          </div>
                      </div>

                      <!-- PayNow Payment Form -->
                      <div id="paynow-payment-form" class="payment-form hidden">
                          <div class="text-center p-6">
                              <h4 class="text-lg font-semibold mb-4">PayNow QR Payment</h4>
                              <div id="paynow-qr-container" class="mb-4">
                                  <!-- QR code will be displayed here -->
                              </div>
                              <p class="text-sm text-gray-600 mb-4">
                                  Scan the QR code with your banking app to complete the payment
                              </p>
                              <div id="paynow-status" class="text-sm">
                                  <span class="text-blue-600">⏳ Waiting for payment...</span>
                              </div>
                          </div>
                      </div>
                      
                      <!-- Form thanh toán -->
                      <form id="payment-form" style="display: none;">
                          <div class="mb-3">
                              <label for="company_name" class="form-label">Company Name</label>
                              <input type="text" id="company_name" class="form-control" required>
                          </div>
                          
                          <div class="mb-3">
                              <label for="email" class="form-label">Email</label>
                              <input type="email" id="email" class="form-control" required>
                          </div>
                          
                          <div class="mb-3">
                              <label for="phone" class="form-label">Phone</label>
                              <input type="tel" id="phone" class="form-control" required>
                          </div>
                          
                          <div class="mb-3">
                              <label for="address" class="form-label">Address</label>
                              <input type="text" id="address" class="form-control" required>
                          </div>
                          
                          <div class="mb-3">
                              <label for="payment-element" class="form-label">Payment Information</label>
                              <div id="payment-element"></div>
                          </div>
                          
                          <button id="submit-button" type="submit" class="btn btn-primary" disabled>Pay Now</button>
                      </form>
                  </div>
               </div>

               <!-- Cột phải: Thông tin đơn hàng -->
               <div class="md:col-span-6 col-span-12 relative overflow-hidden min-h-[220px] flex flex-col justify-start">
                  <div class="font-semibold text-lg mb-4">Order Summary</div>
                  <div class="bg-ipt-bg-1 p-4 rounded-lg">
                     <div class="space-y-4">
                        <?php foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) : 
                           $product = $cart_item['data'];
                           $product_name = $product->get_name();
                           $product_price = WC()->cart->get_product_price($product);
                           $product_quantity = $cart_item['quantity'];
                        ?>
                        <div class="flex justify-between items-center border-b border-gray-200 pb-3">
                           <div>
                              <div class="font-medium"><?php echo esc_html($product_name); ?></div>
                              <div class="text-sm text-gray-500">Quantity: <?php echo esc_html($product_quantity); ?></div>
                           </div>
                           <div class="font-medium"><?php echo $product_price; ?></div>
                        </div>
                        <?php endforeach; ?>
                        
                        <div class="flex justify-between items-center pt-2 font-semibold">
                           <div>Total</div>
                           <div><?php echo WC()->cart->get_total(); ?></div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </main>
   </div>
</div>

<!-- Thêm Stripe JS -->
<style>
.payment-method-option {
    transition: all 0.3s ease;
}

.payment-method-option:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.payment-method-option.selected {
    border-color: #3b82f6 !important;
    background-color: #eff6ff !important;
}

#paynow-qr-container img {
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    background: white;
}

.payment-form {
    transition: opacity 0.3s ease;
}

.hidden {
    display: none !important;
}
</style>

<script src="https://js.stripe.com/v3/"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Khởi tạo Stripe
    try {
        <?php
            $stripe_settings = get_option('woocommerce_stripe_settings');
            $test_mode = isset($stripe_settings['testmode']) && 'yes' === $stripe_settings['testmode'];
            $stripe_key = $test_mode ?
                (isset($stripe_settings['test_publishable_key']) ? $stripe_settings['test_publishable_key'] : '') :
                (isset($stripe_settings['publishable_key']) ? $stripe_settings['publishable_key'] : '');
        ?>

        const stripeKey = '<?php echo esc_js($stripe_key); ?>';

        if (!stripeKey || stripeKey.length === 0) {
            throw new Error('Stripe publishable key is not configured');
        }

        const stripe = Stripe(stripeKey);
        const elements = stripe.elements();

        // Payment method selection handling
        const paymentMethodOptions = document.querySelectorAll('.payment-method-option');
        const cardForm = document.getElementById('card-payment-form');
        const paynowForm = document.getElementById('paynow-payment-form');

        // Handle payment method selection
        paymentMethodOptions.forEach(option => {
            option.addEventListener('click', function() {
                const method = this.dataset.method;
                const radio = this.querySelector('input[type="radio"]');

                // Update radio selection
                radio.checked = true;

                // Update visual selection
                paymentMethodOptions.forEach(opt => {
                    opt.classList.remove('border-blue-500', 'bg-blue-50');
                    opt.classList.add('border-gray-200');
                });
                this.classList.remove('border-gray-200');
                this.classList.add('border-blue-500', 'bg-blue-50');

                // Show/hide appropriate form
                if (method === 'card') {
                    cardForm.classList.remove('hidden');
                    paynowForm.classList.add('hidden');
                    initCardPayment();
                } else if (method === 'paynow') {
                    cardForm.classList.add('hidden');
                    paynowForm.classList.remove('hidden');
                    initPayNowPayment();
                }
            });
        });

        // Initialize with card payment by default
        initCardPayment();

        function initCardPayment() {
            // Tạo payment intent for card
            initPayment();
        }

        async function initPayNowPayment() {
            try {
                document.getElementById('paynow-status').innerHTML = '<span class="text-blue-600">⏳ Creating PayNow payment...</span>';

                const response = await fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'create_paynow_payment',
                        amount: <?php echo $amount; ?>,
                        currency: 'SGD',
                        nonce: '<?php echo wp_create_nonce('paynow_payment_nonce'); ?>'
                    })
                });

                const responseText = await response.text();

                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    throw new Error('Invalid JSON response from server');
                }

                if (data.success && data.qr_code) {
                    // Display QR code
                    document.getElementById('paynow-qr-container').innerHTML =
                        `<img src="${data.qr_code}" alt="PayNow QR Code" class="mx-auto max-w-xs">`;
                    document.getElementById('paynow-status').innerHTML =
                        '<span class="text-blue-600">⏳ Waiting for payment...</span>';

                    // Start polling for payment status
                    pollPaymentStatus(data.payment_intent_id);
                } else {
                    document.getElementById('paynow-status').innerHTML =
                        '<span class="text-red-600">❌ Error creating PayNow payment</span>';
                }
            } catch (error) {
                document.getElementById('paynow-status').innerHTML =
                    '<span class="text-red-600">❌ Error creating PayNow payment</span>';
            }
        }

        function pollPaymentStatus(paymentIntentId) {
            const pollInterval = setInterval(async () => {
                try {
                    const response = await fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: new URLSearchParams({
                            action: 'check_payment_status',
                            payment_intent_id: paymentIntentId,
                            nonce: '<?php echo wp_create_nonce('check_payment_nonce'); ?>'
                        })
                    });

                    const responseText = await response.text();
                    let data;
                    try {
                        data = JSON.parse(responseText);
                    } catch (parseError) {
                        return; // Skip this iteration
                    }

                    if (data.status === 'succeeded') {
                        clearInterval(pollInterval);
                        document.getElementById('paynow-status').innerHTML =
                            '<span class="text-green-600">✅ Payment successful!</span>';
                        // Redirect to success page
                        window.location.href = '<?php echo home_url('?customer_page=subscription'); ?>';
                    } else if (data.status === 'failed') {
                        clearInterval(pollInterval);
                        document.getElementById('paynow-status').innerHTML =
                            '<span class="text-red-600">❌ Payment failed</span>';
                    }
                } catch (error) {
                }
            }, 3000); // Check every 3 seconds

            // Stop polling after 10 minutes
            setTimeout(() => {
                clearInterval(pollInterval);
                document.getElementById('paynow-status').innerHTML =
                    '<span class="text-yellow-600">⏰ Payment timeout. Please try again.</span>';
            }, 600000);
        }
        
        async function initPayment() {
            try {
                // Hiển thị loading, ẩn form và thông báo lỗi
                document.getElementById('payment-loading').style.display = 'block';
                document.getElementById('payment-form').style.display = 'none';
                document.getElementById('error-message').style.display = 'none';
                
                
                // Gọi AJAX để tạo payment intent
                const response = await fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        'action': 'ipt_create_payment_intent',
                        'security': '<?php echo wp_create_nonce('ipt-payment-nonce'); ?>',
                    }),
                });

                const responseText = await response.text();

                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    throw new Error('Invalid JSON response from server');
                }
                
                // Ẩn loading
                document.getElementById('payment-loading').style.display = 'none';
                
                if (!data.success || !data.data || !data.data.client_secret) {
                    const errorMessage = data.data && data.data.message 
                        ? data.data.message 
                        : 'Could not create payment. Please try again.';
                    showError(errorMessage);
                    return;
                }
                
                const clientSecret = data.data.client_secret;
                const isTrial = data.data.is_trial || false;
                const trialDays = data.data.trial_days || 0;
                const setupIntentId = data.data.setup_intent_id || null;


                // Store trial info globally for use in payment confirmation
                window.paymentInfo = {
                    isTrial: isTrial,
                    trialDays: trialDays,
                    setupIntentId: setupIntentId,
                    clientSecret: clientSecret
                };

                // Khởi tạo Stripe Elements với clientSecret
                const elements = stripe.elements({
                    clientSecret: clientSecret,
                    appearance: {
                        theme: 'stripe',
                        variables: {
                            colorPrimary: '#48C9B0',
                        },
                    },
                });
                
                // Tạo và mount payment element
                const paymentElement = elements.create('payment');
                
                // Hiển thị form trước khi mount element
                document.getElementById('payment-form').style.display = 'block';
                
                // Mount payment element
                paymentElement.mount('#payment-element');
                
                // Bật nút submit
                document.getElementById('submit-button').disabled = false;
                
                // Xử lý button click
                const submitButton = document.getElementById('submit-button');
                const form = document.getElementById('payment-form');
                
                form.addEventListener('submit', async function(event) {
                    event.preventDefault();
                    
                    // Disable the submit button to prevent repeated clicks
                    submitButton.disabled = true;
                    submitButton.textContent = 'Processing...';
                    
                    // Lấy thông tin từ form
                    const companyName = document.getElementById('company_name').value;
                    const email = document.getElementById('email').value;
                    const phone = document.getElementById('phone').value;
                    const address = document.getElementById('address').value;
                    
                    // Validate form
                    if (!companyName || !email || !phone || !address) {
                        showError('Please fill in all required fields.');
                        submitButton.disabled = false;
                        submitButton.textContent = 'Pay Now';
                        return;
                    }
                    
                    try {
                        // Check if this is a trial subscription
                        if (window.paymentInfo && window.paymentInfo.isTrial) {

                            // For trial subscriptions, use confirmSetup instead of confirmPayment
                            const {error, setupIntent} = await stripe.confirmSetup({
                                elements,
                                confirmParams: {
                                    return_url: '<?php echo esc_js(add_query_arg('customer_page', 'subscription', home_url())); ?>',
                                    payment_method_data: {
                                        billing_details: {
                                            name: companyName,
                                            email: email,
                                            phone: phone,
                                            address: {
                                                line1: address
                                            }
                                        }
                                    }
                                },
                                redirect: 'if_required'
                            });

                            if (error) {
                                showError(error.message);
                                submitButton.disabled = false;
                                submitButton.textContent = 'Pay Now';
                                return;
                            }

                            // If setup successful, confirm trial setup via AJAX
                            await confirmTrialSetup(window.paymentInfo.setupIntentId);

                        } else {

                            // For regular payments, use confirmPayment
                            const {error} = await stripe.confirmPayment({
                                elements,
                                confirmParams: {
                                    return_url: '<?php echo esc_js(home_url('/customer/subscription/')); ?>',
                                    payment_method_data: {
                                        billing_details: {
                                            name: companyName,
                                            email: email,
                                            phone: phone,
                                            address: {
                                                line1: address
                                            }
                                        }
                                    }
                                }
                            });

                            if (error) {
                                showError(error.message);
                                submitButton.disabled = false;
                                submitButton.textContent = 'Pay Now';
                            }
                            // If no error, user will be redirected to return_url
                        }

                    } catch (error) {
                        showError('An unexpected error occurred. Please try again.');
                        submitButton.disabled = false;
                        submitButton.textContent = 'Pay Now';
                    }
                });
            } catch (error) {
                document.getElementById('payment-loading').style.display = 'none';
                showError('Could not initialize payment. Please try again. Error: ' + error.message);
            }
        }
        
        // Hiển thị lỗi
        function showError(message) {
            const errorElement = document.getElementById('error-message');
            errorElement.textContent = message;
            errorElement.style.display = 'block';

            // Scroll to error
            errorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }

        // Function to confirm trial setup and create trial order
        async function confirmTrialSetup(setupIntentId) {
            try {

                const response = await fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        'action': 'ipt_confirm_trial_setup',
                        'security': '<?php echo wp_create_nonce('ipt-payment-nonce'); ?>',
                        'setup_intent_id': setupIntentId
                    }),
                });

                const data = await response.json();

                if (data.success) {
                    // Show success message
                    showSuccess(`Trial started! You have ${data.data.trial_days} days free trial. You will be charged after the trial period ends.`);

                    // Redirect to subscription page after a short delay
                    setTimeout(() => {
                        window.location.href = data.data.redirect_url;
                    }, 3000);
                } else {
                    const errorMessage = data.data && data.data.message
                        ? data.data.message
                        : 'Could not confirm trial setup. Please try again.';
                    showError(errorMessage);

                    // Re-enable submit button
                    const submitButton = document.getElementById('submit-button');
                    submitButton.disabled = false;
                    submitButton.textContent = 'Pay Now';
                }

            } catch (error) {
                showError('An unexpected error occurred while setting up trial. Please try again.');

                // Re-enable submit button
                const submitButton = document.getElementById('submit-button');
                submitButton.disabled = false;
                submitButton.textContent = 'Pay Now';
            }
        }

        // Function to show success message
        function showSuccess(message) {
            let successDiv = document.getElementById('success-message');
            if (!successDiv) {
                // Create success message div if it doesn't exist
                successDiv = document.createElement('div');
                successDiv.id = 'success-message';
                successDiv.className = 'alert alert-success bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4';
                successDiv.style.display = 'none';

                // Insert before payment container
                const paymentContainer = document.querySelector('.payment-container');
                if (paymentContainer) {
                    paymentContainer.insertBefore(successDiv, paymentContainer.firstChild);
                }
            }

            successDiv.textContent = message;
            successDiv.style.display = 'block';

            // Hide error message if visible
            const errorElement = document.getElementById('error-message');
            if (errorElement) {
                errorElement.style.display = 'none';
            }

            // Scroll to success message
            successDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }

        // Transaction creation is now handled automatically by WooCommerce hooks
        // No need for manual JavaScript transaction creation

    } catch (error) {
        document.getElementById('error-message').textContent = 'Payment configuration error: ' + error.message;
        document.getElementById('error-message').style.display = 'block';
        document.getElementById('payment-loading').style.display = 'none';
    }
});

function toggleMenu(id) {
  const menu = document.getElementById(id);
  menu.classList.toggle('hidden');
}
</script>

<?php include get_stylesheet_directory() . '/customer/footer-customer.php'; ?>
