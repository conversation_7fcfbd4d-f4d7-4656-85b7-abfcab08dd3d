<?php
/**
 * Quick Test for Domain Pricing System
 * 
 * Access via: yoursite.com/wp-content/themes/ipt_home/test-pricing-system.php
 */

// Load WordPress
require_once('../../../../wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

echo "<h1>Domain Pricing System Test</h1>";

// Test 1: Check if files exist
echo "<h2>1. File System Check</h2>";
$files_to_check = [
    'includes/domain-pricing-system.php',
    'admin/domain-pricing-admin.php',
    'update-pricing-manual.php'
];

foreach ($files_to_check as $file) {
    $full_path = get_stylesheet_directory() . '/' . $file;
    if (file_exists($full_path)) {
        echo "✅ $file - EXISTS<br>";
    } else {
        echo "❌ $file - MISSING<br>";
    }
}

// Test 2: Load pricing system
echo "<h2>2. Loading Pricing System</h2>";
try {
    require_once get_stylesheet_directory() . '/includes/domain-pricing-system.php';
    echo "✅ Pricing system loaded successfully<br>";
} catch (Exception $e) {
    echo "❌ Error loading pricing system: " . $e->getMessage() . "<br>";
}

// Test 3: Check database table
echo "<h2>3. Database Table Check</h2>";
global $wpdb;
$table_name = $wpdb->prefix . 'domain_pricing';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

if ($table_exists) {
    echo "✅ Database table '$table_name' exists<br>";
    
    // Check table structure
    $columns = $wpdb->get_results("DESCRIBE $table_name");
    echo "Table columns: ";
    foreach ($columns as $column) {
        echo $column->Field . " (" . $column->Type . "), ";
    }
    echo "<br>";
    
    // Check row count
    $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
    echo "Current rows in table: $count<br>";
    
} else {
    echo "❌ Database table '$table_name' does not exist<br>";
    echo "Creating table...<br>";
    create_domain_pricing_table();
    echo "✅ Table created<br>";
}

// Test 4: Check functions
echo "<h2>4. Function Availability Check</h2>";
$functions_to_check = [
    'create_domain_pricing_table',
    'fetch_pricing_from_namecheap_api',
    'update_domain_pricing_in_db',
    'get_domain_pricing_from_db',
    'get_domain_pricing_stats'
];

foreach ($functions_to_check as $function) {
    if (function_exists($function)) {
        echo "✅ $function() - AVAILABLE<br>";
    } else {
        echo "❌ $function() - MISSING<br>";
    }
}

// Test 5: Check current pricing data
echo "<h2>5. Current Pricing Data</h2>";
if (function_exists('get_domain_pricing_from_db')) {
    $pricing = get_domain_pricing_from_db();
    $stats = get_domain_pricing_stats();
    
    echo "TLDs in database: " . count($pricing) . "<br>";
    if ($stats) {
        echo "Average price: $" . number_format($stats['average_price'] ?? 0, 2) . "<br>";
        echo "Last updated: " . ($stats['newest_update'] ?? 'Never') . "<br>";
    }
    
    if (!empty($pricing)) {
        echo "<h3>Sample Pricing (first 5):</h3>";
        $count = 0;
        foreach ($pricing as $tld => $price) {
            if ($count >= 5) break;
            echo ".$tld - $price<br>";
            $count++;
        }
    }
} else {
    echo "❌ Cannot check pricing data - functions not available<br>";
}

// Test 6: API Test
echo "<h2>6. API Connection Test</h2>";
$graphql_url = 'https://api-weaveform.ip-tribe.com/graphql';
echo "GraphQL endpoint: $graphql_url<br>";

// Test if we can reach the endpoint with Bearer token
$response = wp_remote_post($graphql_url, array(
    'headers' => array(
        'Content-Type' => 'application/json',
        'Authorization' => 'Bearer ' . IPT_API_KEY
    ),
    'body' => json_encode(array(
        'query' => '{ __typename }'
    )),
    'timeout' => 10
));
if (is_wp_error($response)) {
    echo "❌ Cannot reach GraphQL endpoint: " . $response->get_error_message() . "<br>";
} else {
    $status_code = wp_remote_retrieve_response_code($response);
    echo "✅ GraphQL endpoint responds with status: $status_code<br>";
}

echo "<h2>7. Quick Actions</h2>";
echo "<p><a href='#' onclick='testPricingUpdate()' style='background: #0073aa; color: white; padding: 10px 15px; text-decoration: none; border-radius: 3px;'>Test Pricing Update</a></p>";
echo "<p><a href='" . admin_url('tools.php?page=domain-pricing') . "'>Go to Admin Page</a></p>";
echo "<p><a href='" . home_url('/customer/search_domain.php') . "'>Test Domain Search</a></p>";

?>

<script>
function testPricingUpdate() {
    if (confirm('This will attempt to fetch pricing from the API. Continue?')) {
        fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=update_domain_pricing&nonce=<?php echo wp_create_nonce('update_domain_pricing'); ?>'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Success: ' + data.data.message);
                location.reload();
            } else {
                alert('Error: ' + (data.data || 'Unknown error'));
            }
        })
        .catch(error => {
            alert('Network error: ' + error);
        });
    }
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
h1 { color: #333; border-bottom: 2px solid #0073aa; padding-bottom: 10px; }
h2 { color: #666; margin-top: 30px; border-left: 4px solid #0073aa; padding-left: 15px; }
h3 { color: #888; }
p { margin: 10px 0; }
a { color: #0073aa; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
