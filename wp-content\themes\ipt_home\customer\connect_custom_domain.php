<?php
// Ensure domain tables exist before loading page
ipt_ensure_domain_tables_exist();

// Get website_id from URL parameter
$website_id = isset($_GET['website_id']) ? intval($_GET['website_id']) : 0;

// Redirect back to search_domain if no website_id provided
if (empty($website_id)) {
    wp_safe_redirect(site_url('/customer/search_domain/'));
    exit;
}

include get_stylesheet_directory() . '/customer/header-customer.php';
include get_stylesheet_directory() . '/customer/header-dashboard.php';
?>

<div class="flex min-h-screen bg-neutral-light">
   <?php include get_stylesheet_directory() . '/customer/sidebar.php'; ?>
   <div class="flex-1 flex flex-col min-h-screen">

      <!-- Connect Custom Domain -->
      <main class="flex-1 p-4 bg-db-dark-2">
         <div class="bg-white rounded-lg shadow p-6">
           
             <nav class="" aria-label="Breadcrumb">
               <ol class="flex items-center space-x text-sm list-none ml-0 mb-1">
                  <li>
                     <a href="<?php echo site_url('/customer/subscription'); ?>" class="font-normal hover:text-db-text text-db-text  text-14 ">Settings</a>
                  </li>
                  <li>
                     <span class="text-neutral-400 text-12">
                        <svg width="22" height="30" viewBox="0 0 22 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                           <path fill-rule="evenodd" clip-rule="evenodd" d="M8 8.7L12.6 15L8 21.3L8.8 22L14 15L8.8 8L8 8.7Z" fill="#000624" fill-opacity="0.3"/>
                        </svg>
                     </span>
                  </li>
                  <li>
                     <a href="<?php echo site_url('/customer/search_domain/?website_id=' . $website_id); ?>" class="font-normal hover:text-db-text text-db-text text-14">Domain Search</a>
                  </li>
                  <li>
                     <span class="text-neutral-400 text-12">
                        <svg width="22" height="30" viewBox="0 0 22 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                           <path fill-rule="evenodd" clip-rule="evenodd" d="M8 8.7L12.6 15L8 21.3L8.8 22L14 15L8.8 8L8 8.7Z" fill="#000624" fill-opacity="0.3"/>
                        </svg>
                     </span>
                  </li>
                  <li>
                     <a href="#" class="font-normal hover:text-db-text text-db-text text-14">Connect Custom Domain</a>
                  </li>
               </ol>
            </nav>

            <!-- Page Title -->
            <h1 class="text-2xl font-semibold text-gray-900 mb-8">Connect Your Custom Domain</h1>
            
            <!-- Domain Input Section -->
            <div class="max-w-2xl mx-auto">
               <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
                  <div class="flex items-start">
                     <svg class="w-6 h-6 text-blue-600 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                     </svg>
                     <div>
                        <h3 class="text-lg font-medium text-blue-900 mb-2">Connect Your Existing Domain</h3>
                        <p class="text-blue-800">Enter your domain name below to get the nameserver information needed to connect it to our platform.</p>
                     </div>
                  </div>
               </div>

               <!-- Domain Input Form -->
               <form id="custom-domain-form" class="mb-8">
                  <div class="flex items-center bg-gray-100 rounded-xl p-2">
                     <div class="flex items-center flex-1 relative">
                        <!-- <div class="absolute left-4 flex items-center pointer-events-none">
                           <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                           </svg>
                        </div> -->
                        <input
                           type="text"
                           id="custom_domain"
                           name="custom_domain"
                           placeholder="Enter your domain (e.g., yourdomain.com)"
                           style="padding-left: 40px;"
                           class="w-full px-12 pr-4 py-3 bg-transparent border-0 text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-0 text-base"
                           required
                        />
                     </div>
                     <button
                        type="submit"
                        id="get-nameservers-btn"
                        class="ml-2 px-6 py-3 bg-blue-600 text-white font-medium text-base rounded-lg hover:bg-blue-700 focus:outline-none transition-colors"
                     >
                        Get Nameservers
                     </button>
                  </div>
               </form>

               <!-- Loading State -->
               <div id="loading-state" class="hidden text-center py-8">
                  <div class="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-blue-600 bg-blue-50">
                     <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                     </svg>
                     Getting nameserver information...
                  </div>
               </div>

               <!-- Nameserver Information Section -->
               <div id="nameserver-info" class="hidden">
                  <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
                     <h3 class="text-lg font-semibold text-green-900 mb-4">Nameserver Configuration</h3>
                     <p class="text-green-800 mb-4">To connect your domain <strong id="domain-name"></strong>, please update your domain's nameservers to:</p>
                     
                     <div class="bg-white border border-green-300 rounded-lg p-4 mb-4">
                        <div class="space-y-3">
                           <div class="flex items-center justify-between">
                              <div>
                                 <label class="block text-sm font-medium text-gray-700 mb-1">Nameserver 1:</label>
                                 <code id="nameserver-1" class="bg-gray-100 px-3 py-1 rounded text-sm font-mono">alexa.ns.cloudflare.com</code>
                              </div>
                              <button onclick="copyToClipboard('nameserver-1')" class="ml-2 px-3 py-1 bg-blue-100 text-blue-700 text-xs rounded hover:bg-blue-200 transition-colors">
                                 Copy
                              </button>
                           </div>
                           
                           <div class="flex items-center justify-between">
                              <div>
                                 <label class="block text-sm font-medium text-gray-700 mb-1">Nameserver 2:</label>
                                 <code id="nameserver-2" class="bg-gray-100 px-3 py-1 rounded text-sm font-mono">sean.ns.cloudflare.com</code>
                              </div>
                              <button onclick="copyToClipboard('nameserver-2')" class="ml-2 px-3 py-1 bg-blue-100 text-blue-700 text-xs rounded hover:bg-blue-200 transition-colors">
                                 Copy
                              </button>
                           </div>
                        </div>
                     </div>

                     <!-- Instructions -->
                     <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                        <h4 class="font-medium text-yellow-900 mb-2">How to update your nameservers:</h4>
                        <ol class="list-decimal list-inside text-sm text-yellow-800 space-y-1">
                           <li>Log in to your domain registrar's control panel</li>
                           <li>Find the DNS or Nameserver settings section</li>
                           <li>Replace the existing nameservers with the ones shown above</li>
                           <li>Save the changes (propagation may take 24-48 hours)</li>
                           <li>Once nameservers are updated, click the button below to connect your domain</li>
                        </ol>
                     </div>

                     <!-- Connect Domain Button -->
                     <div class="text-center">
                        <button
                           id="connect-domain-btn"
                           onclick="connectCustomDomain()"
                           class="px-6 py-3 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 focus:outline-none transition-colors"
                        >
                           Connect Domain to Website
                        </button>
                     </div>
                  </div>
               </div>

               <!-- Status Check Results -->
               <div id="status-results" class="hidden">
                  <!-- Status results will be shown here -->
               </div>

               <!-- Error Message -->
               <div id="error-message" class="hidden bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                  <div class="flex items-start">
                     <svg class="w-5 h-5 text-red-600 mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                     </svg>
                     <div>
                        <h3 class="text-sm font-medium text-red-800">Error</h3>
                        <p id="error-text" class="mt-1 text-sm text-red-700"></p>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </main>
   </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get website_id from PHP
    const websiteId = <?php echo json_encode($website_id); ?>;

    const form = document.getElementById('custom-domain-form');
    const domainInput = document.getElementById('custom_domain');
    const loadingState = document.getElementById('loading-state');
    const nameserverInfo = document.getElementById('nameserver-info');
    const errorMessage = document.getElementById('error-message');
    const statusResults = document.getElementById('status-results');

    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const domain = domainInput.value.trim();
        if (!domain) {
            showError('Please enter a domain name');
            return;
        }

        // Validate domain format
        const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/;
        if (!domainRegex.test(domain)) {
            showError('Please enter a valid domain name (e.g., yourdomain.com)');
            return;
        }

        await getNameservers(domain);
    });

    async function getNameservers(domain) {
        // Hide previous results and errors
        hideAll();
        loadingState.classList.remove('hidden');

        try {
            // Call GraphQL API to get nameservers
            const response = await fetch('https://api-weaveform.ip-tribe.com/graphql', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ASiOTwM9W9ToZ0zOSDsMF1OQYUmRumys'
                },
                body: JSON.stringify({
                    query: `query Webhooks_cloudflare_get_name_servers {
                        webhooks_cloudflare_get_name_servers(domain: "${domain}")
                    }`
                })
            });

            const data = await response.json();
            
            if (data.errors) {
                throw new Error(data.errors[0].message || 'Failed to get nameservers');
            }

            const nameservers = data.data?.webhooks_cloudflare_get_name_servers;
            
            if (nameservers && nameservers.length >= 2) {
                // Use API nameservers
                showNameservers(domain, nameservers[0], nameservers[1]);
            } else {
                // Use default nameservers
                showNameservers(domain, 'alexa.ns.cloudflare.com', 'sean.ns.cloudflare.com');
            }

        } catch (error) {
            console.error('Error getting nameservers:', error);
            // Use default nameservers on error
            showNameservers(domain, 'alexa.ns.cloudflare.com', 'sean.ns.cloudflare.com');
        } finally {
            loadingState.classList.add('hidden');
        }
    }

    function showNameservers(domain, ns1, ns2) {
        document.getElementById('domain-name').textContent = domain;
        document.getElementById('nameserver-1').textContent = ns1;
        document.getElementById('nameserver-2').textContent = ns2;
        nameserverInfo.classList.remove('hidden');
        
        // Store domain for status checking
        window.currentDomain = domain;
    }

    function showError(message) {
        document.getElementById('error-text').textContent = message;
        errorMessage.classList.remove('hidden');
        loadingState.classList.add('hidden');
    }

    function hideAll() {
        nameserverInfo.classList.add('hidden');
        errorMessage.classList.add('hidden');
        statusResults.classList.add('hidden');
    }

    // Global functions
    window.copyToClipboard = function(elementId) {
        const element = document.getElementById(elementId);
        const text = element.textContent;
        
        navigator.clipboard.writeText(text).then(function() {
            // Show temporary success message
            const button = element.parentElement.querySelector('button');
            const originalText = button.textContent;
            button.textContent = 'Copied!';
            button.classList.add('bg-green-100', 'text-green-700');
            button.classList.remove('bg-blue-100', 'text-blue-700');
            
            setTimeout(() => {
                button.textContent = originalText;
                button.classList.remove('bg-green-100', 'text-green-700');
                button.classList.add('bg-blue-100', 'text-blue-700');
            }, 2000);
        }).catch(function(err) {
            console.error('Could not copy text: ', err);
        });
    };

    window.connectCustomDomain = async function() {
        if (!window.currentDomain) {
            showError('No domain specified');
            return;
        }

        const connectBtn = document.getElementById('connect-domain-btn');
        const originalText = connectBtn.textContent;
        connectBtn.textContent = 'Connecting...';
        connectBtn.disabled = true;

        try {
            // Call GraphQL API to connect custom domain
            const response = await fetch('https://api-weaveform.ip-tribe.com/graphql', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ASiOTwM9W9ToZ0zOSDsMF1OQYUmRumys'
                },
                body: JSON.stringify({
                    query: `mutation Webhooks_custom_domain_process_with_other_provider2 {
                        webhooks_custom_domain_process_with_other_provider(
                            body: { websiteId: "${websiteId}", domain: "${window.currentDomain}" }
                        ) {
                            ip
                            isSuccess
                        }
                    }`
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.errors) {
                throw new Error(data.errors[0].message || 'Failed to connect domain');
            }

            const result = data.data?.webhooks_custom_domain_process_with_other_provider;

            // Display the connection results
            displayConnectionResults(result);

            // If connection was successful, save to database
            if (result && result.isSuccess === true) {
                saveCustomDomainToDatabase(window.currentDomain, websiteId, result.ip);
            }

        } catch (error) {
            console.error('Error connecting custom domain:', error);

            // Show error with more specific information
            statusResults.innerHTML = `
                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-red-600 mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>
                            <h3 class="text-sm font-medium text-red-800">Connection Failed</h3>
                            <p class="mt-1 text-sm text-red-700">
                                Unable to connect <strong>${window.currentDomain}</strong> to website ID ${websiteId}
                                <br>Error: ${error.message}
                                <br><br>Please ensure:
                                <br>• Domain nameservers are correctly updated at your registrar
                                <br>• DNS propagation has completed (can take 24-48 hours)
                                <br>• Domain is properly configured and accessible
                            </p>
                        </div>
                    </div>
                </div>
            `;
            statusResults.classList.remove('hidden');
        } finally {
            connectBtn.textContent = originalText;
            connectBtn.disabled = false;
        }
    };

    function displayConnectionResults(result) {
        let statusHtml = '';

        if (result && result.isSuccess === true) {
            // Success case - domain connected successfully
            statusHtml = `
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-green-600 mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <div>
                            <h3 class="text-sm font-medium text-green-800">🎉 Domain Connected Successfully!</h3>
                            <p class="mt-1 text-sm text-green-700">
                                Your custom domain <strong>${window.currentDomain}</strong> has been successfully connected to website ID ${websiteId}.
                                ${result.ip ? '<br><strong>Server IP:</strong> ' + result.ip : ''}
                                <br><br><strong>Next steps:</strong>
                                <br>• Your domain is now active and ready to use
                                <br>• It may take a few minutes for changes to propagate globally
                                <br>• You can now access your website using this custom domain
                            </p>
                        </div>
                    </div>
                </div>
            `;
        } else {
            // Failed case - domain connection failed
            statusHtml = `
                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-red-600 mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>
                            <h3 class="text-sm font-medium text-red-800">❌ Domain Connection Failed</h3>
                            <p class="mt-1 text-sm text-red-700">
                                Unable to connect domain <strong>${window.currentDomain}</strong> to website ID ${websiteId}.
                                <br><br><strong>Possible reasons:</strong>
                                <br>• Domain nameservers are not properly configured
                                <br>• DNS propagation is still in progress (can take 24-48 hours)
                                <br>• Domain is not accessible or has configuration issues
                                <br>• Website ID may not be valid or accessible
                                <br><br><strong>Please try again later or contact support if the issue persists.</strong>
                            </p>
                        </div>
                    </div>
                </div>
            `;
        }

        statusResults.innerHTML = statusHtml;
        statusResults.classList.remove('hidden');
    }

    // Function to save custom domain to database
    async function saveCustomDomainToDatabase(domain, websiteId, serverIp) {
        try {
            const response = await fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'save_custom_domain',
                    domain: domain,
                    website_id: websiteId,
                    server_ip: serverIp || '',
                    nonce: '<?php echo wp_create_nonce('save-custom-domain-nonce'); ?>'
                })
            });

            const data = await response.json();

            if (data.success) {
                console.log('Custom domain saved to database successfully');
            } else {
                console.error('Failed to save custom domain:', data.data);
            }
        } catch (error) {
            console.error('Error saving custom domain to database:', error);
        }
    }
});
</script>

<?php include get_stylesheet_directory() . '/customer/footer-customer.php'; ?>
