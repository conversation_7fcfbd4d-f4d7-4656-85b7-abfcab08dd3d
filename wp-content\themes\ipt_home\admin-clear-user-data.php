<?php
/**
 * Admin page to clear user subscription data
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check if user is admin
if (!current_user_can('manage_options')) {
    wp_die('You do not have sufficient permissions to access this page.');
}

// Handle form submission
if (isset($_POST['clear_user_data']) && wp_verify_nonce($_POST['_wpnonce'], 'clear_user_data')) {
    $user_id = intval($_POST['user_id']);
    
    if ($user_id > 0) {
        // Clear user meta
        delete_user_meta($user_id, 'customer_id');
        delete_user_meta($user_id, '_ipt_has_used_trial');
        
        // Clear WooCommerce sessions for this user
        global $wpdb;
        $wpdb->delete(
            $wpdb->options,
            array('option_name' => 'wc_session_' . $user_id)
        );
        
        // Clear any cached data
        wp_cache_flush();
        
        echo '<div class="notice notice-success"><p>User subscription data cleared successfully for User ID: ' . $user_id . '</p></div>';
        error_log("Admin cleared subscription data for user {$user_id}");
    }
}

// Handle clear all sessions
if (isset($_POST['clear_all_sessions']) && wp_verify_nonce($_POST['_wpnonce'], 'clear_all_sessions')) {
    global $wpdb;
    
    // Clear all WooCommerce sessions
    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_wc_session_%'");
    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_wc_session_%'");
    
    // Clear cache
    wp_cache_flush();
    
    echo '<div class="notice notice-success"><p>All WooCommerce sessions cleared successfully!</p></div>';
    error_log("Admin cleared all WooCommerce sessions");
}

?>

<div class="wrap">
    <h1>Clear User Subscription Data</h1>
    
    <div class="card">
        <h2>Clear Specific User Data</h2>
        <form method="post">
            <?php wp_nonce_field('clear_user_data'); ?>
            <table class="form-table">
                <tr>
                    <th scope="row">User ID</th>
                    <td>
                        <input type="number" name="user_id" value="<?php echo get_current_user_id(); ?>" min="1" required>
                        <p class="description">Enter the User ID to clear subscription data for. Current user ID: <?php echo get_current_user_id(); ?></p>
                    </td>
                </tr>
            </table>
            <p class="submit">
                <input type="submit" name="clear_user_data" class="button-primary" value="Clear User Data">
            </p>
        </form>
    </div>
    
    <div class="card">
        <h2>Clear All Sessions</h2>
        <form method="post">
            <?php wp_nonce_field('clear_all_sessions'); ?>
            <p>This will clear all WooCommerce sessions for all users.</p>
            <p class="submit">
                <input type="submit" name="clear_all_sessions" class="button-secondary" value="Clear All Sessions" onclick="return confirm('Are you sure you want to clear all WooCommerce sessions?')">
            </p>
        </form>
    </div>
    
    <div class="card">
        <h2>Manual Steps</h2>
        <p>If the above doesn't work, you can manually run these SQL commands in phpMyAdmin:</p>
        <pre style="background: #f1f1f1; padding: 10px; border-radius: 4px;">
-- Clear customer_id from user meta
DELETE FROM wp_usermeta WHERE meta_key = 'customer_id';

-- Clear trial usage flags
DELETE FROM wp_usermeta WHERE meta_key = '_ipt_has_used_trial';

-- Clear all WooCommerce sessions
DELETE FROM wp_options WHERE option_name LIKE '_transient_wc_session_%';
DELETE FROM wp_options WHERE option_name LIKE '_transient_timeout_wc_session_%';
        </pre>
    </div>
    
    <div class="card">
        <h2>Test Subscription Page</h2>
        <p>After clearing data:</p>
        <ol>
            <li>Go to the <a href="<?php echo home_url('?customer_page=subscription'); ?>" target="_blank">Subscription Page</a></li>
            <li>You should see the "Upgrade" button</li>
            <li>Click it to go to the upgrade page and see available plans</li>
        </ol>
    </div>
</div>

<style>
.card {
    background: white;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin: 20px 0;
}
.card h2 {
    margin-top: 0;
}
</style>
