<?php
// Start session for storing registration data
if (!session_id()) {
    session_start();
}

// Ensure domain tables exist before loading page
ipt_ensure_domain_tables_exist();

// Load database functions
require_once get_stylesheet_directory() . '/inc/database.php';

// Get domain information from URL parameters
$domain = isset($_GET['domain']) ? sanitize_text_field($_GET['domain']) : '';
$price = isset($_GET['price']) ? sanitize_text_field($_GET['price']) : '';
$is_premium = isset($_GET['premium']) && $_GET['premium'] === 'true';
$website_id = isset($_GET['website_id']) ? intval($_GET['website_id']) : 0;

// Redirect if no domain specified
if (empty($domain)) {
    $redirect_url = site_url('/customer/search_domain/');
    if ($website_id > 0) {
        $redirect_url .= '?website_id=' . $website_id;
    }
    wp_safe_redirect($redirect_url);
    exit;
}

// Parse price to get numeric value
$price_numeric = floatval(str_replace(['$', '/year'], '', $price));
$amount = (int) ($price_numeric * 100); // Convert to cents for Stripe

// Check for successful payment
if (isset($_GET['payment_intent']) && isset($_GET['redirect_status']) && $_GET['redirect_status'] === 'succeeded') {
    $payment_intent_id = sanitize_text_field($_GET['payment_intent']);

    if (!empty($payment_intent_id)) {
        error_log('Processing domain payment success for: ' . $domain . ' - Payment Intent: ' . $payment_intent_id);

        // Update domain status to completed in database
        $domain_record = ipt_get_domain_by_payment_intent($payment_intent_id);
        if ($domain_record) {
            $update_result = ipt_update_domain_status($domain_record['id'], 'completed', $payment_intent_id);
            if ($update_result) {
                error_log('Domain status updated to completed for domain: ' . $domain . ' (ID: ' . $domain_record['id'] . ')');
            } else {
                error_log('Failed to update domain status for domain: ' . $domain . ' (ID: ' . $domain_record['id'] . ')');
            }
        } else {
            error_log('Domain record not found for payment intent: ' . $payment_intent_id);
        }

        error_log('🔍 BACKEND STEP 11: Retrieving Registration Data from Session');

        // Get domain registration data from session
        if (!session_id()) {
            session_start();
            error_log('🔄 Session started with ID: ' . session_id());
        } else {
            error_log('✅ Session already active with ID: ' . session_id());
        }

        error_log('🔍 Current session contents: ' . json_encode($_SESSION));

        $registration_data = null;
        if (isset($_SESSION['domain_registration_data']) && $_SESSION['domain_registration_data'] !== null && !empty($_SESSION['domain_registration_data'])) {
            $registration_data = $_SESSION['domain_registration_data'];
            error_log('✅ Registration data found in session: ' . json_encode($registration_data));
            unset($_SESSION['domain_registration_data']); // Clear after use
            error_log('🗑️ Registration data cleared from session');
        } else {
            error_log('❌ No valid domain_registration_data found in session');
            error_log('🔍 Session value: ' . json_encode($_SESSION['domain_registration_data'] ?? 'KEY_NOT_SET'));
            error_log('🔍 Available session keys: ' . json_encode(array_keys($_SESSION)));

            // Check if the key exists but is null/empty
            if (isset($_SESSION['domain_registration_data'])) {
                if ($_SESSION['domain_registration_data'] === null) {
                    error_log('🚨 Session key exists but value is NULL - this indicates JSON decode failure during storage');
                } elseif (empty($_SESSION['domain_registration_data'])) {
                    error_log('🚨 Session key exists but value is EMPTY - this indicates data loss during storage');
                }
            }
        }

        // Call Namecheap domain registration API
        if ($registration_data) {
            error_log('🌐 BACKEND STEP 11A: Starting Namecheap Domain Registration');
            error_log('Domain payment successful for: ' . $domain . ' - Payment Intent: ' . $payment_intent_id);
            error_log('Registration data: ' . json_encode($registration_data));

            // Add domain name to registration data
            $registration_data['domainName'] = $domain;

            // Call the Namecheap API to register the domain
            $namecheap_result = call_namecheap_domain_registration_api($registration_data);

            if (is_wp_error($namecheap_result)) {
                error_log('❌ Domain registration failed: ' . $namecheap_result->get_error_message());
                // Store error for user to see
                set_transient('domain_registration_error_' . get_current_user_id(), $namecheap_result->get_error_message(), 300);
            } else {
                error_log('✅ Domain registration successful: ' . json_encode($namecheap_result));
                // Store success message
                set_transient('domain_registration_success_' . get_current_user_id(), 'Domain registered successfully!', 300);
            }
        } else {
            error_log('❌ BACKEND STEP 11: No registration data found in session for domain: ' . $domain);
            error_log('🚨 This means the GraphQL API call to Namecheap will NOT be made');
            set_transient('domain_registration_error_' . get_current_user_id(), 'Registration data not found. Please contact support.', 300);
        }

        // Redirect to domain management page with website_id if available
        $redirect_url = site_url('/customer/domain_management/');
        if ($website_id > 0) {
            $redirect_url .= '?website_id=' . $website_id;
        }
        wp_safe_redirect($redirect_url);
        exit;
    }
}

// Get current user info
$current_user = wp_get_current_user();
$user_name = $current_user->ID ? $current_user->display_name : '';
$user_email = $current_user->ID ? $current_user->user_email : '';
$user_phone = get_user_meta($current_user->ID, 'billing_phone', true);
$user_address = get_user_meta($current_user->ID, 'billing_address_1', true);

// Get Stripe settings
$stripe_settings = get_option('woocommerce_stripe_settings');
$test_mode = isset($stripe_settings['testmode']) && 'yes' === $stripe_settings['testmode'];
$stripe_key = $test_mode ?
    (isset($stripe_settings['test_publishable_key']) ? $stripe_settings['test_publishable_key'] : '') :
    (isset($stripe_settings['publishable_key']) ? $stripe_settings['publishable_key'] : '');
?>

<?php include get_stylesheet_directory() . '/customer/header-customer.php'; ?>
<?php include get_stylesheet_directory() . '/customer/header-dashboard.php'; ?>

<div class="flex min-h-screen bg-neutral-light">
   <?php include get_stylesheet_directory() . '/customer/sidebar.php'; ?>
   <div class="flex-1 flex flex-col min-h-screen">
      
      <!-- Breadcrumb -->
      <div class="bg-white border-b border-gray-200 px-6 py-4">
         <nav aria-label="Breadcrumb">
            <ol class="flex items-center space-x-2 text-sm list-none ml-0 mb-1">
               <li><a href="<?php echo site_url('/customer/subscription'); ?>" class="text-db-text hover:text-db-text text-14">Settings</a></li>
               <li><span class="text-neutral-400 text-12">→</span></li>
               <li><a href="<?php echo site_url('/customer/domain_management'); ?>" class="text-db-text hover:text-db-text text-14">Domain management</a></li>
               <li><span class="text-neutral-400 text-12">→</span></li>
               <li><a href="<?php echo site_url('/customer/search_domain'); ?>" class="text-db-text hover:text-db-text text-14">Search Domain</a></li>
               <li><span class="text-neutral-400 text-12">→</span></li>
               <li><span class="text-db-text text-14">Purchase domain</span></li>
            </ol>
         </nav>
      </div>

      <!-- Main Content -->
      <main class="flex-1 p-4 bg-db-dark-2">
         <div class="bg-white rounded-lg shadow p-4">
            <div class="grid grid-cols-1 md:grid-cols-12 gap-6">
               
               <!-- Left Column: Payment Information -->
               <div class="md:col-span-6 col-span-12">
                  <div class="font-semibold text-lg mb-4">Payment Information</div>
                  <div class="payment-container">
                      <!-- Error Message -->
                      <div id="error-message" class="alert alert-danger bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4" style="display: none;"></div>
                      
                      <!-- Payment Method Selection -->
                      <div class="mb-6">
                          <h3 class="text-lg font-semibold mb-4">Choose Payment Method</h3>
                          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <!-- Card Payment Option -->
                              <div class="payment-method-option border-2 border-gray-200 rounded-lg p-4 cursor-pointer hover:border-blue-500 transition-colors" data-method="card">
                                  <div class="flex items-center mb-2">
                                      <input type="radio" id="payment-card" name="payment_method" value="card" class="mr-3" checked>
                                      <label for="payment-card" class="font-medium cursor-pointer">Credit/Debit Card</label>
                                  </div>
                                  <div class="flex items-center space-x-2 text-sm text-gray-600">
                                      <span>💳</span>
                                      <span>Visa, Mastercard, AMEX</span>
                                  </div>
                              </div>

                              <!-- PayNow Option -->
                              <!-- <div class="payment-method-option border-2 border-gray-200 rounded-lg p-4 cursor-pointer hover:border-blue-500 transition-colors" data-method="paynow">
                                  <div class="flex items-center mb-2">
                                      <input type="radio" id="payment-paynow" name="payment_method" value="paynow" class="mr-3">
                                      <label for="payment-paynow" class="font-medium cursor-pointer">PayNow QR</label>
                                  </div>
                                  <div class="flex items-center space-x-2 text-sm text-gray-600">
                                      <span>📱</span>
                                      <span>Scan QR with banking app</span>
                                  </div>
                              </div> -->
                          </div>
                      </div>

                      <!-- Card Payment Form -->
                      <div id="card-payment-form" class="payment-form">
                          <!-- Loading indicator -->
                          <div id="payment-loading" class="text-center p-4">
                              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
                              <p class="mt-2">Initializing payment...</p>
                          </div>
                      </div>

                      <!-- PayNow Payment Form -->
                      <div id="paynow-payment-form" class="payment-form hidden">
                          <div class="text-center p-6">
                              <h4 class="text-lg font-semibold mb-4">PayNow QR Payment</h4>
                              <div id="paynow-qr-container" class="mb-4">
                                  <!-- QR code will be displayed here -->
                              </div>
                              <p class="text-sm text-gray-600 mb-4">
                                  Scan the QR code with your banking app to complete the payment
                              </p>
                              <div id="paynow-status" class="text-sm">
                                  <span class="text-blue-600">⏳ Waiting for payment...</span>
                              </div>
                          </div>
                      </div>
                      
                      <!-- Payment Form -->
                      <form id="payment-form" style="display: none;">

                          <!-- Domain Registration Contact Information -->
                          <div class="mb-4">
                              <h3 class="text-lg font-medium text-gray-900 mb-3">Domain Registration Information</h3>
                              <p class="text-sm text-gray-600 mb-4">This information will be used for domain registration with Namecheap. All fields marked with * are required.</p>

                              <!-- Domain Registration Contact Information (Namecheap API Format) -->
                              <div class="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
                                  <div>
                                      <label for="first_name" class="block text-sm font-medium text-gray-700 mb-1">First Name *</label>
                                      <input type="text" id="first_name" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="<?php echo esc_attr($user_first_name ?? ''); ?>" required>
                                  </div>

                                  <div>
                                      <label for="last_name" class="block text-sm font-medium text-gray-700 mb-1">Last Name *</label>
                                      <input type="text" id="last_name" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="<?php echo esc_attr($user_last_name ?? ''); ?>" required>
                                  </div>
                              </div>

                              <div class="mb-3">
                                  <label for="email_address" class="block text-sm font-medium text-gray-700 mb-1">Email Address *</label>
                                  <input type="email" id="email_address" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="<?php echo esc_attr($user_email); ?>" required>
                                  <small class="text-gray-500">This email will receive domain registration confirmation and renewal notices</small>
                              </div>

                              <div class="mb-3">
                                  <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number *</label>
                                  <input type="tel" id="phone" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="<?php echo esc_attr($user_phone); ?>" placeholder="+84.986007204" required>
                                  <small class="text-gray-500">Format: +CountryCode.PhoneNumber (e.g., +84.986007204 for Vietnam)</small>
                              </div>

                              <div class="mb-3">
                                  <label for="address1" class="block text-sm font-medium text-gray-700 mb-1">Address *</label>
                                  <input type="text" id="address1" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="<?php echo esc_attr($user_address); ?>" placeholder="Street address" required>
                              </div>

                              <div class="grid grid-cols-1 md:grid-cols-3 gap-3 mb-3">
                                  <div>
                                      <label for="city" class="block text-sm font-medium text-gray-700 mb-1">City *</label>
                                      <input type="text" id="city" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="<?php echo esc_attr($user_city ?? ''); ?>" placeholder="Hanoi" required>
                                  </div>

                                  <div>
                                      <label for="province" class="block text-sm font-medium text-gray-700 mb-1">Province/State *</label>
                                      <input type="text" id="province" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="<?php echo esc_attr($user_state ?? ''); ?>" placeholder="Hanoi" required>
                                  </div>

                                  <div>
                                      <label for="postal_code" class="block text-sm font-medium text-gray-700 mb-1">Postal Code *</label>
                                      <input type="text" id="postal_code" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="<?php echo esc_attr($user_postal_code ?? ''); ?>" placeholder="100000" required>
                                  </div>
                              </div>

                              <div class="mb-4">
                                  <label for="country" class="block text-sm font-medium text-gray-700 mb-1">Country *</label>
                                  <select id="country" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                      <option value="">Select Country</option>
                                      <option value="VN" <?php echo ($user_country ?? '') === 'VN' ? 'selected' : ''; ?>>Vietnam</option>
                                      <option value="US" <?php echo ($user_country ?? '') === 'US' ? 'selected' : ''; ?>>United States</option>
                                      <option value="CA" <?php echo ($user_country ?? '') === 'CA' ? 'selected' : ''; ?>>Canada</option>
                                      <option value="GB" <?php echo ($user_country ?? '') === 'GB' ? 'selected' : ''; ?>>United Kingdom</option>
                                      <option value="AU" <?php echo ($user_country ?? '') === 'AU' ? 'selected' : ''; ?>>Australia</option>
                                      <option value="SG" <?php echo ($user_country ?? '') === 'SG' ? 'selected' : ''; ?>>Singapore</option>
                                      <option value="MY" <?php echo ($user_country ?? '') === 'MY' ? 'selected' : ''; ?>>Malaysia</option>
                                      <option value="IN" <?php echo ($user_country ?? '') === 'IN' ? 'selected' : ''; ?>>India</option>
                                      <option value="DE" <?php echo ($user_country ?? '') === 'DE' ? 'selected' : ''; ?>>Germany</option>
                                      <option value="FR" <?php echo ($user_country ?? '') === 'FR' ? 'selected' : ''; ?>>France</option>
                                      <option value="JP" <?php echo ($user_country ?? '') === 'JP' ? 'selected' : ''; ?>>Japan</option>
                                      <option value="NL" <?php echo ($user_country ?? '') === 'NL' ? 'selected' : ''; ?>>Netherlands</option>
                                      <option value="IT" <?php echo ($user_country ?? '') === 'IT' ? 'selected' : ''; ?>>Italy</option>
                                      <option value="ES" <?php echo ($user_country ?? '') === 'ES' ? 'selected' : ''; ?>>Spain</option>
                                      <option value="BR" <?php echo ($user_country ?? '') === 'BR' ? 'selected' : ''; ?>>Brazil</option>
                                      <option value="MX" <?php echo ($user_country ?? '') === 'MX' ? 'selected' : ''; ?>>Mexico</option>
                                      <option value="TH" <?php echo ($user_country ?? '') === 'TH' ? 'selected' : ''; ?>>Thailand</option>
                                      <option value="ID" <?php echo ($user_country ?? '') === 'ID' ? 'selected' : ''; ?>>Indonesia</option>
                                      <option value="PH" <?php echo ($user_country ?? '') === 'PH' ? 'selected' : ''; ?>>Philippines</option>
                                  </select>
                              </div>
                          </div>
                          
                          <div class="mb-3">
                            
                              <label for="payment-element" class="block text-sm font-medium text-gray-700 mb-1">Payment Information</label>
                              <div id="payment-element" class="border border-gray-300 rounded-md p-3"></div>
                          </div>
                          
                          <button id="submit-button" type="submit" class="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50" disabled>
                              Purchase Domain - <?php echo esc_html($price); ?>
                          </button>
                      </form>
                  </div>
               </div>

               <!-- Right Column: Domain Summary -->
               <div class="md:col-span-6 col-span-12">
                  <div class="font-semibold text-lg mb-4">Domain Purchase Summary</div>
                  <div class="bg-gray-50 p-4 rounded-lg">
                     <div class="space-y-4">
                        <!-- Domain Info -->
                        <div class="flex justify-between items-center border-b border-gray-200 pb-3">
                           <div>
                              <div class="font-medium text-lg"><?php echo esc_html($domain); ?></div>
                              <!-- <div class="text-sm text-gray-500">
                                 <?php if ($is_premium): ?>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                       Premium Domain
                                    </span>
                                 <?php else: ?>
                                    <span class="text-green-600">Standard Domain</span>
                                 <?php endif; ?>
                              </div> -->
                              <!-- <div class="text-sm text-gray-500 mt-1">Registration Period: 1 Year</div> -->
                           </div>
                           <div class="text-right">
                              <div class="font-medium text-lg"><?php echo esc_html($price); ?></div>
                              <!-- <div class="text-sm text-gray-500">per year</div> -->
                           </div>
                        </div>
                        
                        <!-- Features -->
                        <!-- <div class="text-sm text-gray-600">
                           <div class="font-medium mb-2">Included Features:</div>
                           <ul class="space-y-1">
                              <li>✓ Domain registration for 1 year</li>
                              <li>✓ DNS management</li>
                              <li>✓ Domain forwarding</li>
                              <li>✓ Email forwarding</li>
                              <li>✓ 24/7 customer support</li>
                           </ul>
                        </div> -->
                        
                        <!-- Total -->
                        <div class="flex justify-between items-center pt-2 font-semibold text-lg border-t border-gray-300">
                           <div>Total</div>
                           <div><?php echo esc_html($price); ?></div>
                        </div>
                     </div>
                  </div>
                  
                  <!-- Domain Registration Notice -->
                  <!-- <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                     <div class="flex items-start">
                        <svg class="w-5 h-5 text-blue-600 mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                           <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div class="text-sm text-blue-800">
                           <div class="font-medium">Domain Registration</div>
                           <p class="mt-1">After successful payment, your domain will be registered and activated within 24 hours. You will receive confirmation via email.</p>
                        </div>
                     </div>
                  </div> -->
               </div>
            </div>
         </div>
      </main>
   </div>
</div>

<!-- Stripe JS and Styles -->
<style>
.payment-method-option {
    transition: all 0.3s ease;
}

.payment-method-option:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.payment-method-option.selected {
    border-color: #3b82f6 !important;
    background-color: #eff6ff !important;
}

#paynow-qr-container img {
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    background: white;
}

.payment-form {
    transition: opacity 0.3s ease;
}

.hidden {
    display: none !important;
}

.alert {
    border-radius: 6px;
}
</style>

<script src="https://js.stripe.com/v3/"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Domain payment specific data
    const domainData = {
        domain: '<?php echo esc_js($domain); ?>',
        price: '<?php echo esc_js($price); ?>',
        amount: <?php echo $amount; ?>,
        isPremium: <?php echo $is_premium ? 'true' : 'false'; ?>
    };


    // Initialize Stripe
    try {
        const stripeKey = '<?php echo esc_js($stripe_key); ?>';

        if (!stripeKey || stripeKey.length === 0) {
            throw new Error('Stripe publishable key is not configured');
        }

        const stripe = Stripe(stripeKey);

        // Payment method selection handling
        const paymentMethodOptions = document.querySelectorAll('.payment-method-option');
        const cardForm = document.getElementById('card-payment-form');
        const paynowForm = document.getElementById('paynow-payment-form');

        // Handle payment method selection
        paymentMethodOptions.forEach(option => {
            option.addEventListener('click', function() {
                const method = this.dataset.method;
                const radio = this.querySelector('input[type="radio"]');

                // Update radio selection
                radio.checked = true;

                // Update visual selection
                paymentMethodOptions.forEach(opt => {
                    opt.classList.remove('border-blue-500', 'bg-blue-50');
                    opt.classList.add('border-gray-200');
                });
                this.classList.remove('border-gray-200');
                this.classList.add('border-blue-500', 'bg-blue-50');

                // Show/hide appropriate form
                if (method === 'card') {
                    cardForm.classList.remove('hidden');
                    paynowForm.classList.add('hidden');
                    initCardPayment();
                } else if (method === 'paynow') {
                    cardForm.classList.add('hidden');
                    paynowForm.classList.remove('hidden');
                    initPayNowPayment();
                }
            });
        });

        // Initialize with card payment by default
        initCardPayment();

        function initCardPayment() {
            initPayment();
        }

        async function initPayNowPayment() {
            try {
                document.getElementById('paynow-status').innerHTML = '<span class="text-blue-600">⏳ Creating PayNow payment...</span>';

                const response = await fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'create_domain_paynow_payment',
                        domain: domainData.domain,
                        amount: domainData.amount,
                        currency: 'SGD',
                        nonce: '<?php echo wp_create_nonce('domain_paynow_payment_nonce'); ?>'
                    })
                });

                const data = await response.json();

                if (data.success && data.qr_code) {
                    document.getElementById('paynow-qr-container').innerHTML =
                        `<img src="${data.qr_code}" alt="PayNow QR Code" class="mx-auto max-w-xs">`;
                    document.getElementById('paynow-status').innerHTML =
                        '<span class="text-blue-600">⏳ Waiting for payment...</span>';

                    pollPaymentStatus(data.payment_intent_id);
                } else {
                    document.getElementById('paynow-status').innerHTML =
                        '<span class="text-red-600">❌ Error creating PayNow payment</span>';
                }
            } catch (error) {
                document.getElementById('paynow-status').innerHTML =
                    '<span class="text-red-600">❌ Error creating PayNow payment</span>';
            }
        }

        function pollPaymentStatus(paymentIntentId) {
            const pollInterval = setInterval(async () => {
                try {
                    const response = await fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: new URLSearchParams({
                            action: 'check_domain_payment_status',
                            payment_intent_id: paymentIntentId,
                            domain: domainData.domain,
                            nonce: '<?php echo wp_create_nonce('check_domain_payment_nonce'); ?>'
                        })
                    });

                    const data = await response.json();

                    if (data.status === 'succeeded') {
                        clearInterval(pollInterval);
                        document.getElementById('paynow-status').innerHTML =
                            '<span class="text-green-600">✅ Payment successful!</span>';
                        // Redirect to domain management page
                        <?php
                        $redirect_url = site_url('/customer/domain_management/');
                        if ($website_id > 0) {
                            $redirect_url .= '?website_id=' . $website_id;
                        }
                        ?>
                        window.location.href = '<?php echo esc_js($redirect_url); ?>';
                    } else if (data.status === 'failed') {
                        clearInterval(pollInterval);
                        document.getElementById('paynow-status').innerHTML =
                            '<span class="text-red-600">❌ Payment failed</span>';
                    }
                } catch (error) {
                }
            }, 3000);

            // Stop polling after 10 minutes
            setTimeout(() => {
                clearInterval(pollInterval);
                document.getElementById('paynow-status').innerHTML =
                    '<span class="text-yellow-600">⏰ Payment timeout. Please try again.</span>';
            }, 600000);
        }

        async function initPayment() {
            console.log('💳 STEP 8: Payment Initialization Started');
            console.log('📝 Domain Data:', domainData);

            try {
                // Show loading, hide form and error message
                document.getElementById('payment-loading').style.display = 'block';
                document.getElementById('payment-form').style.display = 'none';
                document.getElementById('error-message').style.display = 'none';

                console.log('📡 STEP 8A: Creating Stripe Payment Intent');
                const requestData = {
                    'action': 'create_domain_payment_intent',
                    'domain': domainData.domain,
                    'amount': domainData.amount,
                    'security': '<?php echo wp_create_nonce('domain-payment-nonce'); ?>',
                };
                console.log('📦 Payment Intent Request:', requestData);

                // Create payment intent for domain purchase
                const response = await fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams(requestData),
                });

                console.log('📊 Payment Intent Response Status:', response.status, response.statusText);
                const data = await response.json();
                console.log('📄 Payment Intent Response:', data);

                // Hide loading
                document.getElementById('payment-loading').style.display = 'none';

                if (!data.success || !data.data || !data.data.client_secret) {
                    const errorMessage = data.data && data.data.message
                        ? data.data.message
                        : 'Could not create payment. Please try again.';
                    console.log('❌ Payment Intent Failed:', errorMessage);
                    showError(errorMessage);
                    return;
                }

                const clientSecret = data.data.client_secret;
                console.log('✅ Payment Intent Created Successfully');
                console.log('🔑 Client Secret:', clientSecret.substring(0, 20) + '...');

                console.log('🎨 STEP 8B: Initializing Stripe Elements');
                // Initialize Stripe Elements with clientSecret
                const elements = stripe.elements({
                    clientSecret: clientSecret,
                    appearance: {
                        theme: 'stripe',
                        variables: {
                            colorPrimary: '#3b82f6',
                        },
                    },
                });

                // Create and mount payment element
                console.log('🔧 Creating payment element...');
                const paymentElement = elements.create('payment');

                // Show form before mounting element
                document.getElementById('payment-form').style.display = 'block';

                // Mount payment element
                console.log('📌 Mounting payment element...');
                paymentElement.mount('#payment-element');

                // Enable submit button
                document.getElementById('submit-button').disabled = false;
                console.log('✅ Payment form ready for user input');

                // Handle form submission
                const submitButton = document.getElementById('submit-button');
                const form = document.getElementById('payment-form');

                form.addEventListener('submit', async function(event) {
                    event.preventDefault();
                    console.log('💳 STEP 9: Payment Form Submitted');

                    // Disable submit button
                    submitButton.disabled = true;
                    submitButton.textContent = 'Processing...';

                    // Get domain registration data (matching Namecheap API format)
                    const domainRegistrationData = {
                        FirstName: document.getElementById('first_name').value.trim(),
                        LastName: document.getElementById('last_name').value.trim(),
                        EmailAddress: document.getElementById('email_address').value.trim(),
                        Phone: document.getElementById('phone').value.trim(),
                        Address1: document.getElementById('address1').value.trim(),
                        City: document.getElementById('city').value.trim(),
                        Province: document.getElementById('province').value.trim(),
                        PostalCode: document.getElementById('postal_code').value.trim(),
                        Country: document.getElementById('country').value
                    };

                    console.log('📝 STEP 9A: Domain Registration Data Collected');
                    console.log('👤 Registration Data:', domainRegistrationData);

                    console.log('✅ STEP 9B: Validating Form Data');
                    // Validate required fields
                    const requiredFields = ['FirstName', 'LastName', 'EmailAddress', 'Phone', 'Address1', 'City', 'Province', 'PostalCode', 'Country'];
                    for (const field of requiredFields) {
                        if (!domainRegistrationData[field]) {
                            const fieldName = field.replace(/([A-Z])/g, ' $1').trim().toLowerCase();
                            console.log('❌ Validation failed for field:', field);
                            showError(`Please fill in the ${fieldName} field.`);
                            submitButton.disabled = false;
                            submitButton.textContent = 'Purchase Domain - <?php echo esc_js($price); ?>';
                            return;
                        }
                    }

                    // Validate email format
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(domainRegistrationData.EmailAddress)) {
                        console.log('❌ Email validation failed:', domainRegistrationData.EmailAddress);
                        showError('Please enter a valid email address.');
                        submitButton.disabled = false;
                        submitButton.textContent = 'Purchase Domain - <?php echo esc_js($price); ?>';
                        return;
                    }

                    // Validate phone format for Namecheap (should include country code with dot)
                    const phoneRegex = /^\+\d{1,3}\.\d{4,15}$/;
                    if (!phoneRegex.test(domainRegistrationData.Phone)) {
                        console.log('❌ Phone validation failed:', domainRegistrationData.Phone);
                        showError('Please enter phone number in format: +CountryCode.PhoneNumber (e.g., +84.986007204)');
                        submitButton.disabled = false;
                        submitButton.textContent = 'Purchase Domain - <?php echo esc_js($price); ?>';
                        return;
                    }

                    console.log('✅ All validations passed');

                    // Add domain-specific parameters for Namecheap API
                    domainRegistrationData.domainName = domainData.domain;
                    domainRegistrationData.years = 1;
                    domainRegistrationData.addFreeWhoisguard = true;
                    domainRegistrationData.wgEnabled = true;
                    domainRegistrationData.command = "namecheap.domains.create";

                    console.log('📝 Final Registration Data:', domainRegistrationData);

                    try {
                        console.log('💾 STEP 9C: Storing Registration Data in Session');
                        // Store domain registration data for after payment
                        sessionStorage.setItem('domainRegistrationData', JSON.stringify(domainRegistrationData));
                        console.log('✅ Registration data stored in session');

                        console.log('📡 STEP 9D: Storing Registration Data on Server');
                        // Also send to server to store in session
                        await fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: new URLSearchParams({
                                action: 'store_domain_registration_data',
                                registration_data: JSON.stringify(domainRegistrationData),
                                security: '<?php echo wp_create_nonce('store_domain_registration_data_nonce'); ?>'
                            })
                        });
                        console.log('✅ Registration data stored on server');

                        console.log('💳 STEP 10: Confirming Payment with Stripe');
                        const returnUrl = '<?php echo esc_js(site_url('/customer/create_domain_payment/?domain=' . urlencode($domain) . '&price=' . urlencode($price) . '&premium=' . ($is_premium ? 'true' : 'false'))); ?>';
                        console.log('🔄 Return URL:', returnUrl);

                        const {error} = await stripe.confirmPayment({
                            elements,
                            confirmParams: {
                                return_url: returnUrl,
                                payment_method_data: {
                                    billing_details: {
                                        name: `${domainRegistrationData.FirstName} ${domainRegistrationData.LastName}`,
                                        email: domainRegistrationData.EmailAddress,
                                        phone: domainRegistrationData.Phone,
                                        address: {
                                            line1: domainRegistrationData.Address1,
                                            city: domainRegistrationData.City,
                                            state: domainRegistrationData.Province,
                                            postal_code: domainRegistrationData.PostalCode,
                                            country: domainRegistrationData.Country
                                        }
                                    }
                                }
                            }
                        });

                        if (error) {
                            console.log('❌ STEP 10: Payment Failed');
                            console.log('🚨 Payment Error:', error);
                            showError(error.message);
                            submitButton.disabled = false;
                            submitButton.textContent = 'Purchase Domain - <?php echo esc_js($price); ?>';
                        } else {
                            console.log('✅ STEP 10: Payment Confirmed - Redirecting...');
                        }
                        // If no error, user will be redirected to return_url

                    } catch (error) {
                        showError('An unexpected error occurred. Please try again.');
                        submitButton.disabled = false;
                        submitButton.textContent = 'Purchase Domain - <?php echo esc_js($price); ?>';
                    }
                });
            } catch (error) {
                document.getElementById('payment-loading').style.display = 'none';
                showError('Could not initialize payment. Please try again. Error: ' + error.message);
            }
        }

        // Show error function
        function showError(message) {
            const errorElement = document.getElementById('error-message');
            errorElement.textContent = message;
            errorElement.style.display = 'block';
            errorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }

    } catch (error) {
        document.getElementById('error-message').textContent = 'Payment configuration error: ' + error.message;
        document.getElementById('error-message').style.display = 'block';
        document.getElementById('payment-loading').style.display = 'none';
    }
});
</script>

<?php include get_stylesheet_directory() . '/customer/footer-customer.php'; ?>
