<?php
/**
 * Template Name: Templates page
 *
 * @package ipt_home
 */

get_header();
?>
<main class="w-full bg-white">


    <!-- Templates -->
    <section
        class="flex flex-col items-center px-[16px] py-[48px] md:p-[80px] ">
        <h1 class="text-secondary-main text-center md:text-44 text-32 font-bold">
            Templates Library
        </h1>

        <!-- Search box -->
        <div class="flex justify-center mt-[32px] md:mt-[80px]">
            <form id="search-form" class="md:w-[886px] items-center shadow-[0px_0px_24px_-4px_rgba(0,0,0,0.07)] bg-white flex flex-col md:flex-row gap-5 text-base  p-[20px] rounded-[8px]" id="search-form">
                <div class="flex  items-center text-neutral-light font-normal w-full md:w-[630px] my-auto rounded-[8px]">
                <div class="!bg-color-disabled-2 h-[48px] flex w-full items-center gap-[8px] px-4 rounded-[8px]">
                    <img
                    src="https://cdn.builder.io/api/v1/image/assets/4f22a764650b4745a83d9de62f28fa3f/122d4a6b1e4e8fb08f0091ab133ed7e594cd60fd"
                    alt="Search"
                    class="aspect-[1] object-contain w-6 shrink-0 my-auto"
                    />
                    <input
                    type="text"
                    placeholder="Search..."
                    class="flex-1 !bg-transparent !shadow-none !border-none !outline-none text-neutral-light placeholder:text-neutral-light w-full"
                    id="search-template"
                    />
                </div>
                </div>
                <button
                type="submit" id="search-template-button"
                class="flex h-[48px] items-center justify-center text-black font-semibold w-full md:w-[196px] my-auto rounded-lg !bg-brand-main hover:bg-brand-main active:!bg-brand-main px-5 py-3 hover:opacity-90 transition-opacity"
                >
                Search
                </button>
            </form>
        </div>
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center w-full mt-[25px] gap-4">
            <!-- Category Filter Pills -->
            <div id="filter-categories" class="flex-1">
                <div class="flex flex-wrap gap-1" id="industry-pills-container">
                    <!-- All Categories Button -->
                    <button
                        class="category-pill active px-3 py-1 rounded-full text-xs font-medium transition-all duration-200 bg-brand-main text-white hover:bg-brand-main"
                        data-id="0"
                        onclick="filterByCategory(0)"
                    >
                        All
                    </button>

                    <!-- Industry pills will be loaded here from API -->
                </div>
            </div>

            <!-- Sort Section -->
            <div class="flex items-center gap-2 flex-shrink-0">
                <span class="text-sm text-gray-600">Sort by</span>
                <div class="relative">
                    <button
                        class="flex items-center gap-2 px-3 py-1 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        onclick="toggleDropdown('sort-by-dropdown')"
                        id="sort-button"
                    >
                        <span id="sort-text">Most popular</span>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div id="sort-by-dropdown" class="absolute right-0 mt-1 w-40 bg-white border border-gray-200 rounded-md shadow-lg hidden z-50">
                        <ul class="py-1">
                            <li>
                                <button
                                    class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 focus:outline-none"
                                    onclick="selectSort('created_at:ASC', 'Most popular')"
                                >
                                    Most popular
                                </button>
                            </li>
                            <li>
                                <button
                                    class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 focus:outline-none"
                                    onclick="selectSort('created_at:DESC', 'Newest')"
                                >
                                    Newest
                                </button>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 w-full gap-[20px] mt-[16px]" id="template-list">


        </div>
        <!-- Pagination -->
        <div class="flex items-center md:justify-center md:gap-[16px] mt-[32px] md:mt-[40px] w-full" id="pagination">
            <!-- <a href="#" class="flex items-center justify-center text-14 gap-[4px] px-4 h-8  font-semibold text-color-disabled hover:!text-color-disabled">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                <span class="inline">Prev</span>
            </a>

            <a href="#" class="flex items-center justify-center w-[40px] h-[40px] bg-brand-main text-14 text-neutral-strong hover:bg-brand-main hover:!text-neutral-strong font-semibold rounded-full">1</a>
            <a href="#" class="flex items-center justify-center w-[40px] h-[40px] text-14 text-neutral-strong hover:bg-brand-main hover:!text-neutral-strong focus:!text-neutral-strong active:!text-neutral-strong rounded-full  font-semibold">2</a>
            <a href="#" class="hidden md:flex items-center justify-center w-[40px] h-[40px] text-14 text-neutral-strong hover:bg-brand-main hover:!text-neutral-strong focus:!text-neutral-strong active:!text-neutral-strong rounded-full  font-semibold">3</a>
            <span class="flex items-center justify-center w-[40px] h-[40px] text-neutral-strong">...</span>
            <a href="#" class="hidden md:flex items-center justify-center w-[40px] h-[40px] text-14 text-neutral-strong hover:bg-brand-main hover:!text-neutral-strong focus:!text-neutral-strong active:!text-neutral-strong  rounded-full  font-semibold">8</a>
            <a href="#" class="hidden md:flex items-center justify-center w-[40px] h-[40px] text-14 text-neutral-strong hover:bg-brand-main hover:!text-neutral-strong focus:!text-neutral-strong active:!text-neutral-strong rounded-full  font-semibold">9</a>
            <a href="#" class="flex items-center justify-center w-[40px] h-[40px] text-14 text-neutral-strong hover:bg-brand-main hover:!text-neutral-strong focus:!text-neutral-strong active:!text-neutral-strong rounded-full  font-semibold">10</a>

            <a href="#" class="flex items-center justify-center text-14 gap-[4px] px-4 h-8 font-semibold text-brand-main hover:text-brand-main">
                <span>Next</span>
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </a> -->
        </div>
    </section>
    <script>
        // Sort selection function
        function selectSort(sortValue, sortText) {
            // Update button text
            document.getElementById('sort-text').textContent = sortText;

            // Hide dropdown
            document.getElementById('sort-by-dropdown').classList.add('hidden');

            // Get current filters
            const currentKeyword = document.getElementById('search-template').value;
            const currentIndustry = getCurrentIndustry();

            // Call fetchTemplates with new sort
            fetchTemplates(currentKeyword, currentIndustry, sortValue, 1);
        }

        // Helper function to get current sort
        function getCurrentSort() {
            const sortText = document.getElementById('sort-text').textContent;
            return sortText === 'Newest' ? 'created_at:ASC' : 'created_at:DESC';
        }

        // Helper function to get current industry
        function getCurrentIndustry() {
            const activePill = document.querySelector('.category-pill.active');
            return activePill ? activePill.getAttribute('data-id') : 0;
        }

        // Category filtering function (global scope)
        function filterByCategory(industry) {
            // Update active pill styling
            document.querySelectorAll('.category-pill').forEach(pill => {
                pill.classList.remove('active', 'bg-brand-main', 'text-white');
                pill.classList.add('bg-gray-100', 'text-gray-700');
            });

            // Set active pill
            const activePill = document.querySelector(`[data-id="${industry}"]`);
            if (activePill) {
                activePill.classList.add('active', 'bg-brand-main', 'text-white');
                activePill.classList.remove('bg-gray-100', 'text-gray-700');
            }

            // Use existing fetchTemplates function with industry filter
            const currentKeyword = document.getElementById('search-template').value;
            const currentSort = getCurrentSort();

            // Call existing fetchTemplates function
            fetchTemplates(currentKeyword, industry, currentSort, 1);
        }

        // Load industries from API and create pills (global scope)
        function loadIndustryPills() {
                const industry_filters = [`status_id:=(2);`];

                const queryIndustry = `
                   query Industry_list($filters: [String!]) {
                        industry_list(body: { filters: $filters }) {
                            id
                            name
                            status_id
                        }
                    }
                `;

                jQuery.ajax({
                    url: iptHomeAjax.ajax_url,
                    type: 'POST',
                    dataType: 'json',
                    data: {
                        action: 'ipt_home_graphql',
                        query: queryIndustry,
                        variables: JSON.stringify({ filters: industry_filters })
                    },
                    success: function(response) {
                        if (response.data && response.data.industry_list) {
                            const container = document.getElementById('industry-pills-container');

                            // Add industry pills after the "All" button
                            response.data.industry_list.forEach(industry => {
                                const pill = document.createElement('button');
                                pill.className = 'category-pill px-3 py-1 rounded-full text-xs font-medium transition-all duration-200 bg-gray-100 text-gray-700 hover:bg-brand-main active:bg-brand-main focus:bg-brand-main hover:text-gray-900';
                                pill.setAttribute('data-category', industry.name);
                                pill.setAttribute('data-id', industry.id);
                                pill.setAttribute('onclick', `filterByCategory(${industry.id})`);
                                pill.textContent = industry.name;

                                container.appendChild(pill);
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                    }
                });
            }

        // Global variables
        let debounceTimeout;

        // fetchTemplates function (global scope)
        function fetchTemplates(keyword = "", industry_selected = 0, sort_by = "created_at:ASC", page = 1) {
                const filters = ["status_id:=(2)", "is_kit:=(0)"];

                // Add industry filter only if industry_selected is not 0
                if (industry_selected != 0) {
                    filters.push(`industries.id:[](${industry_selected})`);
                }

                const search = keyword ? keyword : "";

                const query_2 = `
                query Template_list($filters: [String!], $sort: String!, $search: String!, $page: Int) {
                    template_list(body: { sort: $sort, filters: $filters, search: $search, page: $page, limit: 12 }) {
                        totalCount
                        totalPages
                        currentPage
                        data {
                            id
                            name
                            desc
                            image_id
                            image {
                                id
                                file_name
                                file_url
                            }
                            industries {
                                id
                                name
                            }
                            info
                        }
                    }
                }`;

                jQuery.ajax({
                    url: iptHomeAjax.ajax_url,
                    type: 'POST',
                    dataType: 'json',
                    data: {
                        action: 'ipt_home_graphql',
                        query: query_2,
                        variables: JSON.stringify({ filters: filters, sort: sort_by, search: search })
                    },
                    success: function(response) {
                        if(response.data && response.data.template_list) {
                            renderTemplates(response.data.template_list.data, industry_selected);
                            renderPagination(response.data.template_list.totalPages, response.data.template_list.currentPage);
                        }
                    }
                });
            }

            // Render pagination based on API data
            function renderPagination(totalPages, currentPage) {
                if (totalPages <= 1) {
                    $('#pagination').hide();
                    return;
                }
                
                $('#pagination').show();
                let paginationHtml = '';
                
                // Previous button
                const prevDisabled = currentPage <= 1;
                paginationHtml += `
                    <a href="#" class="flex items-center justify-center text-14 gap-[4px] px-4 h-8 font-semibold ${prevDisabled ? 'text-color-disabled hover:!text-color-disabled' : 'text-brand-main hover:text-brand-main'}" 
                       ${prevDisabled ? '' : 'data-page="' + (currentPage - 1) + '"'}>
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                        <span class="inline">Prev</span>
                    </a>
                `;
                
                // Calculate which page numbers to show
                let startPage = Math.max(1, currentPage - 2);
                let endPage = Math.min(totalPages, startPage + 4);
                
                if (endPage - startPage < 4) {
                    startPage = Math.max(1, endPage - 4);
                }
                
                // First page
                if (startPage > 1) {
                    paginationHtml += `
                        <a href="#" data-page="1" class="flex items-center justify-center w-[40px] h-[40px] text-14 text-neutral-strong hover:bg-brand-main hover:!text-neutral-strong rounded-full font-semibold">1</a>
                    `;
                    
                    if (startPage > 2) {
                        paginationHtml += `<span class="flex items-center justify-center w-[40px] h-[40px] text-neutral-strong">...</span>`;
                    }
                }
                
                // Page numbers
                for (let i = startPage; i <= endPage; i++) {
                    const isActive = i === currentPage;
                    paginationHtml += `
                        <a href="#" data-page="${i}" class="flex items-center justify-center w-[40px] h-[40px] ${isActive ? 'bg-brand-main' : ''} text-14 text-neutral-strong hover:bg-brand-main hover:!text-neutral-strong focus:!text-neutral-strong active:!text-neutral-strong rounded-full font-semibold">
                            ${i}
                        </a>
                    `;
                }
                
                // Last page
                if (endPage < totalPages) {
                    if (endPage < totalPages - 1) {
                        paginationHtml += `<span class="flex items-center justify-center w-[40px] h-[40px] text-neutral-strong">...</span>`;
                    }
                    
                    paginationHtml += `
                        <a href="#" data-page="${totalPages}" class="flex items-center justify-center w-[40px] h-[40px] text-14 text-neutral-strong hover:bg-brand-main hover:!text-neutral-strong focus:!text-neutral-strong active:!text-neutral-strong rounded-full font-semibold">
                            ${totalPages}
                        </a>
                    `;
                }
                
                // Next button
                const nextDisabled = currentPage >= totalPages;
                paginationHtml += `
                    <a href="#" class="flex items-center justify-center text-14 gap-[4px] px-4 h-8 font-semibold ${nextDisabled ? 'text-color-disabled hover:!text-color-disabled' : 'text-brand-main hover:text-brand-main'}"
                       ${nextDisabled ? '' : 'data-page="' + (currentPage + 1) + '"'}>
                        <span>Next</span>
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                `;
                
                $('#pagination').html(paginationHtml);
                
                // Add click event for pagination
                $('#pagination a[data-page]').on('click', function(e) {
                    e.preventDefault();
                    const page = $(this).data('page');
                    const keyword = $('#search-template').val();
                    const industry = $('button:contains("Online store")').find('span').first().text();
                    const sort = $('button:contains("Most popular")').find('span').first().text();
                    
                    let sort_by = "created_at:DESC";
                    if (sort === "Most popular") {
                        sort_by = "name:ASC";
                    }
                    
                    // Add page to filters
                    fetchTemplates(keyword, industry ? industry : 0, sort_by, page);
                });
            }

            function renderTemplates(list, industry_selected = "") {
                let html = '';
                list.forEach(function(item) {
                    // let industry_name = item.industries[0] ? item.industries[0].name : "";
                    // if(industry_selected.length > 0) {
                    //     if(industry_selected !== industry_name) {
                    //         return;
                    //     }
                    // }
                    html += `
                         <article class="flex flex-col border bg-white rounded-[8px] overflow-hidden border-solid border-fifth-main h-full">
                            <div class="relative w-full h-[200px]">
                                <img src="${item.image ? item.image.file_url : 'https://cdn.builder.io/api/v1/image/assets/TEMP/11ce7b98d60f888e40ee582f5d48055fe0e8fdca'}"
                                    alt="${item.name}" class="w-full h-full object-cover" />
                                <div class="flex h-[200px] justify-between items-center absolute px-3 py-0 top-0 inset-x-0">
                                    <div class="cursor-pointer">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                                            class="chevron-left">
                                            <path
                                                d="M10.757 12.0001L15.707 16.9501C15.8025 17.0423 15.8787 17.1526 15.9311 17.2747C15.9835 17.3967 16.0111 17.5279 16.0123 17.6607C16.0134 17.7934 15.9881 17.9251 15.9378 18.048C15.8875 18.1709 15.8133 18.2826 15.7194 18.3765C15.6255 18.4703 15.5139 18.5446 15.391 18.5949C15.2681 18.6452 15.1364 18.6705 15.0036 18.6693C14.8708 18.6682 14.7396 18.6406 14.6176 18.5882C14.4956 18.5358 14.3852 18.4596 14.293 18.3641L8.636 12.7071C8.44853 12.5195 8.34322 12.2652 8.34322 12.0001C8.34322 11.7349 8.44853 11.4806 8.636 11.2931L14.293 5.63606C14.4816 5.4539 14.7342 5.35311 14.9964 5.35538C15.2586 5.35766 15.5094 5.46283 15.6948 5.64824C15.8802 5.83365 15.9854 6.08446 15.9877 6.34666C15.99 6.60885 15.8892 6.86146 15.707 7.05006L10.757 12.0001Z"
                                                fill="white"></path>
                                        </svg>
                                    </div>
                                    <div class="cursor-pointer">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                                            class="chevron-right">
                                            <path
                                                d="M13.314 12.071L8.36399 7.12098C8.18184 6.93238 8.08104 6.67978 8.08332 6.41758C8.0856 6.15538 8.19077 5.90457 8.37618 5.71916C8.56158 5.53375 8.8124 5.42859 9.07459 5.42631C9.33679 5.42403 9.58939 5.52482 9.77799 5.70698L15.435 11.364C15.6225 11.5515 15.7278 11.8058 15.7278 12.071C15.7278 12.3361 15.6225 12.5905 15.435 12.778L9.77799 18.435C9.58939 18.6171 9.33679 18.7179 9.07459 18.7157C8.8124 18.7134 8.56158 18.6082 8.37618 18.4228C8.19077 18.2374 8.0856 17.9866 8.08332 17.7244C8.08104 17.4622 8.18184 17.2096 8.36399 17.021L13.314 12.071Z"
                                                fill="white"></path>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            <div class="flex flex-col items-start p-[16px] gap-[4px] self-stretch flex-1">
                                <div class="text-secondary-main text-16 font-normal">
                                    ${item.industries && item.industries.length > 0 ? item.industries.map(industry => industry.name).join(', ') : ''}
                                </div>
                                <h3 class="text-secondary-main text-18 font-bold">
                                    ${item.name}
                                </h3>
                                <div class="text-neutral-medium text-16 font-normal pt-[16px]">
                                    ${item.desc}
                                </div>
                            </div>
                            <div class="flex items-start p-[16px] self-stretch p-4">
                                <button onclick="window.open('${item.info.domain ? (item.info.domain.startsWith('http') ? item.info.domain : 'https://' + item.info.domain) : ''}', '_blank')"
                                    class="border-[1px] border-solid !border-fourth-main w-full text-neutral-strong font-semibold cursor-pointer bg-transparent hover:!bg-brand-orange hover:!text-white py-[12px] rounded-[8px] text-16">
                                    Preview
                                </button>
                            </div>
                        </article>
                    `;
                });
                jQuery('#template-list').html(html);
            }

        jQuery(document).ready(function($) {
            // Load industry pills on page load
            loadIndustryPills();

            fetchTemplates("");

            $('#search-template-button').on('click', function(e) {
                e.preventDefault();
                const keyword = $('#search-template').val();
                fetchTemplates(keyword);
            });

            $('#search-form').on('submit', function(e) {
                e.preventDefault();
                const keyword = $('#search-template').val();
                fetchTemplates(keyword);
            });

            

            /* Industry list */
            // const industry_filters = [`status_id:=2`];
            const industry_filters = ["status_id:=(2)"];

            const queryIndustry = `
               query Industry_list($filters: [String!]) {
                    industry_list(body: { filters: $filters }) {
                        id
                        name
                        status_id
                    }
                }
            `;

            $.ajax({
                url: iptHomeAjax.ajax_url,
                type: 'POST',
                dataType: 'json',
                data: {
                    action: 'ipt_home_graphql',
                    query: queryIndustry,
                    variables: JSON.stringify({ filters: industry_filters })
                },
                success: function(response) {
                    if(response.data && response.data.industry_list) {      
                        renderIndustries(response.data.industry_list);
                    } else {
                        // No data
                    }
                },
                error: function(xhr, status, error) {
                }
            });

            // Hàm render ra HTML
            function renderIndustries(list) {
                let html = '';  
                list.forEach(function(item) {
                    if (item.status_id == 2) {  
                        html += `
                            <li data-industry-id="${item.id}" class=" px-4 py-2 hover:bg-neutral-light-7 cursor-pointer text-16 text-neutral-strong font-medium">${item.name}</li>
                        `;      
                    }
                });
                $('#industry-list').html(html);
            }   

            // Xử lý khi click vào item trong dropdown
            // document.getElementById('filter-by-dropdown').addEventListener('click', function(e) {
            //     if (e.target && e.target.nodeName === "LI") {
            //         const selectedText = e.target.textContent;
            //         const button = e.target.closest('.relative').querySelector('button span');
            //         button.textContent = selectedText;
            //         e.target.closest('.absolute').classList.add('hidden');
            //         // render template
            //         fetchTemplates("", selectedText);
            //     }
            // });
            // document.querySelectorAll('#sort-by-dropdown li').forEach(item => {
            //     item.addEventListener('click', function() {
            //         const selectedText = this.textContent;
            //         const button = this.closest('.relative').querySelector('button span');
            //         button.textContent = selectedText;
            //         this.closest('.absolute').classList.add('hidden');

            //         // render template
            //         let sort_by = "created_at:DESC";
            //         if (selectedText === "Most popular") {
            //             sort_by = "name:ASC";
            //         } 
            //         fetchTemplates("", "", sort_by);
            //     });
            // });
            
        });
    </script>    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Đóng dropdown khi click ra ngoài
            document.addEventListener('click', function(event) {
                const dropdowns = document.querySelectorAll('.absolute');
                dropdowns.forEach(dropdown => {
                    if (!event.target.closest('.relative')) {
                        dropdown.classList.add('hidden');
                    }
                });
            });
        });

        function toggleDropdown(dropdownId) {
            // Đóng tất cả các dropdown khác
            const allDropdowns = document.querySelectorAll('.absolute');
            allDropdowns.forEach(dropdown => {
                if (dropdown.id !== dropdownId) {
                    dropdown.classList.add('hidden');
                }
            });

            // Toggle dropdown hiện tại
            const dropdown = document.getElementById(dropdownId);
            dropdown.classList.toggle('hidden');

            // Ngăn sự kiện click lan ra ngoài
            event.stopPropagation();
        }

        
    </script>

</main><!-- #page -->
<?php
get_footer(); 
