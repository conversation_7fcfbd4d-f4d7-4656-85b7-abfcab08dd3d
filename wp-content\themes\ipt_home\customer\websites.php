<?php include get_stylesheet_directory() . '/customer/header-customer.php'; ?>
<?php include get_stylesheet_directory() . '/customer/header-dashboard.php'; ?>
<style>
/* Custom dropdown arrow styling */
.select-custom {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 16px center;
    background-repeat: no-repeat;
    background-size: 20px 20px;
}

.select-custom:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%232BA990' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
}
</style>
<div class="flex min-h-screen bg-neutral-light">
   <?php include get_stylesheet_directory() . '/customer/sidebar.php'; ?>
   <div class="flex-1 flex flex-col min-h-screen">
      <?php include get_stylesheet_directory() . '/customer/breadcrumb-websites.php'; ?>

       <!-- Website -->
      <main class="flex-1 p-4 bg-db-dark-2">
         <div class="bg-white rounded-lg shadow p-4">
            <div class="flex items-center justify-between mb-4">
               <!-- left side -->
               <div class="font-semibold text-lg">Manange Website</div>
               <!-- right side -->
               <div class="flex items-center gap-4">
                  <!-- Filter Dropdown -->
                  <!-- <div class="relative">
                     <select id="industry" class="select-custom appearance-none bg-white border border-bd-border hover:border-bd-border active:border-bd-border focus:border-bd-border !rounded-full px-6 py-2 pr-10 text-sm text-db-dark font-medium focus:outline-none focus:border-brand-main min-w-[160px]">
                        <option>Select a filter</option>
                     </select>
                  </div> -->
                  <!-- Search Input -->
                  <!-- <div class="relative">
                     <input
                        type="text"
                        placeholder=""
                        id="searchInput"
                        class="bg-white border border-[#E6E8EC] !rounded-full !pl-10 pr-4 text-base text-db-dark font-medium placeholder:text-neutral-light focus:outline-none focus:border-brand-main min-w-[180px]"
                     />
                     <span class="absolute left-4 top-1/2 -translate-y-1/2 text-brand-main">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                           <circle cx="11" cy="11" r="8" stroke-width="2"/>
                           <path d="M21 21l-4.35-4.35" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                     </span>
                     <button type="button" id="clearButton" class="p-3 absolute right-3 top-1/2 -translate-y-1/2 bg-brand-color-01 rounded-full !w-[18px] !h-[18px] flex items-center justify-center hover:bg-brand-color-01 hidden">
                        <i class="fa-solid fa-close text-neutral-medium text-lg"></i>
                     </button>
                  </div> -->
               </div>
              
            </div>
            <!-- Danh sách website sẽ render ở đây -->
            <!-- <div class="text-neutral-medium text-center py-12">No websites found.</div> -->

            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6" id="websites-list">
               <!-- Websites will be rendered here by JavaScript -->
               <div class="col-span-full text-neutral-medium text-center py-12">Loading website...</div>
            </div>
         </div>

         

         <!-- activity -->
         <!-- Nhớ đã import Font Awesome vào dự án của bạn! -->
         <!-- <div class="bg-white rounded-2xl p-8 w-full mt-6">
            <div class="text-[#0A1734] text-[24px] font-semibold mb-6">Activity Feed / Tips Panel</div>
            <div class="flex gap-6">
               <div class="bg-[#F9F9F9] rounded-lg flex items-center px-8 py-6 min-w-[340px]">
                  <i class="fas fa-clock text-[#0A1734] text-2xl mr-4"></i>
                  <span class="text-[18px] text-[#202020]">Site C was created 10 minutes ago</span>
               </div>
               <div class="bg-[#F9F9F9] rounded-lg flex items-center px-8 py-6 min-w-[340px]">
                  <i class="fas fa-clock text-[#0A1734] text-2xl mr-4"></i>
                  <span class="text-[18px] text-[#202020]">New template designs available!</span>
               </div>
            </div>
         </div> -->
      </main>
      <script>
         // Global variable to store current subscription plan
         let currentSubscriptionPlan = '1'; // Default to Trial

         // Function to clean plan name (remove "Plan" suffix and extra spaces)
         function cleanPlanName(planName) {
            if (!planName) return '1';

            // Remove "Plan" suffix and trim spaces
            let cleaned = planName.replace(/\s*plan\s*$/i, '').trim();

            // If empty after cleaning, default to Trial
            return cleaned || '1';
         }

         function changePlanIdToName(planTypeId = 1) {
            switch (planTypeId) {
               case '1':
                  return 'Trial';
                  break;
               case '2':
                  return 'Basic';
                  break;
               case '3':
                  return 'Premium';
                  break;
               default:
                  return 'Trial';
                  break;
            }
         }

         jQuery(document).ready(function($) {
            // Get current user's customer ID
            const customerId = <?php echo json_encode(ipt_home_get_customer_id_from_user(get_current_user_id())); ?>;

            if (!customerId) {
               $('#websites-list').html('<div class="col-span-full text-neutral-medium text-center py-12">Please login to view your websites.</div>');
               return;
            }

            // Fetch subscription plan first, then websites
            fetchSubscriptionPlan().then(() => {
               fetchWebsites();
            }).catch(() => {
               // If subscription fetch fails, still load websites with default plan
               currentSubscriptionPlan = '1';
               fetchWebsites();
            });

            // Function to fetch subscription plan
            function fetchSubscriptionPlan() {
               return new Promise((resolve, reject) => {
                  $.ajax({
                     url: iptHomeAjax.ajax_url,
                     type: 'POST',
                     dataType: 'json',
                     data: {
                        action: 'ipt_get_latest_stripe_order',
                        security: '<?php echo wp_create_nonce('ipt-subscription-nonce'); ?>'
                     },
                     success: function(response) {
                        if (response.success && response.data) {
                           // Store subscription plan globally for use in edit URLs (clean the plan name)
                           // currentSubscriptionPlan = cleanPlanName(response.data.subscription_type_id || 'Trial');
                           currentSubscriptionPlan = response.data.subscription_type_id
                           resolve(currentSubscriptionPlan);
                        } else {
                           currentSubscriptionPlan = '1';
                           resolve(currentSubscriptionPlan);
                        }
                     },
                     error: function(xhr, status, error) {
                        currentSubscriptionPlan = '1';
                        reject(error);
                     }
                  });
               });
            }

            // Function to get authentication token (reused from create_website.php)
            async function getAuthToken() {
               const mutation = `
                  mutation Auth_login($body: LoginInputDto!) {
                     auth_login(body: $body) {
                        id
                        token {
                           access_token
                        }
                     }
                  }
               `;

               const variables = {
                  body: {
                     email: "<EMAIL>",
                     password: "Abc!23456789",
                     role_id: 1
                  }
               };

               return new Promise((resolve, reject) => {
                  jQuery.ajax({
                     url: iptHomeAjax.ajax_url,
                     type: 'POST',
                     dataType: 'json',
                     data: {
                        action: 'ipt_home_graphql',
                        query: mutation,
                        variables: JSON.stringify(variables)
                     },
                     success: function(response) {
                        if (response.errors && response.errors.length > 0) {
                           reject(new Error(response.errors[0].message || 'Authentication failed'));
                        } else if (response.data && response.data.auth_login && response.data.auth_login.token) {
                           resolve(response.data.auth_login.token.access_token);
                        } else {
                           reject(new Error('Invalid authentication response'));
                        }
                     },
                     error: function(xhr, status, error) {
                        reject(new Error('Failed to authenticate'));
                     }
                  });
               });
            }

            // Main function to fetch websites
            async function fetchWebsites(keyword = "") {
               try {

                  // Get authentication token
                  const authToken = await getAuthToken();

                  // Prepare GraphQL query
                  const query = `
                     query Websites_list($input: BasePaginationInput!) {
                        websites_list(input: $input) {
                           totalCount
                           totalPages
                           currentPage
                           data {
                              id
                              created_by
                              updated_by
                              created_at
                              updated_at
                              customer_id
                              template_id
                              domain
                              custom_domain
                              info
                              status_id
                           }
                        }
                     }
                  `;

                  // Prepare variables with customer filter
                  const variables = {
                     input: {
                        filters: `customer_id:=(${customerId})`
                     }
                  };


                  // Make direct API call with Bearer token
                  $.ajax({
                     url: '<?php echo GRAPHQL_API_URL; ?>',
                     type: 'POST',
                     dataType: 'json',
                     headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + authToken
                     },
                     data: JSON.stringify({
                        query: query,
                        variables: variables
                     }),
                     success: function(response) {

                        if (response.errors && response.errors.length > 0) {
                           $('#websites-list').html('<div class="col-span-full text-neutral-medium text-center py-12">Error loading websites: ' + response.errors[0].message + '</div>');
                        } else if (response.data && response.data.websites_list) {
                           renderWebsites(response.data.websites_list.data);
                        } else {
                           $('#websites-list').html('<div class="col-span-full text-neutral-medium text-center py-12">Unexpected response from server.</div>');
                        }
                     },
                     error: function(xhr, status, error) {
                        $('#websites-list').html('<div class="col-span-full text-neutral-medium text-center py-12">Failed to load websites. Please try again.</div>');
                     }
                  });

               } catch (error) {
                  $('#websites-list').html('<div class="col-span-full text-neutral-medium text-center py-12">Error: ' + error.message + '</div>');
               }
            }

            // Function to render websites
            function renderWebsites(websites) {
               let html = '';

               // Check if no websites found
               if(!websites || websites.length === 0) {
                  html = '<div class="col-span-full text-neutral-medium text-center py-12">No websites found. <a href="/customer/create_website" class="text-brand-main hover:underline">Create your first website</a></div>';
                  $('#websites-list').html(html);
                  return;
               }
               let count = 0;
               websites.forEach(function(website) {
                  if(count > 0) {
                     return;
                  }
                  // Parse info JSON
                  let websiteInfo = {};
                  try {
                     websiteInfo = JSON.parse(website.info || '{}');
                  } catch (e) {
                     websiteInfo = {};
                  }

                  let websiteId = website.id;

                  // Format date as dd/mm/YYYY
                  const createdDate = new Date(website.created_at);
                  const updatedDate = `${createdDate.getDate().toString().padStart(2, '0')}/${(createdDate.getMonth() + 1).toString().padStart(2, '0')}/${createdDate.getFullYear()}`;

                  // Get website name from info or use domain
                  const websiteName = websiteInfo.company_name || website.domain || 'Unnamed Website';

                  // Get domains for different purposes
                  const viewDomain = websiteInfo.custom_domain || website.domain; // For view button - use info.domain directly
                  const editDomain = websiteInfo.custom_domain || website.domain; // For edit button - use info.domain with bypass token
                  const displayDomain = websiteInfo.custom_domain || website.domain; // For display

                  // Prepare URLs
                  const viewUrl = viewDomain ? (viewDomain.startsWith('http') ? viewDomain : 'https://' + viewDomain) : '#';
                  const editUrl = editDomain ?
                     (editDomain.startsWith('http') ? editDomain : 'https://' + editDomain) +
                     '?bypass_token=ASiOTwM9W9ToZ0zOSDsMF1OQYUmRumys&plan=' + changePlanIdToName(currentSubscriptionPlan) + '&website_id=' + websiteId + '&customer_id=' + customerId
                     : '#';

                  html += `
                     <div class="relative rounded-2xl overflow-hidden shadow bg-gray-100 min-h-[220px] flex flex-col justify-end" data-website-id="${website.id}">
                        <!-- Background image placeholder -->
                        <div class="absolute inset-0 bg-gradient-to-br from-brand-main to-brand-main/70"></div>

                        <!-- Delete button 
                        <button class="absolute top-4 left-4 w-9 h-9 flex items-center justify-center bg-white rounded-lg shadow !p-2 hover:bg-red-50" onclick="deleteWebsite(${website.id})">
                          <i class="fa fa-trash text-red-600 text-lg" aria-hidden="true"></i>
                         </button>-->

                        <!-- Status toggle -->
                        
                        <!-- <div class="absolute top-4 right-4">
                           <label class="inline-flex items-center cursor-pointer relative">
                              <input type="checkbox" class="sr-only peer" ${website.status_id === 2 ? 'checked' : ''} onchange="toggleWebsiteStatus(${website.id}, this.checked)">
                              <div class="w-10 h-6 bg-gray-200 rounded-full peer peer-checked:bg-green-400 transition"></div>
                              <div class="absolute left-1 top-1 bg-white w-4 h-4 rounded-full shadow peer-checked:translate-x-4 transition"></div>
                           </label>
                        </div> -->

                        <!-- Action buttons -->
                        <div class="relative z-10 flex justify-center gap-2 mb-4">
                           <button class="bg-white active:bg-white text-db-dark font-medium rounded-lg px-2 py-2 shadow hover:bg-gray-50" onclick="window.open('${editUrl}', '_blank')">
                              Edit
                           </button>
                           <button class="bg-brand-main text-white font-medium rounded-lg px-2 py-2 shadow hover:bg-brand-main/80" onclick="window.open('${viewUrl}', '_blank')">
                              View
                           </button>
                        </div>

                        <!-- Website info -->
                        <div class="relative z-10 px-4 pb-4">
                           <div class="text-white text-14 font-bold">${websiteName}</div>
                           <div class="text-white text-14 opacity-80">${updatedDate}</div>
                        </div>
                     </div>
                  `;
                  count++;
               });
               //   <div class="text-white text-12 opacity-60 mt-1">${displayDomain}</div>

               if(websites.length == 0) {
                  // Add "Create New Website" card
                  html += `
                     <div class="flex items-center justify-center rounded-2xl border-2 border-dashed border-brand-main min-h-[220px] bg-white cursor-pointer hover:bg-brand-main/10 transition" onclick="window.location.href='/customer/create_website'">
                        <button class="flex flex-col items-center justify-center !bg-transparent !shadow-none">
                           <span class="text-brand-main text-[100px] mb-2 text-semibold">+</span>
                        </button>
                     </div>
                  `;
               }

               $('#websites-list').html(html);
            }
            // Website action functions
            window.deleteWebsite = function(websiteId) {
               if (confirm('Are you sure you want to delete this website? This action cannot be undone.')) {
                  // TODO: Implement delete functionality
                  // alert('Delete functionality will be implemented later.');
               }
            };

            window.toggleWebsiteStatus = function(websiteId, isActive) {
               // TODO: Implement status toggle functionality
               // alert('Status toggle functionality will be implemented later.');
            };



            // Search functionality
            $('#searchInput').on('input', function() {
               const keyword = $(this).val().trim();
               // TODO: Implement search functionality
            });

            // Search input clear button functionality
            const searchInput = document.getElementById('searchInput');
            const clearButton = document.getElementById('clearButton');

            if (searchInput && clearButton) {
               // Show/hide clear button based on input content
               function toggleClearButton() {
                  if (searchInput.value.trim() !== '') {
                     clearButton.classList.remove('hidden');
                  } else {
                     clearButton.classList.add('hidden');
                  }
               }

               // Listen for input changes
               searchInput.addEventListener('input', toggleClearButton);
               searchInput.addEventListener('keyup', toggleClearButton);
               searchInput.addEventListener('paste', function() {
                  // Delay to allow paste content to be processed
                  setTimeout(toggleClearButton, 10);
               });

               // Clear button click handler
               clearButton.addEventListener('click', function() {
                  searchInput.value = '';
                  searchInput.focus();
                  toggleClearButton();

                  // Trigger search reset
                  fetchWebsites('');
               });

               // Initial check on page load
               toggleClearButton();
            }

         });
</script>


   </div>
</div>

<script>
function toggleMenu(id) {
  const menu = document.getElementById(id);
  menu.classList.toggle('hidden');
}
</script>

<?php include get_stylesheet_directory() . '/customer/footer-customer.php'; ?>