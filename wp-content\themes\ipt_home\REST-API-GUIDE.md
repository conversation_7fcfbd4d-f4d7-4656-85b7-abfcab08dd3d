# Domain Pricing REST API Guide

This document explains how to use the WordPress REST API endpoints for domain pricing management.

## Base URL
All endpoints are prefixed with: `yoursite.com/wp-json/domain-pricing/v1/`

## Authentication
The API supports multiple authentication methods for different use cases:

### Method 1: API Key (Recommended for Server-to-Server)
```bash
# Using X-API-Key header
curl -H "X-API-Key: your-api-key-here" "https://yoursite.com/wp-json/domain-pricing/v1/stats"

# Using Authorization Bearer header
curl -H "Authorization: Bearer your-api-key-here" "https://yoursite.com/wp-json/domain-pricing/v1/stats"
```

### Method 2: Secret Key (Simple)
```bash
# Using URL parameter
curl "https://yoursite.com/wp-json/domain-pricing/v1/stats?secret=your-secret-key"

# Using X-Secret header
curl -H "X-Secret: your-secret-key" "https://yoursite.com/wp-json/domain-pricing/v1/stats"
```

### Method 3: WordPress Login (Browser/Admin Interface)
```javascript
headers: {
    'X-WP-Nonce': 'your-wp-nonce-here'
}
```

**Get your API keys from:** WordPress Admin → Tools → Domain Pricing → API Authentication section

## Endpoints

### 1. Import Pricing from File
**Endpoint:** `POST /import-from-file`

**Description:** Import pricing data from the `namecheap_price.md` file

**Example:**
```javascript
fetch('/wp-json/domain-pricing/v1/import-from-file', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-WP-Nonce': wpNonce
    }
})
.then(response => response.json())
.then(data => console.log(data));
```

**Response:**
```json
{
    "success": true,
    "message": "Successfully imported pricing for 250 TLDs",
    "data": {
        "processed_count": 250,
        "error_count": 5,
        "updated_count": 250,
        "stats": {
            "total_tlds": 250,
            "average_price": 12.45,
            "newest_update": "2024-01-15 10:30:00"
        },
        "sample_pricing": {
            "com": 9.98,
            "net": 12.98,
            "org": 11.98
        }
    }
}
```

### 2. Update Pricing from API
**Endpoint:** `POST /update-from-api`

**Description:** Fetch fresh pricing data from Namecheap API

**Example:**
```javascript
fetch('/wp-json/domain-pricing/v1/update-from-api', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-WP-Nonce': wpNonce
    }
})
```

### 3. Get Pricing Statistics
**Endpoint:** `GET /stats`

**Description:** Get current pricing database statistics

**Example:**
```javascript
fetch('/wp-json/domain-pricing/v1/stats', {
    headers: {
        'X-WP-Nonce': wpNonce
    }
})
```

**Response:**
```json
{
    "success": true,
    "data": {
        "total_tlds": 250,
        "average_price": 12.45,
        "newest_update": "2024-01-15 10:30:00",
        "oldest_update": "2024-01-15 10:30:00"
    }
}
```

### 4. Get All Pricing Data
**Endpoint:** `GET /pricing-data`

**Description:** Get all pricing data from database

**Example:**
```javascript
fetch('/wp-json/domain-pricing/v1/pricing-data', {
    headers: {
        'X-WP-Nonce': wpNonce
    }
})
```

### 5. Production Setup
**Endpoint:** `POST /production-setup`

**Description:** Complete production setup (database + import + cron scheduling)

**Parameters:**
- `force_update` (optional): Set to "true" to overwrite existing data

**Example:**
```javascript
fetch('/wp-json/domain-pricing/v1/production-setup', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-WP-Nonce': wpNonce
    },
    body: JSON.stringify({
        force_update: "true"
    })
})
```

## Usage Examples

### WordPress Admin Interface
The easiest way to use these APIs is through the WordPress admin:
1. Go to **Tools** → **Domain Pricing**
2. Use the REST API buttons in the interface
3. View real-time responses

### JavaScript/AJAX
```javascript
// Get WordPress nonce (add this to your page)
const wpNonce = document.querySelector('meta[name="wp-nonce"]').content;

// Import pricing from file
async function importPricingFromFile() {
    try {
        const response = await fetch('/wp-json/domain-pricing/v1/import-from-file', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-WP-Nonce': wpNonce
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            console.log('Success:', data.message);
            console.log('Updated TLDs:', data.data.updated_count);
        } else {
            console.error('Error:', data.message);
        }
    } catch (error) {
        console.error('Network error:', error);
    }
}
```

### cURL Examples

**Using API Key (Recommended):**
```bash
# Import from file
curl -X POST "https://yoursite.com/wp-json/domain-pricing/v1/import-from-file" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key-here"

# Get statistics
curl "https://yoursite.com/wp-json/domain-pricing/v1/stats" \
  -H "X-API-Key: your-api-key-here"

# Production setup
curl -X POST "https://yoursite.com/wp-json/domain-pricing/v1/production-setup" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key-here" \
  -d '{"force_update": "true"}'
```

**Using Secret Key (Simple):**
```bash
# Import from file
curl -X POST "https://yoursite.com/wp-json/domain-pricing/v1/import-from-file?secret=your-secret-key" \
  -H "Content-Type: application/json"

# Get statistics
curl "https://yoursite.com/wp-json/domain-pricing/v1/stats?secret=your-secret-key"

# Production setup
curl -X POST "https://yoursite.com/wp-json/domain-pricing/v1/production-setup?secret=your-secret-key" \
  -H "Content-Type: application/json" \
  -d '{"force_update": "true"}'
```

**Using Bearer Token:**
```bash
# Production setup
curl -X POST "https://yoursite.com/wp-json/domain-pricing/v1/production-setup" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key-here" \
  -d '{"force_update": "true"}'
```

## Error Handling

All endpoints return consistent error responses:

```json
{
    "code": "error_code",
    "message": "Human readable error message",
    "data": {
        "status": 400
    }
}
```

Common error codes:
- `file_not_found`: Pricing file not found
- `json_decode_error`: Invalid JSON in pricing file
- `no_register_data`: No register category found in pricing data
- `api_fetch_failed`: Failed to fetch from Namecheap API
- `setup_failed`: Production setup failed

## Production Deployment

For production deployment:

1. **Upload the pricing file** to `/wp-content/themes/ipt_home/md/namecheap_price.md`

2. **Run production setup:**
```javascript
fetch('/wp-json/domain-pricing/v1/production-setup', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-WP-Nonce': wpNonce
    }
})
```

3. **Verify setup:**
```javascript
fetch('/wp-json/domain-pricing/v1/stats')
```

This will automatically:
- Create the database table
- Import pricing data
- Schedule automatic updates
- Configure WordPress Cron

## Security Notes

- All endpoints require admin privileges (`manage_options` capability)
- Always use WordPress nonces for CSRF protection
- The pricing file should be placed in a secure location
- Consider removing the pricing file after import for security

## Troubleshooting

If you encounter issues:

1. **Check WordPress error logs**
2. **Verify file permissions** on the pricing file
3. **Test API connectivity** to Namecheap
4. **Check WordPress Cron status**
5. **Use the admin interface** for detailed error messages
