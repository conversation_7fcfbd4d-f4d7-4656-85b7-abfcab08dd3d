jQuery(document).ready(function($) {
  // L<PERSON>y tham chiếu đến các phần tử
  const menuButton = $('[data-collapse-toggle="navbar-header"]');
  const hamburgerIcon = menuButton.find('.hamburger-icon');
  const closeIcon = menuButton.find('.close-icon');
  
  // Toggle menu khi click
  menuButton.on('click', function() {
    // Chuyển đổi giữa các icon
    hamburgerIcon.toggleClass('hidden');
    closeIcon.toggleClass('hidden');
    
    // Các xử lý khác với menu
    $('#navbar-header').toggleClass('hidden');
    
    // Cập nhật trạng thái aria-expanded
    const isExpanded = $(this).attr('aria-expanded') === 'true';
    $(this).attr('aria-expanded', !isExpanded);
  });
  
  // Đóng menu khi click vào các liên kết (thê<PERSON> nếu cần)
  $('#navbar-header a').on('click', function() {
    if (window.innerWidth < 768) {
      $('#navbar-header').addClass('hidden');
      menuButton.attr('aria-expanded', 'false');
      closeIcon.addClass('hidden');
      hamburgerIcon.removeClass('hidden');
    }
  });
  
  // Đóng menu khi click bên ngoài (tuỳ chọn)
  $(document).on('click', function(event) {
    if (window.innerWidth < 768) {
      if (!$(event.target).closest('#navbar-header').length && 
          !$(event.target).closest('[data-collapse-toggle="navbar-header"]').length) {
        $('#navbar-header').addClass('hidden');
        menuButton.attr('aria-expanded', 'false');
        closeIcon.addClass('hidden');
        hamburgerIcon.removeClass('hidden');
      }
    }
  });
});
