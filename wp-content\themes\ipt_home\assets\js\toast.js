/**
 * Toast notification system - Simple version
 */

// Function to display toast message
function showToast(message, type = 'success', duration = 3000) {
    // Remove existing toasts if any
    const existingToasts = document.querySelectorAll('.ipt-toast');
    existingToasts.forEach(toast => {
        toast.remove();
    });
    
    // Create new toast
    const toast = document.createElement('div');
    toast.className = 'ipt-toast';
    toast.textContent = message;
    
    // Set basic styles
    toast.style.position = 'fixed';
    toast.style.bottom = '1rem';
    toast.style.right = '1rem';
    toast.style.padding = '0.75rem 1.5rem';
    toast.style.borderRadius = '0.25rem';
    toast.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
    toast.style.minWidth = '250px';
    toast.style.maxWidth = '350px';
    toast.style.zIndex = '9999';
    toast.style.transition = 'all 0.3s ease';
    toast.style.opacity = '0';
    toast.style.transform = 'translateY(1rem)';
    
    // Set colors based on type
    if (type === 'success') {
        toast.style.backgroundColor = '#10B981';
        toast.style.color = 'white';
    } else if (type === 'error') {
        toast.style.backgroundColor = '#EF4444';
        toast.style.color = 'white';
    } else if (type === 'info') {
        toast.style.backgroundColor = '#3B82F6';
        toast.style.color = 'white';
    }
    
    // Add to body
    document.body.appendChild(toast);
    
    // Show toast
    setTimeout(() => {
        toast.style.opacity = '1';
        toast.style.transform = 'translateY(0)';
        
        // Auto hide after duration
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateY(1rem)';
            
            // Remove toast after animation ends
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, duration);
    }, 10);
}

// Utility functions for common toast types
function showSuccessToast(message, duration = 3000) {
    showToast(message, 'success', duration);
}

function showErrorToast(message, duration = 3000) {
    showToast(message, 'error', duration);
}

function showInfoToast(message, duration = 3000) {
    showToast(message, 'info', duration);
}

// Assign functions to window to use globally
window.showToast = showToast;
window.showSuccessToast = showSuccessToast;
window.showErrorToast = showErrorToast;
window.showInfoToast = showInfoToast;
