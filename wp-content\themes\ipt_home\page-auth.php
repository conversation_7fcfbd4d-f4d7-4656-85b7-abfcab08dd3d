<?php
/*
Template Name: Auth Page
*/

get_header(); ?>

<div class="auth-page">
    <h1>Login or Register</h1>
    <button id="login-facebook">Login with Facebook</button>
    <button id="login-google">Login with Google</button>
</div>

<script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app.js"></script>
<script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth.js"></script>
<script>
  // Your web app's Firebase configuration
  var firebaseConfig = {
    apiKey: "<?php echo FIREBASE_API_KEY; ?>",
    authDomain: "<?php echo FIREBASE_PROJECT_ID; ?>.firebaseapp.com",
    projectId: "<?php echo FIREBASE_PROJECT_ID; ?>",
    storageBucket: "<?php echo FIREBASE_PROJECT_ID; ?>.appspot.com",
    messagingSenderId: "<?php echo FIREBASE_MESSAGING_SENDER_ID; ?>",
    appId: "<?php echo FIREBASE_APP_ID; ?>"
  };
  // Initialize Firebase
  firebase.initializeApp(firebaseConfig);

  // Facebook login
  document.getElementById('login-facebook').addEventListener('click', function() {
    var provider = new firebase.auth.FacebookAuthProvider();
    firebase.auth().signInWithPopup(provider).then(function(result) {
      var user = result.user;
    }).catch(function(error) {
    });
  });

  // Google login
  document.getElementById('login-google').addEventListener('click', function() {
    var provider = new firebase.auth.GoogleAuthProvider();
    firebase.auth().signInWithPopup(provider).then(function(result) {
      var user = result.user;
    }).catch(function(error) {
    });
  });
</script>

<?php get_footer(); ?>