<?php
// Ensure domain tables exist before loading page
ipt_ensure_domain_tables_exist();

// Get website_id from URL parameter (optional for domain management)
$website_id = isset($_GET['website_id']) ? intval($_GET['website_id']) : 0;

include get_stylesheet_directory() . '/customer/header-customer.php';
include get_stylesheet_directory() . '/customer/header-dashboard.php';
?>

<div class="flex min-h-screen bg-neutral-light">
   <?php include get_stylesheet_directory() . '/customer/sidebar.php'; ?>
   <div class="flex-1 flex flex-col min-h-screen">

      <!-- Domain Management -->
      <main class="flex-1 p-4 bg-db-dark-2">
         <div class="bg-white rounded-lg shadow p-6">
           
             <nav class="" aria-label="Breadcrumb">
               <ol class="flex items-center space-x text-sm list-none ml-0 mb-1">
                  <li>
                     <a href="<?php echo site_url('/customer/subscription'); ?>" class="font-normal hover:text-db-text text-db-text  text-14 ">Settings</a>
                  </li>
                  <li>
                     <span class="text-neutral-400 text-12">
                        <svg width="22" height="30" viewBox="0 0 22 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                           <path fill-rule="evenodd" clip-rule="evenodd" d="M8 8.7L12.6 15L8 21.3L8.8 22L14 15L8.8 8L8 8.7Z" fill="#000624" fill-opacity="0.3"/>
                        </svg>
                     </span>
                  </li>
                  <li>
                     <a href="#" class="font-normal hover:text-db-text text-db-text text-14">Manage Domain</a>
                  </li>
                  
               </ol>
            </nav>

            <!-- Page Title -->
            <h1 class="text-2xl font-semibold text-gray-900 mb-8">Manage Domain</h1>

            <!-- Current Domain Status Section -->
            <div class="mb-12">
               <!-- <div class="flex items-center justify-between mb-6">
                  <h2 class="text-lg font-semibold text-gray-900"> Domain's current status</h2>
                  <div class="flex items-center gap-4">
                     <select class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option>All</option>
                        <option value="selected">Selected</option>
                        <option value="pending">Payment Pending</option>
                        <option value="failed">Payment Failed</option>
                        <option value="completed">Active</option>
                     </select>
                  </div>
               </div> -->
               <!-- Domain Table -->
               <div class="bg-green-50 rounded-lg overflow-hidden">
                  <!-- Table Header -->
                  <div class="grid grid-cols-12 gap-4 p-4 bg-green-100 border-b border-green-200">
                     <div class="col-span-3 font-medium text-gray-700">Domain</div>
                     <div class="col-span-2 font-medium text-gray-700">SSL Status</div>

                     <div class="col-span-2 font-medium text-gray-700">Expired Date</div>
                     <div class="col-span-2 font-medium text-gray-700">DNS Records</div>
                     <div class="col-span-4 font-medium text-gray-700"></div>
                  </div>
                  <!-- Domain Rows -->
                  <div id="domain-list">
                     <!-- Domain Rows - Dynamic Content -->
                     <div id="domains-container">
                        <!-- Domains will be loaded here via JavaScript -->
                     </div>
                     <!-- Loading State -->
                     <div id="domains-loading" class="text-center py-8">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500 mx-auto"></div>
                        <p class="mt-2 text-gray-600">Loading domains...</p>
                     </div>

                     <!-- Empty State -->
                     <div id="domains-empty" class="text-center py-12 hidden">
                        <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                           <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                        </svg>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No domains found</h3>
                        <p class="text-gray-500 mb-4">You haven't selected or purchased any domains yet.</p>
                        <a href="<?php echo site_url('/customer/search_domain/'); ?>" class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                           <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                           </svg>
                           Search Domains
                        </a>
                     </div>
                  </div>
               </div>
            </div>

         
         </div>
      </main>
   </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load domains on page load
    loadDomains();

    // Handle domain connection form (only if it exists)
    const connectForm = document.getElementById('domain-connect-form');
    const connectBtn = document.getElementById('connect-btn');

    if (connectForm && connectBtn) {
        connectForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const domainInput = document.getElementById('domain_connect');
            const domain = domainInput.value.trim();

            if (!domain) {
                alert('Please enter a domain name');
                return;
            }

            // Show loading state
            connectBtn.disabled = true;
            connectBtn.textContent = 'Connecting...';

            // Simulate domain connection (replace with actual API call)
            setTimeout(() => {
                alert(`Domain ${domain} connection initiated!`);

                // Reset button state
                connectBtn.disabled = false;
                connectBtn.textContent = "Let's go";

                // Clear input
                domainInput.value = '';
            }, 2000);
        });
    }

    function loadDomains() {
        const container = document.getElementById('domains-container');
        const loading = document.getElementById('domains-loading');
        const empty = document.getElementById('domains-empty');

        // Show loading state
        loading.classList.remove('hidden');
        empty.classList.add('hidden');
        container.innerHTML = '';

        // Fetch real data from server
        fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'get_user_domains',
                security: '<?php echo wp_create_nonce('get_user_domains_nonce'); ?>'
            })
        })
        .then(response => response.json())
        .then(data => {
            loading.classList.add('hidden');

            if (data.success && data.data && data.data.length > 0) {
                displayDomains(data.data);
            } else {
                empty.classList.remove('hidden');
            }
        })
        .catch(error => {
            loading.classList.add('hidden');
            empty.classList.remove('hidden');
        });
    }

    function displayDomains(domains) {
        const container = document.getElementById('domains-container');

        domains.forEach(domain => {
            const domainRow = createDomainRow(domain);
            container.appendChild(domainRow);
        });
    }

    function createDomainRow(domain) {
        const row = document.createElement('div');
        row.className = 'grid grid-cols-12 gap-4 p-4 border-b border-green-200 hover:bg-green-75';

        const statusInfo = getStatusInfo(domain.status);
        const autoRenewalToggle = domain.status === 'completed' ?
            `<label class="inline-flex items-center">
                <input type="checkbox" class="sr-only peer" ${domain.auto_renewal ? 'checked' : ''} onchange="toggleAutoRenewal(${domain.id}, this.checked)">
                <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>` :
            '<span class="text-gray-400 text-sm">N/A</span>';

        const expiryDate = domain.expires_at ?
            new Date(domain.expires_at).toLocaleDateString('en-GB') :
            '-';

        const actionButtons = getActionButtons(domain);

        row.innerHTML = `
            
            <div class="col-span-3">
                <div class="flex items-center">
                    <span class="text-gray-900">${domain.domain_name}</span>
                    ${domain.is_premium ? '<span class="ml-2 px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded-full">Premium</span>' : ''}
                    ${domain.domain_type === 'custom' ? '<span class="ml-2 px-2 py-1 bg-purple-100 text-purple-800 text-xs font-medium rounded-full">Custom Domain</span>' : ''}
                </div>
                <div class="text-sm text-gray-500 mt-1">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusInfo.class}">
                       ${statusInfo.text}
                    </span>
                </div>
            </div>
            <div class="col-span-2">
                ${autoRenewalToggle}
            </div>
            <div class="col-span-2 text-gray-600">${expiryDate}</div>
            <div class="col-span-4 flex gap-2">
                ${actionButtons}
            </div>
        `;

        return row;
    }

    function getStatusInfo(status) {
        const statusMap = {
            'selected': {
                text: 'Selected',
                class: 'bg-blue-100 text-blue-800',
                icon: '🛒'
            },
            'pending': {
                text: 'Payment Pending',
                class: 'bg-yellow-100 text-yellow-800',
                icon: '⏳'
            },
            'failed': {
                text: 'Payment Failed',
                class: 'bg-red-100 text-red-800',
                icon: '❌'
            },
            'completed': {
                text: 'Active',
                class: 'bg-green-100 text-green-800',
                icon: '✅'
            }
        };

        return statusMap[status] || statusMap['selected'];
    }

    function getActionButtons(domain) {
        // Check if this is a custom domain (connected domain)
        if (domain.domain_type === 'custom') {
            return `
                <button onclick="manageDNS('${domain.domain_name}')" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors text-sm">
                    DNS
                </button>
               <!-- <button onclick="viewDomain('${domain.domain_name}')" class="px-4 py-2 bg-green-400 text-white rounded-md hover:bg-green-500 transition-colors text-sm">
                    View
                </button>-->
            `;
        }

        // Regular purchased domain actions based on status
        switch (domain.status) {
            case 'selected':
                return `
                    <!--<button onclick="completePurchase('${domain.domain_name}', '${domain.price}', ${domain.is_premium})" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm">
                        Complete Purchase
                    </button>
                    <button onclick="removeDomain(${domain.id})" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm">
                        Remove
                    </button>-->
                `;
            case 'pending':
                return `
                    <!--<button onclick="checkPaymentStatus(${domain.id})" class="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors text-sm">
                        Check Status
                    </button>
                    <button onclick="cancelPayment(${domain.id})" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm">
                        Cancel
                    </button>-->
                `;
            case 'failed':
                return `
                  <!--  <button onclick="retryPayment('${domain.domain_name}', '${domain.price}', ${domain.is_premium})" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors text-sm">
                        Retry Payment
                    </button>
                    <button onclick="removeDomain(${domain.id})" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm">
                        Remove
                    </button>-->
                `;
            case 'completed':
                return `
                    <button onclick="manageDNS('${domain.domain_name}')" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors text-sm">
                        DNS
                    </button>
                   <!-- <button onclick="viewDomain('${domain.domain_name}')" class="px-4 py-2 bg-green-400 text-white rounded-md hover:bg-green-500 transition-colors text-sm">
                        View
                    </button>
                    <button onclick="editDomain(${domain.id})" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm">
                        Edit
                    </button>-->
                `;
            default:
                return '';
        }
    }

    // Global functions for domain actions
    window.completePurchase = function(domain, price, isPremium) {
        const websiteId = <?php echo json_encode($website_id); ?>;
        let url = `<?php echo site_url('/customer/create_domain_payment/'); ?>?domain=${encodeURIComponent(domain)}&price=${encodeURIComponent(price)}&premium=${isPremium}`;

        // Add website_id if available
        if (websiteId && websiteId > 0) {
            url += `&website_id=${websiteId}`;
        }

        window.location.href = url;
    };

    window.retryPayment = function(domain, price, isPremium) {
        window.completePurchase(domain, price, isPremium);
    };

    window.removeDomain = function(domainId) {
        if (confirm('Are you sure you want to remove this domain from your list?')) {
            // TODO: Implement remove domain functionality
        }
    };

    window.checkPaymentStatus = function(domainId) {
        // TODO: Implement payment status check
    };

    window.cancelPayment = function(domainId) {
        if (confirm('Are you sure you want to cancel this payment?')) {
            // TODO: Implement cancel payment functionality
        }
    };

    window.viewDomain = function(domain) {
        console.log('viewDomain called with domain:', domain);
        window.open('https://' + domain, '_blank');
    };

    window.editDomain = function(domainId) {
        // TODO: Implement domain editing functionality
    };

    window.toggleAutoRenewal = function(domainId, enabled) {
        // TODO: Implement auto-renewal toggle
    };

    window.manageDNS = function(domain) {
        console.log('manageDNS called with domain:', domain);
        const url = `<?php echo site_url('/customer/dns/'); ?>?domain=${encodeURIComponent(domain)}`;
        console.log('Redirecting to:', url);
        window.location.href = url;
    };

    // Debug: Log that functions are loaded
    console.log('Domain management functions loaded:', {
        manageDNS: typeof window.manageDNS,
        viewDomain: typeof window.viewDomain,
        completePurchase: typeof window.completePurchase
    });
});
</script>

<?php include get_stylesheet_directory() . '/customer/footer-customer.php'; ?>
