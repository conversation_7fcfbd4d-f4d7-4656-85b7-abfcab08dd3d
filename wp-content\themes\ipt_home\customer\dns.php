<?php
// Ensure domain tables exist before loading page
ipt_ensure_domain_tables_exist();

// Get domain from URL parameter
$domain = isset($_GET['domain']) ? sanitize_text_field($_GET['domain']) : '';

// Redirect back to domain management if no domain provided
if (empty($domain)) {
    wp_safe_redirect(site_url('/customer/domain_management/'));
    exit;
}

// Note: Domain ownership check removed for testing purposes
// This allows testing DNS functionality with any domain
$user_id = get_current_user_id();

include get_stylesheet_directory() . '/customer/header-customer.php';
include get_stylesheet_directory() . '/customer/header-dashboard.php';
?>

<div class="flex min-h-screen bg-neutral-light">
   <?php include get_stylesheet_directory() . '/customer/sidebar.php'; ?>
   <div class="flex-1 flex flex-col min-h-screen">

      <!-- DNS Management -->
      <main class="flex-1 p-4 bg-db-dark-2">
         <div class="bg-white rounded-lg shadow p-6">
           
             <nav class="" aria-label="Breadcrumb">
               <ol class="flex items-center space-x text-sm list-none ml-0 mb-1">
                  <li>
                     <a href="<?php echo site_url('/customer/subscription'); ?>" class="font-normal hover:text-db-text text-db-text  text-14 ">Settings</a>
                  </li>
                  <li>
                     <span class="text-neutral-400 text-12">
                        <svg width="22" height="30" viewBox="0 0 22 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                           <path fill-rule="evenodd" clip-rule="evenodd" d="M8 8.7L12.6 15L8 21.3L8.8 22L14 15L8.8 8L8 8.7Z" fill="#000624" fill-opacity="0.3"/>
                        </svg>
                     </span>
                  </li>
                  <li>
                     <a href="<?php echo site_url('/customer/domain_management/'); ?>" class="font-normal hover:text-db-text text-db-text text-14">Domain Management</a>
                  </li>
                  <li>
                     <span class="text-neutral-400 text-12">
                        <svg width="22" height="30" viewBox="0 0 22 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                           <path fill-rule="evenodd" clip-rule="evenodd" d="M8 8.7L12.6 15L8 21.3L8.8 22L14 15L8.8 8L8 8.7Z" fill="#000624" fill-opacity="0.3"/>
                        </svg>
                     </span>
                  </li>
                  <li>
                     <a href="#" class="font-normal hover:text-db-text text-db-text text-14">DNS Management</a>
                  </li>
               </ol>
            </nav>

            <!-- Page Title -->
            <h1 class="text-2xl font-semibold text-gray-900 mb-2">DNS Management</h1>
            <p class="text-gray-600 mb-8">Manage DNS records for <strong><?php echo esc_html($domain); ?></strong></p>
            
            <!-- DNS Records Section -->
            <div class="max-w-6xl mx-auto">
               <!-- Info Banner -->
               <!-- <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                  <div class="flex items-start">
                     <svg class="w-5 h-5 text-blue-600 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                     </svg>
                     <div>
                        <h3 class="text-sm font-medium text-blue-900 mb-1">DNS Records Information</h3>
                        <p class="text-sm text-blue-800">Below are the current DNS records for your domain. These records are managed automatically and are read-only.</p>
                     </div>
                  </div>
               </div> -->

               <!-- Loading State -->
               <div id="loading-state" class="text-center py-8">
                  <div class="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-blue-600 bg-blue-50">
                     <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                     </svg>
                     Loading DNS records...
                  </div>
               </div>

               <!-- DNS Records Table -->
               <div id="dns-records-container" class="hidden">
                  <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
                     <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">DNS Records</h3>
                     </div>
                     
                     <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                           <thead class="bg-gray-50">
                              <tr>
                                 <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                 <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                 <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Content</th>
                                 <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">TTL</th>
                                 <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                              </tr>
                           </thead>
                           <tbody id="dns-records-tbody" class="bg-white divide-y divide-gray-200">
                              <!-- DNS records will be populated here -->
                           </tbody>
                        </table>
                     </div>
                  </div>
               </div>

               <!-- Error Message -->
               <div id="error-message" class="hidden bg-red-50 border border-red-200 rounded-lg p-4">
                  <div class="flex items-start">
                     <svg class="w-5 h-5 text-red-600 mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                     </svg>
                     <div>
                        <h3 class="text-sm font-medium text-red-800">Error Loading DNS Records</h3>
                        <p id="error-text" class="mt-1 text-sm text-red-700"></p>
                     </div>
                  </div>
               </div>

               <!-- Back Button -->
               <div class="mt-8 text-center">
                  <a href="<?php echo site_url('/customer/domain_management/'); ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                     <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                     </svg>
                     Back to Domain Management
                  </a>
               </div>
            </div>
         </div>
      </main>
   </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const domain = '<?php echo esc_js($domain); ?>';
    const loadingState = document.getElementById('loading-state');
    const dnsRecordsContainer = document.getElementById('dns-records-container');
    const errorMessage = document.getElementById('error-message');
    const dnsRecordsTbody = document.getElementById('dns-records-tbody');

    // Load DNS records when page loads
    loadDNSRecords();

    async function loadDNSRecords() {
        try {
            // Call GraphQL API to get DNS records
            const response = await fetch('https://api-weaveform.ip-tribe.com/graphql', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ASiOTwM9W9ToZ0zOSDsMF1OQYUmRumys'
                },
                body: JSON.stringify({
                    query: `query Webhooks_cloudflare_get_dns_records {
                        webhooks_cloudflare_get_dns_records(domain: "${domain}") {
                            id
                            type
                            name
                            content
                            ttl
                            priority
                        }
                    }`
                })
            });

            const data = await response.json();

            // Check for GraphQL errors first (these contain the actual API error messages)
            if (data.errors && data.errors.length > 0) {
                const errorMessage = data.errors[0].message || 'Unknown error occurred';
                const errorCode = data.errors[0].code || '';

                // Show the actual API error message
                let displayMessage = errorMessage;
                if (errorCode) {
                    displayMessage = `${errorMessage} (Code: ${errorCode})`;
                }

                console.error('GraphQL API Error:', data.errors[0]);
                showError(displayMessage);
                return;
            }

            // Check for HTTP errors after GraphQL errors
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const dnsRecords = data.data?.webhooks_cloudflare_get_dns_records;

            if (dnsRecords && dnsRecords.length > 0) {
                displayDNSRecords(dnsRecords);
            } else {
                showError('No DNS records found for this domain');
            }

        } catch (error) {
            console.error('Error loading DNS records:', error);
            showError('Failed to load DNS records: ' + error.message);
        } finally {
            loadingState.classList.add('hidden');
        }
    }

    function displayDNSRecords(records) {
        dnsRecordsTbody.innerHTML = '';
        
        records.forEach(record => {
            const row = document.createElement('tr');
            row.className = 'hover:bg-gray-50';
            
            row.innerHTML = `
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(record.type)}">
                        ${record.type}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-mono">${record.name}</td>
                <td class="px-6 py-4 text-sm text-gray-900 font-mono break-all">${record.content}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${record.ttl}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${record.priority || '-'}</td>
            `;
            
            dnsRecordsTbody.appendChild(row);
        });
        
        dnsRecordsContainer.classList.remove('hidden');
    }

    function getTypeColor(type) {
        const colors = {
            'A': 'bg-blue-100 text-blue-800',
            'AAAA': 'bg-blue-100 text-blue-800',
            'CNAME': 'bg-green-100 text-green-800',
            'MX': 'bg-purple-100 text-purple-800',
            'TXT': 'bg-yellow-100 text-yellow-800',
            'NS': 'bg-gray-100 text-gray-800',
            'SRV': 'bg-indigo-100 text-indigo-800'
        };
        
        return colors[type] || 'bg-gray-100 text-gray-800';
    }

    function showError(message) {
        document.getElementById('error-text').textContent = message;
        errorMessage.classList.remove('hidden');
    }
});
</script>

<?php include get_stylesheet_directory() . '/customer/footer-customer.php'; ?>
