<?php
/**
 * Import Domain Pricing from Namecheap Price File
 * 
 * This script reads the namecheap_price.md file and imports pricing data into the database
 * Access via: yoursite.com/wp-content/themes/ipt_home/import-pricing-from-file.php
 */

// Load WordPress
require_once('../../../../wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

// Include the pricing system
require_once get_stylesheet_directory() . '/includes/domain-pricing-system.php';

echo "<h1>Import Domain Pricing from File</h1>";
echo "<p>Reading pricing data from namecheap_price.md...</p>";

// Read the markdown file
$file_path = get_stylesheet_directory() . '/md/namecheap_price.md';

if (!file_exists($file_path)) {
    die("<p style='color: red;'>Error: File not found at $file_path</p>");
}

$content = file_get_contents($file_path);

if (!$content) {
    die("<p style='color: red;'>Error: Could not read file content</p>");
}

echo "<p>✅ File loaded successfully. Size: " . number_format(strlen($content)) . " bytes</p>";

// Extract JSON from the markdown file
preg_match('/\{.*\}/s', $content, $matches);

if (empty($matches)) {
    die("<p style='color: red;'>Error: No JSON data found in file</p>");
}

$json_data = $matches[0];
$data = json_decode($json_data, true);

if (json_last_error() !== JSON_ERROR_NONE) {
    die("<p style='color: red;'>Error: JSON decode failed - " . json_last_error_msg() . "</p>");
}

echo "<p>✅ JSON data parsed successfully</p>";

// Navigate to the register category
$register_products = null;

if (isset($data['data']['name_cheap_index']['ApiResponse']['CommandResponse']['UserGetPricingResult']['ProductType']['ProductCategory'])) {
    $categories = $data['data']['name_cheap_index']['ApiResponse']['CommandResponse']['UserGetPricingResult']['ProductType']['ProductCategory'];
    
    foreach ($categories as $category) {
        if (isset($category['$']['Name']) && $category['$']['Name'] === 'register') {
            $register_products = $category['Product'];
            break;
        }
    }
}

if (!$register_products) {
    die("<p style='color: red;'>Error: Could not find register category in pricing data</p>");
}

echo "<p>✅ Found register category with " . count($register_products) . " products</p>";

// Create database table if it doesn't exist
echo "<p>Creating/checking database table...</p>";
create_domain_pricing_table();
echo "<p>✅ Database table ready</p>";

// Parse pricing data
$pricing_data = [];
$processed_count = 0;
$error_count = 0;

foreach ($register_products as $product) {
    try {
        $tld = $product['$']['Name'];
        
        // Look for 1-year pricing
        $prices = $product['Price'];
        if (!is_array($prices)) {
            $prices = [$prices];
        }
        
        $one_year_price = null;
        
        foreach ($prices as $price_info) {
            if (isset($price_info['$']['Duration']) && $price_info['$']['Duration'] == '1') {
                // Use YourPrice if available, otherwise use Price
                if (isset($price_info['$']['YourPrice']) && $price_info['$']['YourPrice'] > 0) {
                    $one_year_price = floatval($price_info['$']['YourPrice']);
                } else {
                    $one_year_price = floatval($price_info['$']['Price']);
                }
                break;
            }
        }
        
        if ($one_year_price !== null && $one_year_price > 0) {
            $pricing_data[$tld] = $one_year_price;
            $processed_count++;
        } else {
            $error_count++;
            echo "<p style='color: orange;'>⚠️ No valid 1-year price found for .$tld</p>";
        }
        
    } catch (Exception $e) {
        $error_count++;
        echo "<p style='color: red;'>❌ Error processing product: " . $e->getMessage() . "</p>";
    }
}

echo "<p>✅ Processed $processed_count TLDs successfully</p>";
if ($error_count > 0) {
    echo "<p style='color: orange;'>⚠️ $error_count TLDs had errors</p>";
}

// Import into database
if (!empty($pricing_data)) {
    echo "<p>Importing pricing data into database...</p>";
    
    $updated_count = update_domain_pricing_in_db($pricing_data);
    
    echo "<p>✅ <strong>Successfully imported pricing for $updated_count TLDs into database!</strong></p>";
    
    // Show statistics
    $stats = get_domain_pricing_stats();
    echo "<h2>Database Statistics:</h2>";
    echo "<ul>";
    echo "<li>Total TLDs in database: " . $stats['total_tlds'] . "</li>";
    echo "<li>Average price: $" . number_format($stats['average_price'], 2) . "</li>";
    echo "<li>Last updated: " . $stats['newest_update'] . "</li>";
    echo "</ul>";
    
    // Show sample pricing
    echo "<h2>Sample Pricing (first 20 TLDs):</h2>";
    echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
    echo "<tr><th>TLD</th><th>Price (1 Year)</th></tr>";
    
    $count = 0;
    foreach ($pricing_data as $tld => $price) {
        if ($count >= 20) break;
        echo "<tr><td>.$tld</td><td>$" . number_format($price, 2) . "</td></tr>";
        $count++;
    }
    echo "</table>";
    
    if (count($pricing_data) > 20) {
        echo "<p><em>... and " . (count($pricing_data) - 20) . " more TLDs</em></p>";
    }
    
} else {
    echo "<p style='color: red;'>❌ No pricing data to import</p>";
}

echo "<hr>";
echo "<h2>Next Steps:</h2>";
echo "<ul>";
echo "<li><a href='" . admin_url('tools.php?page=domain-pricing') . "'>View Domain Pricing Admin →</a></li>";
echo "<li><a href='" . home_url('/customer/search_domain.php') . "'>Test Domain Search →</a></li>";
echo "<li><strong>Delete this file after use for security</strong></li>";
echo "</ul>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
h1 { color: #333; border-bottom: 2px solid #0073aa; padding-bottom: 10px; }
h2 { color: #666; margin-top: 30px; }
p { margin: 10px 0; }
ul { margin: 10px 0 10px 20px; }
li { margin: 5px 0; }
table { margin: 10px 0; }
th { background: #f1f1f1; }
hr { margin: 30px 0; }
a { color: #0073aa; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
