<?php
/**
 * Template Name: Support page template
 *
 * @package ipt_home
 */

get_header();
?>
<main class="w-full">
    <!-- Support Header -->
    <h1 class="text-secondary-main text-center md:text-44 text-32 font-bold mt-[48px] mx-[16px] md:mx-auto md:mt-[80px]">
            Support
        </h1>
    <!-- Page content -->
    <section class="flex flex-col mx-auto max-w-[1064px] mt-[50px]">
        <div class="grid grid-cols-12 gap-[20px]">
            <!-- Trên mobile: 12 cột (full width), Trên desktop: 4 cột -->
            <div class="col-span-12 md:col-span-3">
                <div
                    class="flex flex-col items-start gap-0.5 border bg-[rgba(255,255,255,0.59)] p-1 rounded-lg border-solid border-[#E8E8E8] shadow-lg max-md:w-full max-sm:w-full max-sm:max-w-none max-sm:rounded">
                    <ul id="options-list" role="listbox" aria-label="Options" class="w-full list-none p-0 m-0">
                        <li class="option-item flex items-center gap-3 w-full bg-white p-[12px] border-b-[#EFEFEF] border-b border-solid cursor-pointer "
                            role="option" tabindex="0">
                            <div
                                class="self-stretch text-[#202020] text-sm font-normal leading-5 tracking-[-0.14px] gap-0.5 flex-1">
                                Home</div>
                        </li>
                        <li class="option-item flex items-center gap-3 w-full bg-white p-[12px] border-b-[#EFEFEF] border-b border-solid cursor-pointer "
                            role="option" tabindex="0">
                            <div
                                class="self-stretch text-[#202020] text-sm font-normal leading-5 tracking-[-0.14px] gap-0.5 flex-1">
                                About</div>
                        </li>
                        <li class="option-item flex items-center gap-3 w-full bg-white p-[12px] border-b-[#EFEFEF] border-b border-solid cursor-pointer "
                            role="option" tabindex="0">
                            <div
                                class="self-stretch text-[#202020] text-sm font-normal leading-5 tracking-[-0.14px] gap-0.5 flex-1">
                                Services</div>
                        </li>
                        <li class="option-item flex items-center gap-3 w-full bg-white p-[12px] border-b-[#EFEFEF] border-b border-solid cursor-pointer "
                            role="option" tabindex="0">
                            <div
                                class="self-stretch text-[#202020] text-sm font-normal leading-5 tracking-[-0.14px] gap-0.5 flex-1">
                                Products</div>
                        </li>
                        <li class="option-item flex items-center gap-3 w-full bg-white p-[12px] border-b-[#EFEFEF] border-b border-solid cursor-pointer "
                            role="option" tabindex="0">
                            <div
                                class="self-stretch text-[#202020] text-sm font-normal leading-5 tracking-[-0.14px] gap-0.5 flex-1">
                                Contact</div>
                        </li>
                        <li class="option-item flex items-center gap-3 w-full bg-white p-[12px] border-b-[#EFEFEF] cursor-pointer "
                            role="option" tabindex="0">
                            <div
                                class="self-stretch text-[#202020] text-sm font-normal leading-5 tracking-[-0.14px] gap-0.5 flex-1">
                                Support</div>
                        </li>
                    </ul>
                </div>
                <script>
                // Add event listeners to option items
                document.querySelectorAll('.option-item').forEach(item => {
                    // Click event
                    item.addEventListener('click', function() {
                        // Remove selected class from all items
                        document.querySelectorAll('.option-item').forEach(el => {
                            el.classList.remove('selected');
                            el.setAttribute('aria-selected', 'false');
                        });

                        // Add selected class to clicked item
                        this.classList.add('selected');
                        this.setAttribute('aria-selected', 'true');

                        // Log selected option
                    });

                    // Keyboard event
                    item.addEventListener('keydown', function(e) {
                        if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            this.click();
                        }
                    });
                });
                </script>
            </div>

            <div class="col-span-12 md:col-span-9">
                <div class="faq-container">
                    <!-- FAQ Item 1 -->
                    <div
                        class="faq-item flex flex-col border overflow-hidden bg-white rounded-lg border-solid border-[#E8E8E8] mb-[12px]">
                        <div class="faq-question flex flex-1 justify-between items-center cursor-pointer p-[16px] max-sm:p-3"
                            onclick="toggleFaq(this)">
                            <div
                                class="flex-1 text-base font-bold leading-6 tracking-[-0.16px] text-black max-sm:text-sm max-sm:leading-5">
                                Lorem ipsum dolor sit amet consectetur. Viverra risus vestibulum libero augue nisi.
                            </div>
                            <svg width="32" height="32" viewBox="0 0 32 32" fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                                class="chevron-icon transition-transform duration-200">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M7.05752 11.0572C7.57822 10.5365 8.42244 10.5365 8.94313 11.0572L16.0003 18.1144L23.0575 11.0572C23.5782 10.5365 24.4224 10.5365 24.9431 11.0572C25.4638 11.5779 25.4638 12.4221 24.9431 12.9428L16.9431 20.9428C16.4224 21.4635 15.5782 21.4635 15.0575 20.9428L7.05752 12.9428C6.53682 12.4221 6.53682 11.5779 7.05752 11.0572Z"
                                    fill="#8D8D8D" />
                            </svg>
                        </div>
                        <div
                            class="faq-answer hidden text-base font-normal leading-6 tracking-[-0.16px] text-[#8D8D8D] pt-0 pb-4 px-4 max-sm:text-sm max-sm:leading-5 max-sm:pt-0 max-sm:pb-3 max-sm:px-3">
                            Lorem ipsum dolor sit amet consectetur. Viverra risus vestibulum libero augue nisi.Lorem
                            ipsum dolor sit amet consectetur. Viverra risus vestibulum libero augue nisi.Lorem ipsum
                            dolor sit amet consectetur. Viverra risus vestibulum libero augue nisi.
                        </div>
                    </div>

                    <!-- FAQ Item 2 -->
                    <div
                        class="faq-item flex flex-col border overflow-hidden bg-white rounded-lg border-solid border-[#E8E8E8] mb-[12px]">
                        <div class="faq-question flex flex-1 justify-between items-center cursor-pointer p-[16px] max-sm:p-3"
                            onclick="toggleFaq(this)">
                            <div
                                class="flex-1 text-base font-bold leading-6 tracking-[-0.16px] text-black max-sm:text-sm max-sm:leading-5">
                                Lorem ipsum dolor sit amet consectetur. Viverra risus vestibulum libero augue nisi.
                            </div>
                            <svg width="32" height="32" viewBox="0 0 32 32" fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                                class="chevron-icon transition-transform duration-200">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M7.05752 11.0572C7.57822 10.5365 8.42244 10.5365 8.94313 11.0572L16.0003 18.1144L23.0575 11.0572C23.5782 10.5365 24.4224 10.5365 24.9431 11.0572C25.4638 11.5779 25.4638 12.4221 24.9431 12.9428L16.9431 20.9428C16.4224 21.4635 15.5782 21.4635 15.0575 20.9428L7.05752 12.9428C6.53682 12.4221 6.53682 11.5779 7.05752 11.0572Z"
                                    fill="#8D8D8D" />
                            </svg>
                        </div>
                        <div
                            class="faq-answer hidden text-base font-normal leading-6 tracking-[-0.16px] text-[#8D8D8D] pt-0 pb-4 px-4 max-sm:text-sm max-sm:leading-5 max-sm:pt-0 max-sm:pb-3 max-sm:px-3">
                            Lorem ipsum dolor sit amet consectetur. Viverra risus vestibulum libero augue nisi.Lorem
                            ipsum dolor sit amet consectetur. Viverra risus vestibulum libero augue nisi.Lorem ipsum
                            dolor sit amet consectetur. Viverra risus vestibulum libero augue nisi.
                        </div>
                    </div>

                    <!-- FAQ Item 3 -->
                    <div
                        class="faq-item flex flex-col border overflow-hidden bg-white rounded-lg border-solid border-[#E8E8E8] mb-[12px]">
                        <div class="faq-question flex flex-1 justify-between items-center cursor-pointer p-[16px] max-sm:p-3"
                            onclick="toggleFaq(this)">
                            <div
                                class="flex-1 text-base font-bold leading-6 tracking-[-0.16px] text-black max-sm:text-sm max-sm:leading-5">
                                Lorem ipsum dolor sit amet consectetur. Viverra risus vestibulum libero augue nisi.
                            </div>
                            <svg width="32" height="32" viewBox="0 0 32 32" fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                                class="chevron-icon transition-transform duration-200">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M7.05752 11.0572C7.57822 10.5365 8.42244 10.5365 8.94313 11.0572L16.0003 18.1144L23.0575 11.0572C23.5782 10.5365 24.4224 10.5365 24.9431 11.0572C25.4638 11.5779 25.4638 12.4221 24.9431 12.9428L16.9431 20.9428C16.4224 21.4635 15.5782 21.4635 15.0575 20.9428L7.05752 12.9428C6.53682 12.4221 6.53682 11.5779 7.05752 11.0572Z"
                                    fill="#8D8D8D" />
                            </svg>
                        </div>
                        <div
                            class="faq-answer hidden text-base font-normal leading-6 tracking-[-0.16px] text-[#8D8D8D] pt-0 pb-4 px-4 max-sm:text-sm max-sm:leading-5 max-sm:pt-0 max-sm:pb-3 max-sm:px-3">
                            Lorem ipsum dolor sit amet consectetur. Viverra risus vestibulum libero augue nisi.Lorem
                            ipsum dolor sit amet consectetur. Viverra risus vestibulum libero augue nisi.Lorem ipsum
                            dolor sit amet consectetur. Viverra risus vestibulum libero augue nisi.
                        </div>
                    </div>

                    <!-- FAQ Item 4 -->
                    <div
                        class="faq-item flex flex-col border overflow-hidden bg-white rounded-lg border-solid border-[#E8E8E8] mb-[12px]">
                        <div class="faq-question flex flex-1 justify-between items-center cursor-pointer p-[16px] max-sm:p-3"
                            onclick="toggleFaq(this)">
                            <div
                                class="flex-1 text-base font-bold leading-6 tracking-[-0.16px] text-black max-sm:text-sm max-sm:leading-5">
                                Lorem ipsum dolor sit amet consectetur. Viverra risus vestibulum libero augue nisi.
                            </div>
                            <svg width="32" height="32" viewBox="0 0 32 32" fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                                class="chevron-icon transition-transform duration-200">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M7.05752 11.0572C7.57822 10.5365 8.42244 10.5365 8.94313 11.0572L16.0003 18.1144L23.0575 11.0572C23.5782 10.5365 24.4224 10.5365 24.9431 11.0572C25.4638 11.5779 25.4638 12.4221 24.9431 12.9428L16.9431 20.9428C16.4224 21.4635 15.5782 21.4635 15.0575 20.9428L7.05752 12.9428C6.53682 12.4221 6.53682 11.5779 7.05752 11.0572Z"
                                    fill="#8D8D8D" />
                            </svg>
                        </div>
                        <div
                            class="faq-answer hidden text-base font-normal leading-6 tracking-[-0.16px] text-[#8D8D8D] pt-0 pb-4 px-4 max-sm:text-sm max-sm:leading-5 max-sm:pt-0 max-sm:pb-3 max-sm:px-3">
                            Lorem ipsum dolor sit amet consectetur. Viverra risus vestibulum libero augue nisi.Lorem
                            ipsum dolor sit amet consectetur. Viverra risus vestibulum libero augue nisi.Lorem ipsum
                            dolor sit amet consectetur. Viverra risus vestibulum libero augue nisi.
                        </div>
                    </div>

                    <!-- FAQ Item 5 -->
                    <div
                        class="faq-item flex flex-col border overflow-hidden bg-white rounded-lg border-solid border-[#E8E8E8] mb-[12px]">
                        <div class="faq-question flex flex-1 justify-between items-center cursor-pointer p-[16px] max-sm:p-3"
                            onclick="toggleFaq(this)">
                            <div
                                class="flex-1 text-base font-bold leading-6 tracking-[-0.16px] text-black max-sm:text-sm max-sm:leading-5">
                                Lorem ipsum dolor sit amet consectetur. Viverra risus vestibulum libero augue nisi.
                            </div>
                            <svg width="32" height="32" viewBox="0 0 32 32" fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                                class="chevron-icon transition-transform duration-200">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M7.05752 11.0572C7.57822 10.5365 8.42244 10.5365 8.94313 11.0572L16.0003 18.1144L23.0575 11.0572C23.5782 10.5365 24.4224 10.5365 24.9431 11.0572C25.4638 11.5779 25.4638 12.4221 24.9431 12.9428L16.9431 20.9428C16.4224 21.4635 15.5782 21.4635 15.0575 20.9428L7.05752 12.9428C6.53682 12.4221 6.53682 11.5779 7.05752 11.0572Z"
                                    fill="#8D8D8D" />
                            </svg>
                        </div>
                        <div
                            class="faq-answer hidden text-base font-normal leading-6 tracking-[-0.16px] text-[#8D8D8D] pt-0 pb-4 px-4 max-sm:text-sm max-sm:leading-5 max-sm:pt-0 max-sm:pb-3 max-sm:px-3">
                            Lorem ipsum dolor sit amet consectetur. Viverra risus vestibulum libero augue nisi.Lorem
                            ipsum dolor sit amet consectetur. Viverra risus vestibulum libero augue nisi.Lorem ipsum
                            dolor sit amet consectetur. Viverra risus vestibulum libero augue nisi.
                        </div>
                    </div>
                </div>
            </div>
            <script>
            function toggleFaq(element) {
                // Toggle the answer visibility
                const answer = element.nextElementSibling;
                answer.classList.toggle('hidden');

                // Toggle the chevron rotation
                const chevron = element.querySelector('.chevron-icon');
                chevron.classList.toggle('rotate-180');
            }
            </script>
        </div>

    </section>

    <div class="w-full flex items-center justify-center max-w-full py-[72px] pt-[73px] pt-[138px]">
        <section
            class="w-full min-h-[401px] max-w-[1296px] bg-[#001A2C] relative overflow-hidden p-10 rounded-[30px] max-md:p-8 max-sm:p-6">
            <!-- Background SVG -->
            <svg width="1296" height="401" viewBox="0 0 1296 401" fill="none" xmlns="http://www.w3.org/2000/svg"
                class="absolute inset-0 w-full h-full" aria-hidden="true">
                <rect width="1296" height="401" rx="30" fill="#001A2C" />
                <circle cx="52" cy="321" r="26" fill="#0CFFD3" />
                <path
                    d="M548 45.0399C572.142 38.2499 622.238 44.5873 629.481 124.257C602.321 126.521 548 113.846 548 45.0399Z"
                    fill="#FFA318" />
                <mask id="mask0_2049_2102" style="mask-type: alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="1296"
                    height="401">
                    <rect width="1296" height="401" rx="30" fill="#0075FF" />
                </mask>
                <g mask="url(#mask0_2049_2102)"></g>
            </svg>

            <div class="relative z-[1] flex justify-between items-center gap-10 max-md:flex-col max-md:items-start">
                <div class="w-[588px] relative max-md:w-full">
                    <svg id="2049:2126" width="590" height="321" viewBox="0 0 590 321" fill="none"
                        xmlns="http://www.w3.org/2000/svg" class="w-full h-auto">
                        <path
                            d="M40.7362 335.313C40.7362 335.313 24.2141 286.671 48.1906 254.088C84.2481 205.082 164.069 252.559 218.913 210.328C273.757 168.101 375.494 114.051 475.246 165.318C574.998 216.586 558.48 308.19 552.516 335.313H40.7362Z"
                            fill="url(#paint0_linear_2049_2126)" />
                        <path
                            d="M21.9601 57.4503C38.6047 57.9298 97.1373 58.0802 113.103 57.1703C129.069 56.2569 127.403 48.463 118.73 47.5391C110.058 46.6152 113.446 38.8038 102.667 41.0717C91.8912 43.3395 89.2875 43.336 79.3028 32.3119C69.3181 21.2878 51.8406 20.1154 45.4081 32.3119C40.9215 40.8197 38.9861 44.7639 29.8344 41.8486C20.6827 38.9368 15.1461 45.3063 12.4584 48.505C10.0716 51.3468 1.42728 50.3879 1.01081 54.0626C0.590845 57.7408 19.4613 57.3803 21.9601 57.4503Z"
                            fill="url(#paint1_linear_2049_2126)" />
                        <path
                            d="M420.839 335.313C420.839 335.313 423.908 323.39 389.884 303.42C355.86 283.451 347.517 239.932 293.268 240.531C239.022 241.125 222.031 315.046 235.743 335.313H420.839Z"
                            fill="url(#paint2_linear_2049_2126)" />
                        <defs>
                            <linearGradient id="paint0_linear_2049_2126" x1="296.259" y1="144.43" x2="296.259"
                                y2="335.313" gradientUnits="userSpaceOnUse">
                                <stop stop-color="white" />
                                <stop offset="1" stop-color="white" stop-opacity="0" />
                            </linearGradient>
                            <linearGradient id="paint1_linear_2049_2126" x1="1.00157" y1="40.7159" x2="125.213"
                                y2="40.7159" gradientUnits="userSpaceOnUse">
                                <stop offset="0.00398406" stop-color="#D8E3FF" />
                                <stop offset="1" stop-color="#D2DFFF" />
                            </linearGradient>
                            <linearGradient id="paint2_linear_2049_2126" x1="286.142" y1="367.53" x2="358.736"
                                y2="266.148" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#B6CCBD" />
                                <stop offset="1" stop-color="#D1E7D9" />
                            </linearGradient>
                        </defs>
                    </svg>
                </div>
                <div class="flex-1 max-w-[528px] max-md:max-w-full">
                    <h1
                        class="text-[45px] text-white font-bold leading-[58.95px] mb-3.5 max-md:text-4xl max-sm:text-[32px]">
                        Contact
                    </h1>
                    <p class="text-base text-[rgba(255,255,255,0.75)] font-normal leading-[26px] mb-6">
                        Craven omni memoria patriae zombieland clairvius narcisse religionis
                        sunt diri undead historiarum.
                    </p>
                    <form class="flex w-full w-full h-[68px] bg-gray-50 overflow-hidden rounded-[16px] overflow-hidden">
                        <input 
                            type="email" 
                            placeholder="@enter email-address"
                            class="flex-1 text-base text-neutral-medium font-normal px-6 bg-transparent border-none outline-none placeholder:text-neutral-medium shadow-none !h-full"
                            aria-label="Email address" 
                        />
                        <button 
                            type="submit"
                            class="w-[68px] h-full !bg-dark-blue flex items-center justify-center hover:bg-dark-blue transition-colors text-primary-main font-semibold !rounded-none"
                            aria-label="Submit"
                        >
                            <svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M2.70833 13.6282L6.30416 15.2021L16.6667 9.54899L10.4167 16.8407L19.375 20.9198C19.5282 20.9869 19.6953 21.0161 19.8622 21.005C20.0291 20.9939 20.1908 20.9427 20.3338 20.8559C20.4767 20.769 20.5966 20.6491 20.6834 20.5061C20.7702 20.3631 20.8212 20.2013 20.8323 20.0344L21.874 4.40941C21.8856 4.22856 21.8499 4.0478 21.7704 3.88493C21.691 3.72206 21.5705 3.58268 21.4208 3.48051C21.2711 3.37834 21.0974 3.31691 20.9168 3.30225C20.7361 3.28759 20.5548 3.32021 20.3906 3.39691L2.68229 11.7302C2.50165 11.8159 2.34943 11.9517 2.24373 12.1214C2.13803 12.2911 2.08329 12.4876 2.08604 12.6876C2.08878 12.8875 2.14888 13.0824 2.2592 13.2491C2.36952 13.4159 2.52541 13.5474 2.70833 13.6282ZM8.33333 23.0907L13.3083 20.6782L8.33333 18.3573V23.0907Z" fill="white"/>
                            </svg>

                        </button>
                    </form>
                </div>
            </div>
        </section>
    </div>

    <script>
    document.querySelector('form').addEventListener('submit', function(e) {
        e.preventDefault();
        const email = this.querySelector('input[type="email"]').value;
            email
        });
        // Handle form submission
    });
    </script>

    <!-- /. Page content -->
</main><!-- #page -->

<?php
get_footer(); 