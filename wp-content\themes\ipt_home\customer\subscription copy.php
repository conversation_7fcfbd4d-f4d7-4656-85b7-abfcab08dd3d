<?php include get_stylesheet_directory() . '/customer/header-customer.php'; ?>
<?php include get_stylesheet_directory() . '/customer/header-dashboard.php'; ?>
<div class="flex min-h-screen bg-neutral-light">
   <?php include get_stylesheet_directory() . '/customer/sidebar.php'; ?>
   <div class="flex-1 flex flex-col min-h-screen">
      <?php include get_stylesheet_directory() . '/customer/breadcrumb-create-website.php'; ?>

      <!-- Website -->
      <main class="flex-1 p-4 bg-db-dark-2">
         <div class="bg-white rounded-lg shadow p-4">
          
            <!-- Danh sách website sẽ render ở đây -->
            <!-- <div class="text-neutral-medium text-center py-12">No websites found.</div> -->
            <div class="grid grid-cols-1 md:grid-cols-12 gap-6">
               <!-- Cột trái: 4/12 -->
               <div class="md:col-span-4 col-span-12 relative overflow-hidden min-h-[220px] flex flex-col justify-start">
                  <div class="font-semibold text-lg">Your Website</div>
                  <form class="flex flex-col gap-8 mt-2">
                     <!-- Business Name Input -->
                     <div class="flex flex-col gap-2">
                        <label for="business_name" class="text-primary-main text-base font-medium">
                           Company Name
                        </label>
                        <input type="text" 
                           id="business_name" 
                           name="business_name" 
                           placeholder="Company Name"
                           class="w-full !px-[16px] !py-[12px] !h-[48px] rounded-lg !bg-ipt-bg-1
                                    focus:outline-none focus:border-brand-main focus:ring-1 focus:ring-brand-main
                                    placeholder:text-gray-400
                                    transition-all duration-200">
                     </div>

                     <!-- Business Name Input -->
                     <div class="flex flex-col gap-2">
                        <label for="business_name" class="text-primary-main text-base font-medium">
                           Phone
                        </label>
                        <input type="text" 
                           id="business_name" 
                           name="business_name" 
                           placeholder="Phone"
                           class="w-full !px-[16px] !py-[12px] !h-[48px] rounded-lg !bg-ipt-bg-1
                                    focus:outline-none focus:border-brand-main focus:ring-1 focus:ring-brand-main
                                    placeholder:text-gray-400
                                    transition-all duration-200">
                     </div>

                     <!-- Business Description Input -->
                     <div class="flex flex-col gap-2">
                        <label for="business_description" class="text-primary-main text-base font-medium">
                           Address
                        </label>
                        <input type="text" 
                           id="business_description" 
                           name="business_description" 
                           placeholder="Address"
                           class="w-full !px-[16px] !py-[12px] !h-[48px] rounded-lg !bg-ipt-bg-1
                                    focus:outline-none focus:border-brand-main focus:ring-1 focus:ring-brand-main
                                    placeholder:text-gray-400
                                    transition-all duration-200">
                     </div>

                     
                  </form>
               </div>

               <!-- Cột phải: 8/12 -->
               <div class="md:col-span-8 col-span-12 relative rounded-2xl pb-10  bg-white min-h-[220px] flex flex-col justify-start">
                  <div class="font-semibold text-lg">Choose template</div>
                  <div class="flex justify-left mt-2">
                     <form id="search-form" class="items-center flex flex-col md:flex-row gap-5 text-base pl-0  p-[20px] rounded-[8px]" id="search-form">
                        <div class="flex  items-center text-neutral-light font-normal w-full my-auto rounded-[8px]">
                           <div class="!bg-color-disabled-2 h-[48px] flex w-full items-center gap-[8px] px-4 rounded-[8px]">
                              <img
                              src="https://cdn.builder.io/api/v1/image/assets/4f22a764650b4745a83d9de62f28fa3f/122d4a6b1e4e8fb08f0091ab133ed7e594cd60fd"
                              alt="Search"
                              class="aspect-[1] object-contain w-6 shrink-0 my-auto"
                              />
                              <input
                              type="text"
                              placeholder="Search..."
                              class="flex-1 !bg-transparent !shadow-none !border-none !outline-none text-neutral-light placeholder:text-neutral-light w-full"
                              id="search-template"
                              />
                           </div>
                        </div>
                        <button
                        type="submit" id="search-template-button"
                        class="flex h-[48px] items-center justify-center text-black font-semibold w-full md:w-[196px] my-auto rounded-lg !bg-brand-main hover:bg-brand-main active:!bg-brand-main px-5 py-3 hover:opacity-90 transition-opacity"
                        >
                           Search
                        </button>
                        <label class="text-primary-main text-base font-medium">Filter</label>
                        <!-- Industry Dropdown -->
                        <div class="flex flex-col gap-2">
                           <!-- Industry Dropdown Container -->
                           <div class="relative z-30">
                              <select id="industry" 
                                       class="w-full min-w-[220px] !px-[16px] !py-[12px] rounded-lg border border-[#E6E9F5] 
                                          appearance-none bg-white
                                          focus:outline-none focus:border-brand-main focus:ring-1 focus:ring-brand-main
                                          cursor-pointer
                                          transition-all duration-200 !h-[48px] text-16 !bg-ipt-bg-1 select-custom">
                                    <option value="" disabled selected>Select Industry</option>
                                    <!-- <option value="health">Health</option>
                                    <option value="beauty">Beauty</option>
                                    <option value="sports">Sports</option>
                                    <option value="services">Services</option>
                                    <option value="construction">Construction</option>   -->
                              </select>
                              <!-- Arrow Icon -->
                              <!-- <div class="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
                                    <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                       <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                              </div> -->
                              <!-- Custom Dropdown Content -->
                              <div id="industryDropdown" 
                                    class="dropdown-content absolute left-0 right-0 top-full mt-1 hidden
                                          bg-white rounded-lg border border-[#E6E9F5] shadow-lg
                                          max-h-[300px] overflow-y-auto
                                          overflow-hidden z-40">
                                    <!-- Dropdown Items -->
                                    <div class="py-2 bg-white">
                                       <!-- <div class="px-4 py-2 hover:bg-ipt-bg-1 cursor-pointer transition-colors duration-150">
                                          Health
                                       </div>
                                       <div class="px-4 py-2 hover:bg-ipt-bg-1 cursor-pointer transition-colors duration-150">
                                          Beauty
                                       </div>
                                       <div class="px-4 py-2 hover:bg-ipt-bg-1 cursor-pointer transition-colors duration-150">
                                          Sports
                                       </div>
                                       <div class="px-4 py-2 hover:bg-ipt-bg-1 cursor-pointer transition-colors duration-150">
                                          Services
                                       </div>
                                       <div class="px-4 py-2 hover:bg-ipt-bg-1 cursor-pointer transition-colors duration-150">
                                          Construction
                                       </div> -->
                                    </div>
                              </div>
                           </div>
                        </div>
                     </form>
                  </div>
                  <script>
                    jQuery(document).ready(function($) {
                        let debounceTimeout;
                        function fetchTemplates(keyword = "", industry_selected = "") {
                           // Nếu có từ khóa thì tạo filters là mảng, không thì để null
                           const filters = ["status_id:=(2)", "is_kit:=(0)"];
                           const search = keyword ? keyword : "";

                           const query = `
                           query Template_list($filters: [String!], $search: String!) {
                              template_list(body: {filters: $filters, search: $search }) {
                                    totalCount
                                    totalPages
                                    currentPage
                                    data {
                                       id
                                       name
                                       desc
                                       image_id
                                       image {
                                          id
                                          file_name
                                          file_url
                                       }
                                       industries {
                                          id
                                          name
                                       }
                                    }
                              }
                           }`;

                           $.ajax({
                              url: iptHomeAjax.ajax_url,
                              type: 'POST',
                              dataType: 'json',
                              data: {
                                    action: 'ipt_home_graphql',
                                    query: query,
                                    variables: JSON.stringify({ filters: filters, search: search })
                              },
                              success: function(response) {
                                    if(response.data && response.data.template_list) {
                                       renderTemplates(response.data.template_list.data, industry_selected);
                                    }
                              }
                           });
                        }

                        // Get all templates
                        fetchTemplates("");

                        $('#search-template-button').on('click', function(e) {
                           e.preventDefault();
                           const keyword = $('#search-template').val();
                           fetchTemplates(keyword);
                        });

                        $('#search-form').on('submit', function(e) {
                           e.preventDefault();
                           const keyword = $('#search-template').val();
                           fetchTemplates(keyword);
                        });

                        // Render templates
                        function renderTemplates(list, industry_selected = "") {
                           let html = '';
                           list.forEach(function(item) {
                              let industry_name = item.industries[0] ? item.industries[0].name : "";
                              if(industry_selected.length > 0) {
                                    if(industry_selected !== industry_name) {
                                       return;
                                    }
                              }
                              
                              html += `
                                    <article class="flex relative flex-col border bg-white rounded-[8px] overflow-hidden border-solid border-fifth-main h-full">
                                       <div class="absolute top-2 right-2 z-[999] cursor-pointer favorite-toggle">
                                          <svg class="heart-outline" width="22" height="20" viewBox="0 0 22 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                             <path fill-rule="evenodd" clip-rule="evenodd" d="M10.9929 1.71691C8.65298 -0.327979 5.19252 -0.746436 2.50521 1.54965C-0.401577 4.03328 -0.823907 8.21609 1.47471 11.1739C2.34724 12.2967 4.05023 13.9835 5.68684 15.5282C7.34261 17.0911 8.99457 18.5679 9.80923 19.2894L9.82474 19.3031C9.90133 19.371 9.99669 19.4555 10.0881 19.5244C10.1975 19.6068 10.3547 19.7091 10.5645 19.7717C10.8434 19.8549 11.1432 19.8549 11.4221 19.7717C11.6319 19.7091 11.789 19.6068 11.8984 19.5244C11.9898 19.4555 12.0852 19.371 12.1618 19.3031L12.1773 19.2894C12.992 18.5679 14.6439 17.0911 16.2997 15.5282C17.9363 13.9835 19.6393 12.2967 20.5118 11.1739C22.8016 8.22745 22.4445 4.01238 19.4709 1.54088C16.7537 -0.717541 13.3301 -0.328758 10.9929 1.71691ZM10.2333 3.78575C8.52828 1.79238 5.818 1.34975 3.80441 3.07021C1.7011 4.86733 1.41807 7.84171 3.05392 9.9467C3.81701 10.9286 5.40681 12.5137 7.05966 14.0738C8.6027 15.5303 10.1462 16.9145 10.9933 17.6663C11.8403 16.9145 13.3838 15.5303 14.9269 14.0738C16.5797 12.5137 18.1695 10.9286 18.9326 9.9467C20.5773 7.83034 20.3152 4.84319 18.1925 3.07898C16.1256 1.36105 13.4507 1.80119 11.7532 3.78575C11.5632 4.00786 11.2856 4.13573 10.9933 4.13573C10.701 4.13573 10.4233 4.00786 10.2333 3.78575Z" fill="white"/>
                                          </svg>
                                          <svg class="heart-filled hidden" width="22" height="20" viewBox="0 0 22 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                             <path fill-rule="evenodd" clip-rule="evenodd" d="M0 7.48C0 3.97111 2.65774 0 6.88889 0C8.71593 0 10.0661 0.710504 11 1.51082C11.9339 0.710503 13.2841 0 15.1111 0C19.3423 0 22 3.97111 22 7.48C22 9.32544 21.2854 11.0297 20.293 12.5091C19.2998 13.9897 17.9924 15.2999 16.7111 16.3798C15.4261 17.4629 14.1397 18.3372 13.1636 18.9411C12.6749 19.2435 12.2596 19.4807 11.9558 19.6447C11.8047 19.7263 11.6762 19.7924 11.5771 19.8404C11.5289 19.8637 11.4787 19.8871 11.4319 19.9068C11.4098 19.9161 11.3759 19.9299 11.3368 19.9431C11.3177 19.9496 11.2854 19.9601 11.2456 19.9699C11.2202 19.9762 11.1237 20 11 20C10.8763 20 10.7805 19.9763 10.7551 19.9701C10.7153 19.9602 10.6823 19.9496 10.6632 19.9431C10.6241 19.9299 10.5902 19.9161 10.5681 19.9068C10.5213 19.8871 10.4711 19.8637 10.4229 19.8404C10.3238 19.7924 10.1953 19.7263 10.0442 19.6447C9.74037 19.4807 9.32509 19.2435 8.83637 18.9411C7.86027 18.3372 6.57395 17.4629 5.28889 16.3798C4.00758 15.2999 2.70022 13.9897 1.70703 12.5091C0.714641 11.0297 0 9.32544 0 7.48Z" fill="#E54D2E"/>
                                          </svg>
                                       </div>
                                       <div class="relative w-full h-[200px]">
                                          <img src="${item.image ? item.image.file_url : 'https://cdn.builder.io/api/v1/image/assets/TEMP/11ce7b98d60f888e40ee582f5d48055fe0e8fdca'}"
                                                alt="${item.name}" class="w-full h-full object-cover" />
                                          <div class="flex h-[200px] justify-between items-center absolute px-3 py-0 top-0 inset-x-0">
                                                <div class="cursor-pointer">
                                                   <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                                                      class="chevron-left">
                                                      <path
                                                            d="M10.757 12.0001L15.707 16.9501C15.8025 17.0423 15.8787 17.1526 15.9311 17.2747C15.9835 17.3967 16.0111 17.5279 16.0123 17.6607C16.0134 17.7934 15.9881 17.9251 15.9378 18.048C15.8875 18.1709 15.8133 18.2826 15.7194 18.3765C15.6255 18.4703 15.5139 18.5446 15.391 18.5949C15.2681 18.6452 15.1364 18.6705 15.0036 18.6693C14.8708 18.6682 14.7396 18.6406 14.6176 18.5882C14.4956 18.5358 14.3852 18.4596 14.293 18.3641L8.636 12.7071C8.44853 12.5195 8.34322 12.2652 8.34322 12.0001C8.34322 11.7349 8.44853 11.4806 8.636 11.2931L14.293 5.63606C14.4816 5.4539 14.7342 5.35311 14.9964 5.35538C15.2586 5.35766 15.5094 5.46283 15.6948 5.64824C15.8802 5.83365 15.9854 6.08446 15.9877 6.34666C15.99 6.60885 15.8892 6.86146 15.707 7.05006L10.757 12.0001Z"
                                                            fill="white"></path>
                                                   </svg>
                                                </div>
                                                <div class="cursor-pointer">
                                                   <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
                                                      class="chevron-right">
                                                      <path
                                                            d="M13.314 12.071L8.36399 7.12098C8.18184 6.93238 8.08104 6.67978 8.08332 6.41758C8.0856 6.15538 8.19077 5.90457 8.37618 5.71916C8.56158 5.53375 8.8124 5.42859 9.07459 5.42631C9.33679 5.42403 9.58939 5.52482 9.77799 5.70698L15.435 11.364C15.6225 11.5515 15.7278 11.8058 15.7278 12.071C15.7278 12.3361 15.6225 12.5905 15.435 12.778L9.77799 18.435C9.58939 18.6171 9.33679 18.7179 9.07459 18.7157C8.8124 18.7134 8.56158 18.6082 8.37618 18.4228C8.19077 18.2374 8.0856 17.9866 8.08332 17.7244C8.08104 17.4622 8.18184 17.2096 8.36399 17.021L13.314 12.071Z"
                                                            fill="white"></path>
                                                   </svg>
                                                </div>
                                          </div>
                                       </div>
                                       <div class="flex flex-col items-start p-2 gap-[4px] self-stretch flex-1">
                                          <div class="text-secondary-main text-16 font-normal">
                                                ${item.industries[0] ? item.industries[0].name : ''}
                                          </div>
                                          <h3 class="text-secondary-main text-18 font-bold">
                                                ${item.name}
                                          </h3>
                                          
                                       </div>
                                       <div class="flex items-start gap-2 p-2 self-stretch">
                                          <button
                                                class="border border-fourth-main flex-1 text-neutral-strong font-semibold cursor-pointer bg-transparent hover:bg-transparent py-2 rounded-[8px] text-14">
                                                Preview
                                          </button>
                                          <button
                                                class="border border-fourth-main flex-1 text-neutral-strong font-semibold cursor-pointer bg-transparent hover:bg-transparent py-2 rounded-[8px] text-14">
                                                Choose
                                          </button>
                                       </div>
                                    </article>
                              `;
                           });
                           $('#template-list').html(html);
                        }
                        
                        /* Industry list */
                        const industry_filters = [`status_id:=(2);`];

                        const queryIndustry = `
                           query Industry_list($filters: [String!]) {
                              industry_list(body: { filters: $filters }) {
                                    id
                                    name
                                    status_id
                              }
                           }
                        `;

                        $.ajax({
                              url: iptHomeAjax.ajax_url,
                              type: 'POST',
                              dataType: 'json',
                              data: {
                                 action: 'ipt_home_graphql',
                                 query: queryIndustry,
                                 variables: JSON.stringify({ filters: industry_filters })

                              },
                              success: function(response) {
                                 if(response.data && response.data.industry_list) {      
                                    renderIndustries(response.data.industry_list);
                                 } else {
                                    // No data
                                 }
                              },
                              error: function(xhr, status, error) {
                              }
                        });

                        // Hàm render ra HTML
                        function renderIndustries(list) {
                              let html = '';  
                              list.forEach(function(item) {
                                 html += `
                                       <option value="${item.id}">${item.name}</option>
                                 `;      
                              });
                              $('#industry').html(html);

                              let html_dropdown = '';  
                              list.forEach(function(item) {
                                 html_dropdown += `
                                    <div data-industry-id="${item.id}" class="px-4 py-2 hover:bg-ipt-bg-1 cursor-pointer transition-colors duration-150">
                                          ${item.name}
                                    </div>
                                 `;      
                              });
                              $('#industryDropdown').html(html_dropdown);
                        }   

                         // Xử lý sự kiện khi thay đổi giá trị dropdown
                        $('#industry').on('change', function() {
                           const selectedOption = $(this).find('option:selected');
                           const industryName = selectedOption.text();
                           fetchTemplates("", industryName);
                        });
                        
                        // Xử lý sự kiện khi click vào item trong dropdown
                        $(document).on('click', '#industryDropdown div', function() {
                           const industryName = $(this).data('industry-name');
                           const industryId = $(this).data('industry-id');
                           
                           // Cập nhật giá trị cho select
                           $('#industry').val(industryId);
                           
                           // Ẩn dropdown
                           $('#industryDropdown').addClass('hidden');
                           
                           // Gọi lại fetchTemplates với industry đã chọn
                           fetchTemplates("", industryName);
                        });
                        
                        // Hiển thị/ẩn dropdown khi click vào select
                        // $('#industry').on('click', function() {
                        //    $('#industryDropdown').toggleClass('hidden');
                        // });
                        
                        // // Ẩn dropdown khi click ra ngoài
                        // $(document).on('click', function(e) {
                        //    if (!$(e.target).closest('#industry').length && !$(e.target).closest('#industryDropdown').length) {
                        //       $('#industryDropdown').addClass('hidden');
                        //    }
                        // });
                        
                     });
                  </script> 
                  <!-- List templates -->
                  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 w-full gap-[20px] mt-[16px]" id="template-list">

                  </div>
                  <!-- Navigation Buttons -->
                        <div class="flex gap-4 mt-8 justify-end">
                           <button type="button" 
                                    id="continueBtn"
                                    class="flex-1 py-3 px-6 rounded-lg bg-brand-main hover:bg-brand-main/80 max-w-[196px] text-primary-main font-semibold
                                       transition-all duration-200"
                                    >
                              Next
                           </button>
                        </div>

                        <script>
                        jQuery(document).ready(function($) {
                            // Handle favorite icon toggle
                           $(document).on('click', '.favorite-toggle', function() {
                                // Toggle hidden class between the two icons
                                $(this).find('.heart-outline').toggleClass('hidden');
                                $(this).find('.heart-filled').toggleClass('hidden');
         
                            });
                        });
                        </script>
                  </div>
               
            </div>
            
         </div>

         
      </main>

   </div>
</div>

<script>
function toggleMenu(id) {
  const menu = document.getElementById(id);
  menu.classList.toggle('hidden');
}
</script>

<?php include get_stylesheet_directory() . '/customer/footer-customer.php'; ?>
