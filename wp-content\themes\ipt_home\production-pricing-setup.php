<?php
/**
 * Production Domain Pricing Setup Script
 * 
 * This script sets up domain pricing for production deployment
 * Run this once after deploying to production to populate pricing data
 * 
 * Access via: yoursite.com/wp-content/themes/ipt_home/production-pricing-setup.php
 */

// Load WordPress
require_once('../../../../wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

// Include the pricing system
require_once get_stylesheet_directory() . '/includes/domain-pricing-system.php';

echo "<h1>Production Domain Pricing Setup</h1>";
echo "<p><strong>This script should be run once after production deployment.</strong></p>";

// Check if pricing data already exists
$existing_stats = get_domain_pricing_stats();
if ($existing_stats['total_tlds'] > 0) {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0;'>";
    echo "<h3>⚠️ Warning: Pricing Data Already Exists</h3>";
    echo "<p>Database already contains " . $existing_stats['total_tlds'] . " TLDs.</p>";
    echo "<p>Last updated: " . $existing_stats['newest_update'] . "</p>";
    echo "<p><strong>Do you want to continue and overwrite existing data?</strong></p>";
    echo "<form method='post'>";
    echo "<input type='hidden' name='force_update' value='1'>";
    echo "<input type='submit' value='Yes, Overwrite Existing Data' style='background: #dc3545; color: white; padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer;'>";
    echo " <a href='" . admin_url('tools.php?page=domain-pricing') . "' style='margin-left: 10px;'>Cancel</a>";
    echo "</form>";
    echo "</div>";
    
    if (!isset($_POST['force_update'])) {
        exit;
    }
}

echo "<h2>Step 1: Database Setup</h2>";
echo "<p>Creating/checking database table...</p>";
create_domain_pricing_table();
echo "<p>✅ Database table ready</p>";

echo "<h2>Step 2: Loading Pricing Data</h2>";

// Check if the pricing file exists
$file_path = get_stylesheet_directory() . '/md/namecheap_price.md';

if (file_exists($file_path)) {
    echo "<p>✅ Found pricing file: namecheap_price.md</p>";
    echo "<p>Importing from file...</p>";
    
    // Include the import logic (simplified version)
    $content = file_get_contents($file_path);
    preg_match('/\{.*\}/s', $content, $matches);
    
    if (!empty($matches)) {
        $json_data = $matches[0];
        $data = json_decode($json_data, true);
        
        if (json_last_error() === JSON_ERROR_NONE) {
            // Extract register category
            $register_products = null;
            $categories = $data['data']['name_cheap_index']['ApiResponse']['CommandResponse']['UserGetPricingResult']['ProductType']['ProductCategory'];
            
            foreach ($categories as $category) {
                if (isset($category['$']['Name']) && $category['$']['Name'] === 'register') {
                    $register_products = $category['Product'];
                    break;
                }
            }
            
            if ($register_products) {
                $pricing_data = [];
                
                foreach ($register_products as $product) {
                    $tld = $product['$']['Name'];
                    $prices = is_array($product['Price']) ? $product['Price'] : [$product['Price']];
                    
                    foreach ($prices as $price_info) {
                        if (isset($price_info['$']['Duration']) && $price_info['$']['Duration'] == '1') {
                            $price = isset($price_info['$']['YourPrice']) && $price_info['$']['YourPrice'] > 0 
                                ? floatval($price_info['$']['YourPrice']) 
                                : floatval($price_info['$']['Price']);
                            
                            if ($price > 0) {
                                $pricing_data[$tld] = $price;
                            }
                            break;
                        }
                    }
                }
                
                if (!empty($pricing_data)) {
                    $updated_count = update_domain_pricing_in_db($pricing_data);
                    echo "<p>✅ <strong>Successfully imported " . $updated_count . " TLDs from file!</strong></p>";
                } else {
                    echo "<p style='color: red;'>❌ No valid pricing data found in file</p>";
                }
            } else {
                echo "<p style='color: red;'>❌ Could not find register category in pricing data</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ JSON decode error: " . json_last_error_msg() . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ No JSON data found in file</p>";
    }
    
} else {
    echo "<p style='color: orange;'>⚠️ Pricing file not found. Trying API method...</p>";
    
    // Try API method as fallback
    echo "<p>Attempting to fetch from Namecheap API...</p>";
    $pricing_data = fetch_pricing_from_namecheap_api();
    
    if ($pricing_data && !empty($pricing_data)) {
        $updated_count = update_domain_pricing_in_db($pricing_data);
        echo "<p>✅ <strong>Successfully fetched " . $updated_count . " TLDs from API!</strong></p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to fetch from API. Manual setup required.</p>";
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 20px 0;'>";
        echo "<h3>Manual Setup Required</h3>";
        echo "<p>Neither the pricing file nor the API method worked. Please:</p>";
        echo "<ol>";
        echo "<li>Upload the <code>namecheap_price.md</code> file to <code>/wp-content/themes/ipt_home/md/</code></li>";
        echo "<li>Or use the import script: <a href='import-pricing-from-file.php'>import-pricing-from-file.php</a></li>";
        echo "<li>Or manually configure API access</li>";
        echo "</ol>";
        echo "</div>";
    }
}

echo "<h2>Step 3: Setup Automatic Updates</h2>";
echo "<p>Configuring WordPress Cron for automatic pricing updates...</p>";

// Schedule the cron job
if (!wp_next_scheduled('update_domain_pricing_cron')) {
    wp_schedule_event(time(), 'every_six_hours', 'update_domain_pricing_cron');
    echo "<p>✅ Scheduled automatic pricing updates every 6 hours</p>";
} else {
    echo "<p>✅ Automatic updates already scheduled</p>";
}

echo "<h2>Step 4: Final Statistics</h2>";
$final_stats = get_domain_pricing_stats();

if ($final_stats['total_tlds'] > 0) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0;'>";
    echo "<h3>✅ Setup Complete!</h3>";
    echo "<ul>";
    echo "<li><strong>Total TLDs:</strong> " . $final_stats['total_tlds'] . "</li>";
    echo "<li><strong>Average Price:</strong> $" . number_format($final_stats['average_price'], 2) . "</li>";
    echo "<li><strong>Last Updated:</strong> " . $final_stats['newest_update'] . "</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>Test Your Setup:</h3>";
    echo "<ul>";
    echo "<li><a href='" . home_url('/customer/search_domain.php') . "' target='_blank'>Test Domain Search →</a></li>";
    echo "<li><a href='" . admin_url('tools.php?page=domain-pricing') . "' target='_blank'>Domain Pricing Admin →</a></li>";
    echo "</ul>";
    
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 20px 0;'>";
    echo "<h3>❌ Setup Failed</h3>";
    echo "<p>No pricing data was imported. Please check the steps above and try again.</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h2>Security Notice</h2>";
echo "<p style='color: red;'><strong>Important:</strong> Delete this file after successful setup for security reasons.</p>";
echo "<p>You can delete it by removing: <code>/wp-content/themes/ipt_home/production-pricing-setup.php</code></p>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
h1 { color: #333; border-bottom: 2px solid #0073aa; padding-bottom: 10px; }
h2 { color: #666; margin-top: 30px; border-left: 4px solid #0073aa; padding-left: 15px; }
h3 { color: #888; }
p { margin: 10px 0; }
ul, ol { margin: 10px 0 10px 20px; }
li { margin: 5px 0; }
hr { margin: 30px 0; }
a { color: #0073aa; text-decoration: none; }
a:hover { text-decoration: underline; }
code { background: #f1f1f1; padding: 2px 4px; border-radius: 3px; }
</style>
