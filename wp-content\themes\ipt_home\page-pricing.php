<?php
/**
 * Template Name: Pricing page template
 *
 * @package ipt_home
 */

get_header();
?>
<?php 
    // L<PERSON>y sản phẩm với SKU = "free"
    $product_free_id = wc_get_product_id_by_sku('free');
    $product_free = false;

    if ($product_free_id) {
        $product_free = wc_get_product($product_free_id);
    }

    // Lấy sản phẩm với SKU = "premium"
    $product_premium_id = wc_get_product_id_by_sku('premium');
    $product_premium = false;

    if ($product_premium_id) {
        $product_premium = wc_get_product($product_premium_id);
    }

    // Check if current user has trial (for showing "Current Plan" button)
    $user_has_trial = false;
    $user_has_premium = false;
    if (is_user_logged_in()) {
        $current_user_id = get_current_user_id();

        // Get user's orders
        $orders = wc_get_orders(array(
            'customer_id' => $current_user_id,
            'status' => array('completed', 'processing'),
            'limit' => -1
        ));

        foreach ($orders as $order) {
            foreach ($order->get_items() as $item) {
                $product = $item->get_product();
                if ($product) {
                    $sku = $product->get_sku();
                    if ($sku === 'free') {
                        $user_has_trial = true;
                    } elseif ($sku === 'premium') {
                        $user_has_premium = true;
                    }
                }
            }
        }
    }
?>
<main class="w-full">
<!-- Plans -->
<section id="pricing-section-bg" class="flex flex-col items-center gap-[48px] relative overflow-hidden p-20 max-md:px-10 max-md:max-w-full max-sm:px-5 max-sm:py-10 w-full">
    <!-- Pricing Header -->
    <div class="flex flex-col items-center gap-2 w-full max-w-screen-xl max-sm:gap-4">
        <div class="text-seventh-main text-20 font-bold leading-5 tracking-[1px] uppercase max-sm:text-lg">
            PRICING
        </div>
        <div class="text-secondary-main text-42 font-bold leading-[46.2px] text-center max-sm:text-[32px]">
            SUBSCRIPTION PLANS
        </div>
    </div>

    <!-- Pricing Cards Container -->
    <div class="flex w-[848px] items-stretch gap-[80px] max-md:w-full max-md:flex-col max-md:gap-10 max-sm:gap-8" >
        <!-- Starter Card -->
        <div
            class="flex flex-col items-center gap-[32px] flex-[1_0_0] border relative bg-white px-[24px] py-[48px] rounded-lg border-solid border-fifth-main max-md:w-full max-md:max-w-[500px] max-sm:px-5 max-sm:py-8">
            <div class="flex flex-col items-center gap-[8px] self-stretch">
                <div class="text-secondary-main text-center text-[32px] font-bold leading-[35.2px] max-sm:text-[28px]">
                    <?php echo $product_free ? $product_free->get_name() : 'For Starter'; ?>
                </div>
                <div class="text-secondary-main text-center text-18 font-normal">
                     <?php echo $product_free ? $product_free->get_description() : 'Quisque donec nibh diam tellus integer eros.'; ?>
                </div>
            </div>

            <div class="flex flex-col items-center gap-2">
                <div class="text-secondary-main text-center text-[54px] font-bold leading-[59.4px] max-sm:text-[42px]">
                    <?php
                    if ($product_free) {
                        $price_html = $product_free->get_price_html();
                        echo !empty($price_html) ? $price_html : '$0';
                    } else {
                        echo '$0';
                    }
                    ?>
                </div>
            </div>

            <!-- Get started button -->
            <?php if (!is_user_logged_in()) : ?>
                <!-- User not logged in - redirect to login page -->
                <a href="<?php echo get_permalink(41); ?>"
                    class="mx-auto rounded-[8px] text-primary-main hover:!text-primary-main bg-brand-main hover:bg-brand-main/80 focus:bg-brand-main/80 active:bg-brand-main/80 text-16 font-semibold self-stretch cursor-pointer py-[12px] px-[10px] border-[none] w-[129px] inline-block text-center">
                    Get Started
                </a>
            <?php elseif ($user_has_trial) : ?>
                <!-- User already has trial - show Current Plan -->
                <div class="mx-auto rounded-[8px] text-neutral-medium bg-gray-200 text-16 font-semibold self-stretch py-[12px] px-[10px] border-[none] w-[129px] inline-block text-center">
                    Current Plan
                </div>
            <?php elseif ($product_free) : ?>
                <!-- User doesn't have trial - show Get Started -->
                <a href="<?php echo esc_url(add_query_arg(array(
                    'empty-cart' => '1', // Thêm tham số để xóa giỏ hàng
                    'add-to-cart' => $product_free_id,
                    'redirect' => urlencode(site_url('/customer/create_payment/'))
                ), site_url())); ?>"
                    class="mx-auto rounded-[8px] text-primary-main hover:!text-primary-main bg-brand-main hover:bg-brand-main/80 focus:bg-brand-main/80 active:bg-brand-main/80 text-16 font-semibold self-stretch cursor-pointer py-[12px] px-[10px] border-[none] w-[129px] inline-block text-center">
                    Get Started
                </a>
            <?php else : ?>
                <!-- Fallback if no free product -->
                <a href="<?php echo get_permalink(41); ?>"
                    class="mx-auto rounded-[8px] text-primary-main hover:!text-primary-main bg-brand-main hover:bg-brand-main/80 focus:bg-brand-main/80 active:bg-brand-main/80 text-16 font-semibold self-stretch cursor-pointer py-[12px] px-[10px] border-[none] w-[129px] inline-block text-center">
                    Get Started
                </a>
            <?php endif; ?>

            <div class="flex flex-col items-start gap-2">
                 <?php
                // Hiển thị các tính năng từ sản phẩm nếu có
                if ($product_free && $product_free->get_short_description()) {
                    $features = explode("\n", $product_free->get_short_description());
                    foreach ($features as $feature) {
                        if (!empty(trim($feature))) {
                            ?>
                            <!-- Feature 1 -->
                            <div class="flex justify-center items-center gap-2">
                                <div>
                                    <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M10.4862 17.23C10.355 17.2303 10.2249 17.2046 10.1036 17.1545C9.98229 17.1044 9.87205 17.0308 9.77923 16.938L5.53723 12.695C5.44166 12.6028 5.3654 12.4925 5.31291 12.3706C5.26041 12.2486 5.23274 12.1174 5.23149 11.9846C5.23024 11.8519 5.25545 11.7202 5.30565 11.5972C5.35584 11.4743 5.43001 11.3626 5.52384 11.2686C5.61766 11.1747 5.72926 11.1004 5.85212 11.05C5.97498 10.9996 6.10664 10.9742 6.23942 10.9753C6.3722 10.9763 6.50344 11.0038 6.62548 11.0562C6.74752 11.1085 6.85792 11.1846 6.95023 11.28L10.4852 14.815L16.8502 8.45202C17.0377 8.26438 17.2921 8.15891 17.5574 8.15881C17.8226 8.15872 18.0771 8.26401 18.2647 8.45152C18.4524 8.63902 18.5578 8.89339 18.5579 9.15866C18.558 9.42393 18.4527 9.67838 18.2652 9.86602L11.1932 16.938C11.1004 17.0308 10.9902 17.1044 10.8688 17.1545C10.7475 17.2046 10.6175 17.2303 10.4862 17.23Z"
                                            fill="#25A249" />
                                    </svg>
                                </div>
                                <div class="text-secondary-main text-18 font-normal ">
                                     <?php echo trim($feature); ?>
                                </div>
                            </div>
                            <?php
                        }
                    }
                }
                ?>
                <!-- Feature 2 -->
                <!-- <div class="flex justify-center items-center gap-2">
                    <div>
                        <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M10.4862 17.23C10.355 17.2303 10.2249 17.2046 10.1036 17.1545C9.98229 17.1044 9.87205 17.0308 9.77923 16.938L5.53723 12.695C5.44166 12.6028 5.3654 12.4925 5.31291 12.3706C5.26041 12.2486 5.23274 12.1174 5.23149 11.9846C5.23024 11.8519 5.25545 11.7202 5.30565 11.5972C5.35584 11.4743 5.43001 11.3626 5.52384 11.2686C5.61766 11.1747 5.72926 11.1004 5.85212 11.05C5.97498 10.9996 6.10664 10.9742 6.23942 10.9753C6.3722 10.9763 6.50344 11.0038 6.62548 11.0562C6.74752 11.1085 6.85792 11.1846 6.95023 11.28L10.4852 14.815L16.8502 8.45202C17.0377 8.26438 17.2921 8.15891 17.5574 8.15881C17.8226 8.15872 18.0771 8.26401 18.2647 8.45152C18.4524 8.63902 18.5578 8.89339 18.5579 9.15866C18.558 9.42393 18.4527 9.67838 18.2652 9.86602L11.1932 16.938C11.1004 17.0308 10.9902 17.1044 10.8688 17.1545C10.7475 17.2046 10.6175 17.2303 10.4862 17.23Z"
                                fill="#25A249" />
                        </svg>
                    </div>
                    <div class="text-secondary-main text-18 font-normal ">
                        0.5 GB storage
                    </div>
                </div> -->
                <!-- Feature 3 -->
                <!-- <div class="flex justify-center items-center gap-2">
                    <div>
                        <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M10.4862 17.23C10.355 17.2303 10.2249 17.2046 10.1036 17.1545C9.98229 17.1044 9.87205 17.0308 9.77923 16.938L5.53723 12.695C5.44166 12.6028 5.3654 12.4925 5.31291 12.3706C5.26041 12.2486 5.23274 12.1174 5.23149 11.9846C5.23024 11.8519 5.25545 11.7202 5.30565 11.5972C5.35584 11.4743 5.43001 11.3626 5.52384 11.2686C5.61766 11.1747 5.72926 11.1004 5.85212 11.05C5.97498 10.9996 6.10664 10.9742 6.23942 10.9753C6.3722 10.9763 6.50344 11.0038 6.62548 11.0562C6.74752 11.1085 6.85792 11.1846 6.95023 11.28L10.4852 14.815L16.8502 8.45202C17.0377 8.26438 17.2921 8.15891 17.5574 8.15881C17.8226 8.15872 18.0771 8.26401 18.2647 8.45152C18.4524 8.63902 18.5578 8.89339 18.5579 9.15866C18.558 9.42393 18.4527 9.67838 18.2652 9.86602L11.1932 16.938C11.1004 17.0308 10.9902 17.1044 10.8688 17.1545C10.7475 17.2046 10.6175 17.2303 10.4862 17.23Z"
                                fill="#25A249" />
                        </svg>
                    </div>
                    <div class="text-secondary-main text-18 font-normal ">
                        Subdomain
                    </div>
                </div> -->
            </div>
        </div>

        <!-- Premium Card -->
        <div
            class="flex flex-col items-center gap-[32px] flex-[1_0_0] border relative bg-white px-[24px] py-[48px] rounded-lg border-solid border-fifth-main max-md:w-full max-md:max-w-[500px] max-sm:px-5 max-sm:py-8">
            <div
                class="absolute text-secondary-main text-14 font-normal bg-[#F2F4F8] px-[12px] py-[2px] rounded-[12px] left-1/2 -translate-x-1/2 -top-3">
                Most Popular
            </div>
            <div class="flex flex-col items-center gap-[8px] self-stretch">
                <div class="text-secondary-main text-center text-32 font-bold">
                   <?php echo $product_premium ? $product_premium->get_name() : 'For Starter'; ?>
                </div>
                <div class="text-secondary-main text-center text-18 font-normal">
                    <?php echo $product_premium ? $product_premium->get_description() : 'Sed eget purus vulputate suscipit erat.'; ?>
                </div>
            </div>

            <div class="flex flex-col items-center gap-[8px]">
                <!-- <div class="text-remove-text text-center text-34 font-bold line-through">
                    $65/month
                </div>
                <div class="text-secondary-main text-center text-54 font-bold gap-0">
                    <span>$55</span>
                    <span class="text-24">/month</span>
                </div> -->
                <?php if ($product_premium) : ?>
                    <?php if ($product_premium->get_regular_price() && $product_premium->is_on_sale()) : ?>
                        <div class="text-remove-text text-center text-34 font-bold line-through">
                            <?php
                                $regular_price = $product_premium->get_regular_price();
                                echo !empty($regular_price) ? wc_price($regular_price) : '$0';
                            ?>/month
                        </div>
                        <div class="text-secondary-main text-center text-54 font-bold gap-0">
                            <span><?php
                                $sale_price = $product_premium->get_sale_price();
                                echo !empty($sale_price) ? wc_price($sale_price) : '$0';
                            ?></span>
                            <span class="text-24">/month</span>
                        </div>
                    <?php else : ?>
                        <div class="text-secondary-main text-center text-54 font-bold gap-0">
                            <span><?php
                                $price = $product_premium->get_price();
                                echo !empty($price) ? wc_price($price) : '$0';
                            ?></span>
                            <span class="text-24">/month</span>
                        </div>
                    <?php endif; ?>
                <?php else : ?>
                    <div class="text-remove-text text-center text-34 font-bold line-through">
                        $65/month
                    </div>
                    <div class="text-secondary-main text-center text-54 font-bold gap-0">
                        <span>$55</span>
                        <span class="text-24">/month</span>
                    </div>
                <?php endif; ?>
                <!-- <div class="text-secondary-main text-center text-14 font-normal]">
                    $55 USD per month, paid annually
                </div> -->
            </div>

           <!-- Get started button -->
            <?php if (!is_user_logged_in()) : ?>
                <!-- User not logged in - redirect to login page -->
                <a href="<?php echo get_permalink(41); ?>"
                    class="mx-auto rounded-[8px] text-primary-main hover:!text-primary-main bg-brand-main hover:bg-brand-main/80 focus:bg-brand-main/80 active:bg-brand-main/80 text-16 font-semibold self-stretch cursor-pointer py-[12px] px-[10px] border-[none] w-[129px] inline-block text-center">
                    Get Started
                </a>
            <?php elseif ($user_has_premium) : ?>
                <!-- User already has premium - show Current Plan -->
                <div class="mx-auto rounded-[8px] text-neutral-medium bg-gray-200 text-16 font-semibold self-stretch py-[12px] px-[10px] border-[none] w-[129px] inline-block text-center">
                    Current Plan
                </div>
            <?php elseif ($product_premium) : ?>
                <!-- User doesn't have premium - show Get Started -->
                <a href="<?php echo esc_url(add_query_arg(array(
                    'empty-cart' => '1', // Thêm tham số để xóa giỏ hàng
                    'add-to-cart' => $product_premium_id,
                    'redirect' => urlencode(site_url('/customer/create_payment/'))
                ), site_url())); ?>"
                    class="mx-auto rounded-[8px] text-primary-main hover:!text-primary-main bg-brand-main hover:bg-brand-main/80 focus:bg-brand-main/80 active:bg-brand-main/80 text-16 font-semibold self-stretch cursor-pointer py-[12px] px-[10px] border-[none] w-[129px] inline-block text-center">
                    Get Started
                </a>
            <?php else : ?>
                <!-- Fallback if no premium product -->
                <a href="<?php echo get_permalink(41); ?>"
                    class="mx-auto rounded-[8px] text-primary-main hover:!text-primary-main bg-brand-main hover:bg-brand-main/80 focus:bg-brand-main/80 active:bg-brand-main/80 text-16 font-semibold self-stretch cursor-pointer py-[12px] px-[10px] border-[none] w-[129px] inline-block text-center">
                    Get Started
                </a>
            <?php endif; ?>

            <div class="flex flex-col items-start gap-2">
                <!-- Feature 1 -->
                 <?php
                // Hiển thị các tính năng từ sản phẩm nếu có
                if ($product_premium && $product_premium->get_short_description()) {
                    $features = explode("\n", $product_premium->get_short_description());
                    foreach ($features as $feature) {
                        if (!empty(trim($feature))) {
                            ?>
                <div class="flex justify-center items-center gap-2">
                    <div>
                        <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M10.4862 17.23C10.355 17.2303 10.2249 17.2046 10.1036 17.1545C9.98229 17.1044 9.87205 17.0308 9.77923 16.938L5.53723 12.695C5.44166 12.6028 5.3654 12.4925 5.31291 12.3706C5.26041 12.2486 5.23274 12.1174 5.23149 11.9846C5.23024 11.8519 5.25545 11.7202 5.30565 11.5972C5.35584 11.4743 5.43001 11.3626 5.52384 11.2686C5.61766 11.1747 5.72926 11.1004 5.85212 11.05C5.97498 10.9996 6.10664 10.9742 6.23942 10.9753C6.3722 10.9763 6.50344 11.0038 6.62548 11.0562C6.74752 11.1085 6.85792 11.1846 6.95023 11.28L10.4852 14.815L16.8502 8.45202C17.0377 8.26438 17.2921 8.15891 17.5574 8.15881C17.8226 8.15872 18.0771 8.26401 18.2647 8.45152C18.4524 8.63902 18.5578 8.89339 18.5579 9.15866C18.558 9.42393 18.4527 9.67838 18.2652 9.86602L11.1932 16.938C11.1004 17.0308 10.9902 17.1044 10.8688 17.1545C10.7475 17.2046 10.6175 17.2303 10.4862 17.23Z"
                                fill="#25A249" />
                        </svg>
                    </div>
                    <div class="text-secondary-main text-18 font-normal ">
                        <?php echo trim($feature); ?>
                    </div>
                </div>
                <?php 
                        }
                    }
                }
                ?>
            </div>
        </div>
    </div>
</section>
<style>
    #pricing-section-bg {
        background-image: url('<?php echo get_stylesheet_directory_uri(); ?>/assets/img/pricing-bg.png');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
    }
</style>

<!-- Features -->
<?php 
    $data = [
        // ['Features', '3 days Trial', 'Basic ($12)'],
        ['Website features', '-', '-'],
        ['Storage', '0.5 GB', '10 GB'],
        ['Custom domain (?) <br> Website address of your choice e.g: www.BestShoeEver.com', '-', 'Y'],
        ['1 year free domain', '-', 'Y'],
        ['SSL', '-', 'Y'],
        ['AI Assisted content', 'Y', 'Y'],
        ['Mobile optimized', 'Y', 'Y'],
        ['Backups', 'N', 'Every 10 days'],
        ['Ads', 'N', 'N'],
        ['Apps', '-', '-'],
        ['Contact form', 'Y', 'Y'],
        ['Firewall and security scanner', '-', 'Y'],
        ['Support', '-', '-'], 
        ['Customer care', 'N', 'Email (1 day)'],
    ]
?>
<div class="px-[16px] py-[48px] md:p-[80px] md:pt-[160px] bg-bg-light">
    <div class="container mx-auto ">
       
        <!-- Pricing Table with Horizontal Scroll -->
        <div class="overflow-x-auto">
            <div class="w-full mx-auto md:w-[1064px]"> <!-- Minimum width to force horizontal scroll on mobile -->
                <div class="bg-white rounded-[8px] overflow-hidden">
                    <table class="w-full  mb-0 rounded-[8px] overflow-hidden border border-dvd-table" id="plan-comparison-table">
                        <!-- Table Header -->
                        <thead>
                            <tr class="bg-brand-color-01">
                                <th class="py-[16px] md:py-[28px] px-[16px] md:px-[32px] text-left text-16 md:text-24 font-semibold text-eighth-main">Features</th>
                                <th class="py-[16px] md:py-[28px] px-[8px] md:px-[16px] text-center md:text-left text-16 md:text-24 font-semibold text-eighth-main max-sm:min-w-[100px]">
                                    <?php echo $product_free ? $product_free->get_name() : ''; ?>
                                </th>
                                <th class="py-[16px] md:py-[28px] px-[8px] md:px-[16px] text-center md:text-left text-16 md:text-24 font-semibold text-eighth-main max-sm:min-w-[100px]">
                                    <?php echo $product_premium ? $product_premium->get_name() : ''; ?>
                                </th>
                            </tr>
                        </thead>

                        <!-- Table Body -->
                        <tbody class="divide-y divide-dvd-table divide-solid">

                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
 <?php
    $id_subs = [];
    if($product_free) {
        $id_subs[] = $product_free->get_meta('subscription_plan_id');
    }
    if($product_premium) {
        $id_subs[] = $product_premium->get_meta('subscription_plan_id');
    }
?>

<script>
    jQuery(document).ready(function($) {
        const FeatureList = [
            { id: 1, label: 'Storage Limit', type: 'number', unit: 'GB' },
            { id: 2, label: 'Custom Domain Enabled', type: 'checkbox', desc: 'Website address of your choice e.g. www.BestShoeEver.com' },
            { id: 3, label: '1 Year Free Domain Enabled', type: 'checkbox' },
            { id: 4, label: 'SSL Enabled', type: 'checkbox' },
            { id: 5, label: 'AI Assisted Content Enabled', type: 'checkbox' },
            { id: 6, label: 'Mobile Optimized Enabled', type: 'checkbox' },
            { id: 7, label: 'Backup Frequency', type: 'dropdown', options: ['None', 'Daily', 'Weekly', 'Every 10 days'], desc: 'Frequency' },
            { id: 8, label: 'Ads Enabled', type: 'checkbox', desc: 'Show Weaveform Ads' },
            { id: 9, label: 'Contact Form Enabled', type: 'checkbox' },
            { id: 10, label: 'Firewall/Scanner Enabled', type: 'checkbox' },
            { id: 11, label: 'Customer Care Level', type: 'dropdown', options: ['Nil', 'Email (1 day)', 'Priority Support'], desc: 'Support Level' },
        ];

        function featureArrayToMap(features) {
            const map = {};
            features.forEach(f => {
                map[f.id] = f.value;
            });
            return map;
        }

        function renderPlanComparisonTable(plans) {
            // Chuyển mảng features của từng plan thành object map
            const plan1Features = featureArrayToMap(plans[0].features);
            const plan2Features = featureArrayToMap(plans[1].features);

            let html = `
            <table class="w-full mb-0 rounded-[8px] overflow-hidden border border-dvd-table">
                <thead>
                    <tr class="bg-brand-color-01">
                        <th class="py-[16px] md:py-[28px] px-[16px] md:px-[32px] text-left text-16 md:text-24 font-semibold text-eighth-main">Features</th>
                        <th class="py-[16px] md:py-[28px] px-[8px] md:px-[16px] text-center md:text-left text-16 md:text-24 font-semibold text-eighth-main max-sm:min-w-[100px]">
                            ${plans[0].trial_days ? `${plans[0].trial_days} days Trial` : plans[0].name} ${plans[0].price ? `($${plans[0].price})` : ''}
                        </th>
                        <th class="py-[16px] md:py-[28px] px-[8px] md:px-[16px] text-center md:text-left text-16 md:text-24 font-semibold text-eighth-main max-sm:min-w-[100px]">
                            ${plans[1].trial_days ? `${plans[1].trial_days} days Trial` : plans[1].name} ${plans[1].price ? `($${plans[1].price})` : ''}
                        </th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-dvd-table divide-solid">
            `;

            FeatureList.forEach((feature, idx) => {
                const rowClass = idx % 2 === 1 ? 'bg-table-light' : '';
                let desc = feature.desc ? `<br><span class="text-12 text-neutral-medium">${feature.desc}</span>` : '';

                let value1 = plan1Features[feature.id];
                let value2 = plan2Features[feature.id];

                // Xử lý hiển thị cho từng loại
                if (feature.type === 'checkbox') {
                    value1 = value1 === "true" ? 'Y' : 'N';
                    value2 = value2 === "true" ? 'Y' : 'N';
                } else if (feature.type === 'number' && feature.unit) {
                    value1 = value1 ? value1 + ' ' + feature.unit : '-';
                    value2 = value2 ? value2 + ' ' + feature.unit : '-';
                } else if (feature.type === 'dropdown') {
                    value1 = value1 || '-';
                    value2 = value2 || '-';
                } else {
                    value1 = value1 || '-';
                    value2 = value2 || '-';
                }

                html += `
                    <tr class="${rowClass}">
                        <td class="py-[12px] px-[16px] md:px-[32px] text-14 md:text-18 text-eighth-main left-0">
                            ${feature.label}${desc}
                        </td>
                        <td class="py-[20px] text-14 md:text-18 text-eighth-main text-center">${value1}</td>
                        <td class="py-[20px] text-14 md:text-18 text-eighth-main text-center">${value2}</td>
                    </tr>
                `;
            });

            html += `</tbody></table>`;
            $('#plan-comparison-table').html(html);
        }

      
        const planFilters = ["id:[](<?php echo implode(',', $id_subs); ?>)"];
        const queryPlan = `
            query Subscription_plan_list($filters: [String!]) {
                subscription_plan_list(body: { filters: $filters }) {
                    id
                    name
                    desc
                    price
                    features
                    type_id
                    trial_days
                }
            }
        `;

        /* Plan list */
        $.ajax({
            url: iptHomeAjax.ajax_url,
            type: 'POST',
            dataType: 'json',
            data: {
                action: 'ipt_home_graphql',
                query: queryPlan,
                variables: {
                    filters: planFilters
                }
            },
            success: function(response) {
                if(response.data && response.data.subscription_plan_list) {      
                    renderPlanComparisonTable(response.data.subscription_plan_list);
                } else {
                    // No data
                }
            },
            error: function(xhr, status, error) {
            }
        });
    });
</script>
</main><!-- #page -->
<?php
get_footer(); 
