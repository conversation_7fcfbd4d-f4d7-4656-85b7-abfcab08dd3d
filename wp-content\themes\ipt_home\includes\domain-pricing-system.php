<?php
/**
 * Domain Pricing Database System
 * 
 * This system manages domain pricing data in a local WordPress database table
 * for fast lookups and reduced API calls during domain searches.
 * 
 * Features:
 * - Automatic database table creation
 * - Background pricing updates from Namecheap API
 * - Fast local pricing lookups
 * - WordPress Cron integration for scheduled updates
 * - Manual pricing update via AJAX
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Create or update the domain pricing table
 */
function create_domain_pricing_table() {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'domain_pricing';
    
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE $table_name (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        tld varchar(50) NOT NULL,
        price decimal(10,2) NOT NULL,
        currency varchar(3) DEFAULT 'USD',
        last_updated datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        is_active tinyint(1) DEFAULT 1,
        <PERSON>IMAR<PERSON> (id),
        UNIQUE KEY tld (tld),
        <PERSON><PERSON><PERSON> last_updated (last_updated)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
    
    // Log table creation
    error_log('Domain pricing table created/updated: ' . $table_name);
}

/**
 * Initialize table and cron job on theme activation
 */
function init_domain_pricing_system() {
    create_domain_pricing_table();
    
    // Schedule pricing updates every 6 hours
    if (!wp_next_scheduled('update_domain_pricing_cron')) {
        wp_schedule_event(time(), 'sixhourly', 'update_domain_pricing_cron');
    }
}

/**
 * Add custom cron interval for 6 hours
 */
function add_custom_cron_intervals($schedules) {
    $schedules['sixhourly'] = array(
        'interval' => 6 * 60 * 60, // 6 hours in seconds
        'display' => __('Every 6 Hours')
    );
    return $schedules;
}
add_filter('cron_schedules', 'add_custom_cron_intervals');

/**
 * Update pricing data in database
 */
function update_domain_pricing_in_db($pricing_data) {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'domain_pricing';
    $updated_count = 0;
    
    foreach ($pricing_data as $tld => $price) {
        // Clean price (remove $ and convert to decimal)
        $clean_price = floatval(str_replace(['$', ','], '', $price));
        
        if ($clean_price > 0) {
            $result = $wpdb->replace(
                $table_name,
                array(
                    'tld' => $tld,
                    'price' => $clean_price,
                    'currency' => 'USD',
                    'last_updated' => current_time('mysql'),
                    'is_active' => 1
                ),
                array('%s', '%f', '%s', '%s', '%d')
            );
            
            if ($result !== false) {
                $updated_count++;
            }
        }
    }
    
    error_log("Updated pricing for $updated_count TLDs in database");
    return $updated_count;
}

/**
 * Get pricing from database
 */
function get_domain_pricing_from_db($tlds = null) {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'domain_pricing';
    
    if ($tlds && is_array($tlds)) {
        $placeholders = implode(',', array_fill(0, count($tlds), '%s'));
        $query = $wpdb->prepare(
            "SELECT tld, price, currency, last_updated FROM $table_name WHERE tld IN ($placeholders) AND is_active = 1",
            $tlds
        );
    } else {
        $query = "SELECT tld, price, currency, last_updated FROM $table_name WHERE is_active = 1 ORDER BY tld";
    }
    
    $results = $wpdb->get_results($query, ARRAY_A);
    
    $pricing = array();
    foreach ($results as $row) {
        $pricing[$row['tld']] = '$' . number_format($row['price'], 2);
    }
    
    return $pricing;
}

/**
 * Get pricing statistics
 */
function get_domain_pricing_stats() {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'domain_pricing';
    
    $stats = $wpdb->get_row("
        SELECT 
            COUNT(*) as total_tlds,
            MIN(last_updated) as oldest_update,
            MAX(last_updated) as newest_update,
            AVG(price) as average_price
        FROM $table_name 
        WHERE is_active = 1
    ", ARRAY_A);
    
    return $stats;
}

/**
 * Fetch pricing from Namecheap API (server-side)
 */
function fetch_pricing_from_namecheap_api() {
    // GraphQL endpoint - use the same endpoint as domain search
    $graphql_url = 'https://api-weaveform.ip-tribe.com/graphql';
    
    $pricing_query_string = 'Command=namecheap.users.getPricing&ProductType=DOMAIN&ProductCategory=REGISTER';
    
    $query = '
        query Name_cheap_index($queryString: String!) {
            name_cheap_index(query_string: $queryString)
        }
    ';
    
    $variables = array('queryString' => $pricing_query_string);
    
    $response = wp_remote_post($graphql_url, array(
        'headers' => array(
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . IPT_API_KEY
        ),
        'body' => json_encode(array(
            'query' => $query,
            'variables' => $variables
        )),
        'timeout' => 30
    ));
    
    if (is_wp_error($response)) {
        error_log('Pricing API Error: ' . $response->get_error_message());
        return false;
    }

    $status_code = wp_remote_retrieve_response_code($response);
    $body = wp_remote_retrieve_body($response);

    error_log('Pricing API Response Status: ' . $status_code);
    error_log('Pricing API Response Body: ' . substr($body, 0, 500) . '...');

    $data = json_decode($body, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log('JSON decode error: ' . json_last_error_msg());
        return false;
    }

    if (isset($data['errors'])) {
        error_log('GraphQL errors: ' . print_r($data['errors'], true));
        return false;
    }

    if (isset($data['data']['name_cheap_index'])) {
        $pricing_data = parse_pricing_response($data['data']['name_cheap_index']);
        error_log('Successfully parsed ' . count($pricing_data) . ' TLDs from API response');
        return $pricing_data;
    }

    error_log('No pricing data in API response. Full response: ' . print_r($data, true));
    return false;
}

/**
 * Parse pricing response from Namecheap API
 */
function parse_pricing_response($pricing_response) {
    try {
        $api_response = json_decode($pricing_response, true);
        
        if (!$api_response || !isset($api_response['ApiResponse'])) {
            return array();
        }
        
        $response = $api_response['ApiResponse'];
        
        if (!isset($response['CommandResponse']['UserGetPricingResult']['ProductType'])) {
            return array();
        }
        
        $product_types = $response['CommandResponse']['UserGetPricingResult']['ProductType'];
        
        // Handle both single and multiple product types
        if (!isset($product_types[0])) {
            $product_types = array($product_types);
        }
        
        $pricing = array();
        
        foreach ($product_types as $product_type) {
            if (isset($product_type['ProductCategory'])) {
                $categories = $product_type['ProductCategory'];
                
                if (!isset($categories[0])) {
                    $categories = array($categories);
                }
                
                foreach ($categories as $category) {
                    if (isset($category['Product'])) {
                        $products = $category['Product'];
                        
                        if (!isset($products[0])) {
                            $products = array($products);
                        }
                        
                        foreach ($products as $product) {
                            if (isset($product['@attributes']['Name']) && isset($product['Price'])) {
                                $tld = strtolower(str_replace('.', '', $product['@attributes']['Name']));
                                
                                $prices = $product['Price'];
                                if (!isset($prices[0])) {
                                    $prices = array($prices);
                                }
                                
                                // Get 1-year registration price
                                foreach ($prices as $price_info) {
                                    if (isset($price_info['@attributes']['Duration']) && 
                                        $price_info['@attributes']['Duration'] == '1' &&
                                        isset($price_info['@attributes']['Price'])) {
                                        $pricing[$tld] = '$' . number_format(floatval($price_info['@attributes']['Price']), 2);
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        
        error_log('Parsed pricing for ' . count($pricing) . ' TLDs from API');
        return $pricing;
    } catch (Exception $e) {
        error_log('Error parsing pricing response: ' . $e->getMessage());
        return array();
    }
}

/**
 * Cron job to update pricing
 */
function update_domain_pricing_cron_job() {
    error_log('Starting scheduled domain pricing update...');
    
    $pricing_data = fetch_pricing_from_namecheap_api();
    
    if ($pricing_data && !empty($pricing_data)) {
        $updated_count = update_domain_pricing_in_db($pricing_data);
        error_log("Scheduled pricing update completed: $updated_count TLDs updated");
    } else {
        error_log('Scheduled pricing update failed: No data from API');
    }
}
add_action('update_domain_pricing_cron', 'update_domain_pricing_cron_job');

/**
 * AJAX endpoint for manual pricing update
 */
function ajax_update_domain_pricing() {
    // Verify nonce for security
    if (!wp_verify_nonce($_POST['nonce'], 'update_domain_pricing')) {
        wp_die('Security check failed');
    }
    
    // Check user permissions
    if (!current_user_can('manage_options')) {
        wp_die('Insufficient permissions');
    }
    
    // Fetch pricing from Namecheap API
    $pricing_data = fetch_pricing_from_namecheap_api();
    
    if ($pricing_data && !empty($pricing_data)) {
        $updated_count = update_domain_pricing_in_db($pricing_data);
        $stats = get_domain_pricing_stats();
        
        wp_send_json_success(array(
            'message' => "Updated pricing for $updated_count TLDs",
            'count' => $updated_count,
            'timestamp' => current_time('mysql'),
            'stats' => $stats
        ));
    } else {
        wp_send_json_error('Failed to fetch pricing data from API');
    }
}
add_action('wp_ajax_update_domain_pricing', 'ajax_update_domain_pricing');
add_action('wp_ajax_nopriv_update_domain_pricing', 'ajax_update_domain_pricing');

/**
 * Initialize the system
 */
add_action('after_setup_theme', 'init_domain_pricing_system');

/**
 * Include admin page for logged-in admin users
 */
if (is_admin()) {
    $admin_file = get_stylesheet_directory() . '/admin/domain-pricing-admin.php';
    if (file_exists($admin_file)) {
        require_once $admin_file;
    } else {
        error_log('Domain pricing admin file not found: ' . $admin_file);
    }
}

/**
 * Register REST API endpoints for domain pricing
 */
add_action('rest_api_init', 'register_domain_pricing_rest_endpoints');

function register_domain_pricing_rest_endpoints() {
    // Import pricing from file endpoint
    register_rest_route('domain-pricing/v1', '/import-from-file', array(
        'methods' => 'POST',
        'callback' => 'rest_import_pricing_from_file',
        'permission_callback' => 'domain_pricing_permission_check'
    ));

    // Update pricing from API endpoint
    register_rest_route('domain-pricing/v1', '/update-from-api', array(
        'methods' => 'POST',
        'callback' => 'rest_update_pricing_from_api',
        'permission_callback' => 'domain_pricing_permission_check'
    ));

    // Get pricing statistics endpoint
    register_rest_route('domain-pricing/v1', '/stats', array(
        'methods' => 'GET',
        'callback' => 'rest_get_pricing_stats',
        'permission_callback' => 'domain_pricing_permission_check'
    ));

    // Get all pricing data endpoint
    register_rest_route('domain-pricing/v1', '/pricing-data', array(
        'methods' => 'GET',
        'callback' => 'rest_get_pricing_data',
        'permission_callback' => 'domain_pricing_permission_check'
    ));

    // Production setup endpoint
    register_rest_route('domain-pricing/v1', '/production-setup', array(
        'methods' => 'POST',
        'callback' => 'rest_production_setup',
        'permission_callback' => 'domain_pricing_permission_check'
    ));
}

/**
 * Custom permission check for domain pricing API
 * Uses existing IPT_API_KEY constant for system synchronization
 */
function domain_pricing_permission_check($request) {
    // Method 1: Check if user is logged in and has admin privileges
    if (is_user_logged_in() && current_user_can('manage_options')) {
        return true;
    }

    // Method 2: Check for API key in headers (using IPT_API_KEY constant)
    $api_key = $request->get_header('X-API-Key');
    if (!$api_key) {
        // Also check for API key in Authorization header
        $auth_header = $request->get_header('Authorization');
        if ($auth_header && strpos($auth_header, 'Bearer ') === 0) {
            $api_key = substr($auth_header, 7);
        }
    }

    if ($api_key) {
        // Use the existing IPT_API_KEY constant for authentication
        if (defined('IPT_API_KEY') && hash_equals(IPT_API_KEY, $api_key)) {
            return true;
        }
    }

    // Method 3: Check for secret parameter (using IPT_API_KEY as secret too)
    $secret = $request->get_param('secret') ?: $request->get_header('X-Secret');
    if ($secret) {
        // Use IPT_API_KEY as the secret key as well for simplicity
        if (defined('IPT_API_KEY') && hash_equals(IPT_API_KEY, $secret)) {
            return true;
        }
    }

    return new WP_Error(
        'rest_forbidden',
        'You do not have permission to access this endpoint. Use WordPress admin login or IPT_API_KEY.',
        array('status' => 403)
    );
}

/**
 * REST API: Import pricing from file
 */
function rest_import_pricing_from_file($request) {
    try {
        // Read the markdown file
        $file_path = get_stylesheet_directory() . '/md/namecheap_price.md';

        if (!file_exists($file_path)) {
            return new WP_Error('file_not_found', 'Pricing file not found at: ' . $file_path, array('status' => 404));
        }

        $content = file_get_contents($file_path);
        if (!$content) {
            return new WP_Error('file_read_error', 'Could not read file content', array('status' => 500));
        }

        // Extract JSON from the markdown file
        preg_match('/\{.*\}/s', $content, $matches);
        if (empty($matches)) {
            return new WP_Error('no_json_data', 'No JSON data found in file', array('status' => 400));
        }

        $json_data = $matches[0];
        $data = json_decode($json_data, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return new WP_Error('json_decode_error', 'JSON decode failed: ' . json_last_error_msg(), array('status' => 400));
        }

        // Navigate to the register category
        $register_products = null;
        if (isset($data['data']['name_cheap_index']['ApiResponse']['CommandResponse']['UserGetPricingResult']['ProductType']['ProductCategory'])) {
            $categories = $data['data']['name_cheap_index']['ApiResponse']['CommandResponse']['UserGetPricingResult']['ProductType']['ProductCategory'];

            foreach ($categories as $category) {
                if (isset($category['$']['Name']) && $category['$']['Name'] === 'register') {
                    $register_products = $category['Product'];
                    break;
                }
            }
        }

        if (!$register_products) {
            return new WP_Error('no_register_data', 'Could not find register category in pricing data', array('status' => 400));
        }

        // Create database table if it doesn't exist
        create_domain_pricing_table();

        // Parse pricing data
        $pricing_data = [];
        $processed_count = 0;
        $error_count = 0;

        foreach ($register_products as $product) {
            try {
                $tld = $product['$']['Name'];

                // Look for 1-year pricing
                $prices = $product['Price'];
                if (!is_array($prices)) {
                    $prices = [$prices];
                }

                $one_year_price = null;

                foreach ($prices as $price_info) {
                    if (isset($price_info['$']['Duration']) && $price_info['$']['Duration'] == '1') {
                        // Use YourPrice if available, otherwise use Price
                        if (isset($price_info['$']['YourPrice']) && $price_info['$']['YourPrice'] > 0) {
                            $one_year_price = floatval($price_info['$']['YourPrice']);
                        } else {
                            $one_year_price = floatval($price_info['$']['Price']);
                        }
                        break;
                    }
                }

                if ($one_year_price !== null && $one_year_price > 0) {
                    $pricing_data[$tld] = $one_year_price;
                    $processed_count++;
                } else {
                    $error_count++;
                }

            } catch (Exception $e) {
                $error_count++;
            }
        }

        // Import into database
        if (!empty($pricing_data)) {
            $updated_count = update_domain_pricing_in_db($pricing_data);
            $stats = get_domain_pricing_stats();

            return rest_ensure_response(array(
                'success' => true,
                'message' => "Successfully imported pricing for $updated_count TLDs",
                'data' => array(
                    'processed_count' => $processed_count,
                    'error_count' => $error_count,
                    'updated_count' => $updated_count,
                    'stats' => $stats,
                    'sample_pricing' => array_slice($pricing_data, 0, 10, true)
                )
            ));
        } else {
            return new WP_Error('no_pricing_data', 'No valid pricing data found to import', array('status' => 400));
        }

    } catch (Exception $e) {
        return new WP_Error('import_error', 'Import failed: ' . $e->getMessage(), array('status' => 500));
    }
}

/**
 * REST API: Update pricing from API
 */
function rest_update_pricing_from_api($request) {
    try {
        create_domain_pricing_table();

        $pricing_data = fetch_pricing_from_namecheap_api();

        if ($pricing_data && !empty($pricing_data)) {
            $updated_count = update_domain_pricing_in_db($pricing_data);
            $stats = get_domain_pricing_stats();

            return rest_ensure_response(array(
                'success' => true,
                'message' => "Successfully updated pricing for $updated_count TLDs from API",
                'data' => array(
                    'updated_count' => $updated_count,
                    'stats' => $stats,
                    'sample_pricing' => array_slice($pricing_data, 0, 10, true)
                )
            ));
        } else {
            return new WP_Error('api_fetch_failed', 'Failed to fetch pricing data from API', array('status' => 500));
        }

    } catch (Exception $e) {
        return new WP_Error('api_update_error', 'API update failed: ' . $e->getMessage(), array('status' => 500));
    }
}

/**
 * REST API: Get pricing statistics
 */
function rest_get_pricing_stats($request) {
    $stats = get_domain_pricing_stats();

    return rest_ensure_response(array(
        'success' => true,
        'data' => $stats
    ));
}

/**
 * REST API: Get all pricing data
 */
function rest_get_pricing_data($request) {
    $pricing_data = get_domain_pricing_from_db();
    $stats = get_domain_pricing_stats();

    return rest_ensure_response(array(
        'success' => true,
        'data' => array(
            'pricing' => $pricing_data,
            'stats' => $stats,
            'total_tlds' => count($pricing_data)
        )
    ));
}

/**
 * REST API: Production setup
 */
function rest_production_setup($request) {
    try {
        $force_update = $request->get_param('force_update') === 'true';

        // Check if pricing data already exists
        $existing_stats = get_domain_pricing_stats();
        if ($existing_stats['total_tlds'] > 0 && !$force_update) {
            return rest_ensure_response(array(
                'success' => false,
                'message' => 'Pricing data already exists. Use force_update=true to overwrite.',
                'data' => array(
                    'existing_stats' => $existing_stats,
                    'requires_force' => true
                )
            ));
        }

        // Create database table
        create_domain_pricing_table();

        $result = array(
            'database_setup' => true,
            'pricing_import' => false,
            'cron_setup' => false
        );

        // Try to import from file first
        $file_path = get_stylesheet_directory() . '/md/namecheap_price.md';
        if (file_exists($file_path)) {
            $import_result = rest_import_pricing_from_file($request);
            if (!is_wp_error($import_result)) {
                $result['pricing_import'] = true;
                $result['import_method'] = 'file';
                $result['import_data'] = $import_result->data['data'];
            }
        }

        // If file import failed, try API
        if (!$result['pricing_import']) {
            $api_result = rest_update_pricing_from_api($request);
            if (!is_wp_error($api_result)) {
                $result['pricing_import'] = true;
                $result['import_method'] = 'api';
                $result['import_data'] = $api_result->data['data'];
            }
        }

        // Setup cron job
        if (!wp_next_scheduled('update_domain_pricing_cron')) {
            wp_schedule_event(time(), 'every_six_hours', 'update_domain_pricing_cron');
            $result['cron_setup'] = true;
        } else {
            $result['cron_setup'] = 'already_scheduled';
        }

        $final_stats = get_domain_pricing_stats();
        $result['final_stats'] = $final_stats;

        if ($result['pricing_import']) {
            return rest_ensure_response(array(
                'success' => true,
                'message' => 'Production setup completed successfully',
                'data' => $result
            ));
        } else {
            return new WP_Error('setup_failed', 'Production setup failed - could not import pricing data', array('status' => 500));
        }

    } catch (Exception $e) {
        return new WP_Error('setup_error', 'Production setup failed: ' . $e->getMessage(), array('status' => 500));
    }
}



/**
 * Clean up on theme deactivation
 */
function cleanup_domain_pricing_system() {
    wp_clear_scheduled_hook('update_domain_pricing_cron');
}
register_deactivation_hook(__FILE__, 'cleanup_domain_pricing_system');
